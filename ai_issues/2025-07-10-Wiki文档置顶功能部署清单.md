# Wiki文档置顶功能部署清单

**创建时间：** 2025年07月10日  
**功能描述：** Wiki文档置顶功能完整实现  
**优先级：** 高  

## 部署前检查

### 后端检查项

- [ ] **数据库迁移文件**
  - 文件路径：`migrations/2025_07_10_000000_add_pinned_fields_to_wiki_documents_table.php`
  - 验证字段：`is_pinned`, `pinned_at`, `pinned_by`, `pinned_reason`
  - 验证索引：`idx_pinned_sort`, `idx_space_pinned`

- [ ] **模型更新**
  - 文件：`app/Model/TchipBi/WikiDocumentModel.php`
  - 新增属性定义
  - 新增 `pinner()` 关联方法
  - fillable 和 casts 数组更新

- [ ] **控制器接口**
  - 文件：`app/Controller/TchipWiki/TchipWikiController.php`
  - 新增4个置顶相关接口方法
  - 接口注解正确

- [ ] **服务层实现**
  - 文件：`app/Core/Services/TchipWiki/TchipWikiService.php`
  - 置顶业务逻辑实现
  - 权限验证逻辑
  - 查询排序优化

### 前端检查项

- [ ] **API接口文件**
  - 文件：`src/api/biWiki.ts`
  - 4个新增API方法正确定义

- [ ] **文档列表组件**
  - 文件：`src/views/oa/Wiki/components/WikiAllDocument.vue`
  - 置顶图标显示
  - 批量操作选项
  - 置顶对话框集成

- [ ] **树形结构组件**
  - 文件：`src/views/oa/Wiki/components/WikiContent.vue`
  - 右键菜单置顶选项
  - 树形节点置顶图标
  - 置顶操作方法

- [ ] **置顶对话框组件**
  - 文件：`src/views/oa/Wiki/components/dialogs/WikiPinDialog.vue`
  - 新增组件文件存在
  - 批量操作界面完整

## 部署步骤

### 1. 后端部署

```bash
# 进入后端目录
cd tchip_bi_backend

# 执行数据库迁移
php bin/hyperf.php migrate

# 验证迁移结果
php bin/hyperf.php migrate:status

# 清除缓存（如需要）
php bin/hyperf.php cache:clear
```

### 2. 前端部署

```bash
# 进入前端目录
cd tchip_bi_frontend

# 安装依赖（如有新增）
npm install

# 构建项目
npm run build:dev   # 开发环境
npm run build:pre   # 预发布环境
npm run build       # 生产环境

# 部署到服务器
npm run push
```

## 功能验证

### 基础功能测试

- [ ] **单个文档置顶**
  - 在树形结构中右键点击文档
  - 选择"置顶文档"
  - 输入置顶原因
  - 确认置顶成功

- [ ] **单个文档取消置顶**
  - 在已置顶文档上右键
  - 选择"取消置顶"
  - 确认取消成功

- [ ] **批量文档置顶**
  - 在文档列表中选择多个文档
  - 选择"批量置顶"操作
  - 使用置顶对话框完成操作

- [ ] **批量文档取消置顶**
  - 选择已置顶的文档
  - 选择"批量取消置顶"操作
  - 确认操作成功

### 显示效果验证

- [ ] **置顶图标显示**
  - 文档列表中置顶文档显示星形图标
  - 树形结构中置顶文档显示星形图标
  - 图标颜色为橙色 (#ff6b00)

- [ ] **排序效果**
  - 置顶文档在列表顶部显示
  - 置顶文档按置顶时间倒序排列
  - 非置顶文档按原有排序规则显示

- [ ] **详情提示**
  - 鼠标悬浮图标显示置顶时间
  - 显示置顶原因
  - 显示置顶操作人

### 权限验证

- [ ] **管理员权限**
  - 空间管理员可以执行置顶操作
  - 操作按钮正常显示

- [ ] **普通用户权限**
  - 普通用户无法看到置顶操作选项
  - 或显示但操作时提示无权限

- [ ] **跨空间权限**
  - 不同空间的权限正确隔离
  - 操作只影响有权限的空间

## 性能检查

- [ ] **数据库性能**
  - 查询时间在可接受范围内
  - 索引正确使用
  - 无慢查询产生

- [ ] **前端性能**
  - 页面加载流畅
  - 操作响应及时
  - 无明显内存泄漏

## 回滚方案

### 如遇问题需回滚

1. **数据库回滚**
```bash
# 回滚到指定迁移版本
php bin/hyperf.php migrate:rollback --step=1
```

2. **前端回滚**
```bash
# 恢复到上一个版本的构建文件
# 或重新构建之前的代码版本
```

3. **API接口兼容**
   - 新增接口不影响原有功能
   - 可以仅回滚前端，保留后端接口

## 监控要点

### 部署后监控

- [ ] **错误日志监控**
  - 检查后端错误日志
  - 监控前端控制台错误
  - 关注数据库错误

- [ ] **性能监控**
  - 页面加载时间
  - 接口响应时间
  - 数据库查询性能

- [ ] **用户反馈**
  - 收集用户使用反馈
  - 关注功能使用情况
  - 记录改进建议

## 完成标志

- [ ] 所有检查项通过
- [ ] 功能验证完成
- [ ] 性能表现良好
- [ ] 无阻塞性问题
- [ ] 用户可正常使用

---

**负责人：** 开发团队  
**预计完成时间：** 部署当日  
**风险评估：** 低风险（功能独立，不影响现有功能）