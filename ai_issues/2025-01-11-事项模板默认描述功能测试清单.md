# 事项模板默认描述功能测试清单

## 功能概述
为事项模板添加默认描述字段，使新建事项时能够自动填充模板预设的描述内容。

## 实现内容总结

### 1. 后端修改
- ✅ 创建数据库迁移文件：`2025_01_11_120000_add_default_description_to_issue_templates.php`
- ✅ 更新 `IssueTemplateModel.php`：添加 `default_description` 到 `$fillable` 数组
- ✅ 更新 `IssueTemplateService.php`：修改 `getChecklistItemsFromTemplate` 方法返回默认描述数据

### 2. 前端修改
- ✅ 更新 `TemplateDialog.vue`：添加默认描述输入字段
- ✅ 更新 `IssueEditNew.vue`：实现模板应用时自动填充描述功能
- ✅ 添加样式类：`template-dialog-default-description` 和 `issue-edit-new-template-description`

## 测试清单

### 测试环境准备
- [ ] 确保后端数据库迁移已执行
- [ ] 确保前后端代码已部署
- [ ] 准备测试项目和事项类型

### 1. 模板管理功能测试

#### 1.1 创建带默认描述的模板
- [ ] 打开项目管理 → 事项模板管理
- [ ] 点击"新增模板"
- [ ] 填写模板基本信息：
  - [ ] 模板名称：`测试模板_默认描述`
  - [ ] 模板描述：`这是一个测试模板`
  - [ ] **默认描述**：填写多行测试内容，如：
    ```
    ## 事项描述
    
    这是模板预设的默认描述内容。
    
    ### 背景
    - 背景信息1
    - 背景信息2
    
    ### 期望结果
    请描述期望达到的效果。
    ```
- [ ] 添加几个检查项
- [ ] 保存模板
- [ ] **验证**：模板列表中显示新创建的模板

#### 1.2 编辑模板的默认描述
- [ ] 编辑上面创建的模板
- [ ] **验证**：默认描述字段正确显示之前保存的内容
- [ ] 修改默认描述内容
- [ ] 保存修改
- [ ] **验证**：再次编辑时显示修改后的内容

#### 1.3 不设置默认描述的模板
- [ ] 创建新模板，默认描述字段留空
- [ ] 保存模板
- [ ] **验证**：模板正常保存，不影响其他功能

### 2. 事项创建功能测试

#### 2.1 应用有默认描述的模板
- [ ] 创建新事项
- [ ] 选择上面创建的包含默认描述的模板
- [ ] **验证**：
  - [ ] 检查项正确应用
  - [ ] 描述编辑器自动填充了模板的默认描述内容
  - [ ] 内容格式保持正确（Markdown格式）
- [ ] 保存事项
- [ ] **验证**：事项详情页正确显示描述内容

#### 2.2 应用无默认描述的模板
- [ ] 创建新事项
- [ ] 选择没有默认描述的模板
- [ ] **验证**：
  - [ ] 检查项正确应用
  - [ ] 描述编辑器保持为空
  - [ ] 无错误提示

#### 2.3 模板切换测试
- [ ] 创建新事项
- [ ] 先选择无默认描述的模板
- [ ] 再切换到有默认描述的模板
- [ ] **验证**：描述内容正确填充

### 3. 描述内容覆盖测试

#### 3.1 空编辑器应用模板
- [ ] 创建新事项
- [ ] 确保描述编辑器为空
- [ ] 应用有默认描述的模板
- [ ] **验证**：直接填充，无确认提示

#### 3.2 有内容时应用模板
- [ ] 创建新事项
- [ ] 在描述编辑器中输入一些内容
- [ ] 应用有默认描述的模板
- [ ] **验证**：
  - [ ] 显示确认对话框："应用模板将会覆盖当前的描述内容，是否继续？"
  - [ ] 点击"确定"：内容被模板描述替换
  - [ ] 点击"取消"：保持原有内容不变

#### 3.3 编辑器有空白字符时的处理
- [ ] 创建新事项
- [ ] 在描述编辑器中只输入空格或换行符
- [ ] 应用有默认描述的模板
- [ ] **验证**：直接填充，视为空内容处理

### 4. 界面样式测试

#### 4.1 模板对话框样式
- [ ] 打开模板编辑对话框
- [ ] **验证**：
  - [ ] 默认描述字段在模板描述字段下方
  - [ ] 输入框高度为5行
  - [ ] 字符限制显示（最多1000字符）
  - [ ] placeholder 提示文本正确显示
  - [ ] 样式类 `template-dialog-default-description` 生效

#### 4.2 事项编辑页面样式
- [ ] 创建新事项页面
- [ ] **验证**：应用模板后描述编辑器样式正常

### 5. 边界情况测试

#### 5.1 长文本处理
- [ ] 创建模板，在默认描述中输入接近1000字符的内容
- [ ] 保存并应用模板
- [ ] **验证**：内容完整保存和显示

#### 5.2 特殊字符处理
- [ ] 在默认描述中输入特殊字符：`<script>alert('test')</script>`、markdown语法等
- [ ] 保存并应用模板
- [ ] **验证**：内容正确转义，无安全问题

#### 5.3 网络异常处理
- [ ] 模拟网络断开
- [ ] 尝试应用模板
- [ ] **验证**：显示合适的错误提示

### 6. 兼容性测试

#### 6.1 现有模板兼容性
- [ ] 检查系统中已有的模板
- [ ] **验证**：
  - [ ] 编辑现有模板时默认描述字段为空
  - [ ] 应用现有模板功能正常
  - [ ] 不影响原有检查项功能

#### 6.2 权限测试
- [ ] 使用不同权限的用户测试
- [ ] **验证**：
  - [ ] 有权限用户能正常使用功能
  - [ ] 无权限用户看不到模板管理功能

### 7. 性能测试

#### 7.1 大量检查项 + 长描述
- [ ] 创建包含大量检查项和长默认描述的模板
- [ ] 应用模板
- [ ] **验证**：响应时间在可接受范围内

## 测试结果记录

### 发现问题
- [ ] 问题1：描述...
- [ ] 问题2：描述...

### 测试总结
- [ ] 所有核心功能正常
- [ ] 界面显示正确
- [ ] 边界情况处理合理
- [ ] 性能表现良好
- [ ] 兼容性良好

## 部署检查清单

### 数据库
- [ ] 执行迁移：`php bin/hyperf.php migrate`
- [ ] 验证新字段：检查 `issue_templates` 表是否有 `default_description` 字段

### 代码部署
- [ ] 后端代码已更新
- [ ] 前端代码已更新并构建
- [ ] 清除缓存（如有需要）

### 监控
- [ ] 监控应用日志无异常
- [ ] 监控数据库性能无影响

---

**测试完成时间：** ___________

**测试人员：** ___________

**测试结果：** □ 通过 □ 需要修复