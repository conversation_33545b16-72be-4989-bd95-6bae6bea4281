# 知识豆查阅组件后端接口需求

## 任务状态
✅ **已完成** - 2025-01-29

## 需求概述

为知识豆查阅组件提供后端数据支持，支持按季度时间查阅、按部门分类筛选、按人员筛选，并根据知识豆数量进行排序展示。

## 实际实现方案

本需求已经完成实现，实际采用的接口设计与原始需求略有不同，以适应现有系统架构。

## 接口设计

### 1. 知识豆分析数据接口

**接口路径**: `GET /api/points/analysis`

**功能描述**: 获取知识豆分析数据，支持多维度筛选和排序

**请求参数**:
```json
{
  "page": 1,                    // 页码
  "pageSize": 20,               // 每页数量
  "quarter": "2025-1",          // 季度筛选，格式：YYYY-Q
  "departmentIds": [1, 2, 3],   // 部门ID数组
  "userIds": [1, 2, 3],         // 用户ID数组
  "sortBy": "points_desc"       // 排序方式：points_desc, username_asc, department_asc
}
```

**响应数据**:
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "user_id": 1,
        "username": "张三",
        "avatar": "avatar_url",
        "department_id": 1,
        "department_name": "技术部",
        "quarter_points": 1200,       // 季度获得知识豆
        "current_points": 5800,       // 当前知识豆
        "level": 3,
        "level_name": "黄金智者",
        "quarter_rank": 1,            // 季度排名
        "monthly_avg": 400,           // 月均获得知识豆
        "last_points_at": "2025-01-28 15:30:00"
      }
    ],
    "overview": {
      "total_users": 150,             // 总用户数
      "total_points": 180000,         // 总知识豆数
      "avg_points": 1200,             // 平均知识豆
      "active_departments": 8         // 活跃部门数
    },
    "pagination": {
      "total": 150,
      "page": 1,
      "pageSize": 20,
      "totalPages": 8
    }
  }
}
```

### 2. 部门树结构接口

**接口路径**: `GET /api/departments/tree`

**功能描述**: 获取部门树形结构数据

**响应数据**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "技术部",
      "parent_id": 0,
      "children": [
        {
          "id": 11,
          "name": "前端组",
          "parent_id": 1,
          "children": []
        },
        {
          "id": 12,
          "name": "后端组", 
          "parent_id": 1,
          "children": []
        }
      ]
    }
  ]
}
```

### 3. 用户搜索接口

**接口路径**: `GET /api/users/search`

**功能描述**: 根据关键词搜索用户

**请求参数**:
```json
{
  "keyword": "张",              // 搜索关键词
  "limit": 10                  // 返回数量限制
}
```

**响应数据**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "张三",
      "department_name": "技术部"
    }
  ]
}
```

## 数据库设计建议

### 季度积分统计表（建议新增）

```sql
CREATE TABLE `quarter_points_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `year` int(4) NOT NULL COMMENT '年份',
  `quarter` int(1) NOT NULL COMMENT '季度(1-4)',
  `quarter_points` int(11) NOT NULL DEFAULT 0 COMMENT '季度获得积分',
  `quarter_rank` int(11) DEFAULT NULL COMMENT '季度排名',
  `monthly_avg` int(11) DEFAULT 0 COMMENT '月均积分',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_quarter` (`user_id`, `year`, `quarter`),
  KEY `idx_year_quarter` (`year`, `quarter`),
  KEY `idx_quarter_points` (`quarter_points`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='季度积分统计表';
```

## 实现要点

### 1. 季度数据计算

```php
// 根据季度参数计算日期范围
private function getQuarterDateRange(string $quarter): array
{
    [$year, $q] = explode('-', $quarter);
    $startMonth = ($q - 1) * 3 + 1;
    $endMonth = $q * 3;
    
    return [
        'start' => Carbon::createFromDate($year, $startMonth, 1)->startOfMonth(),
        'end' => Carbon::createFromDate($year, $endMonth, 1)->endOfMonth()
    ];
}
```

### 2. 排名计算

```php
// 使用窗口函数计算排名
$query = UserPointsModel::query()
    ->selectSub(
        DB::raw('ROW_NUMBER() OVER (ORDER BY quarter_points DESC)'),
        'quarter_rank'
    );
```

### 3. 部门筛选

```php
// 支持多级部门筛选
if (!empty($departmentIds)) {
    $query->whereHas('user.userDepartmentBind', function ($q) use ($departmentIds) {
        $q->whereIn('department_id', $departmentIds);
    });
}
```

### 4. 性能优化建议

1. **索引优化**: 为季度查询、排序字段添加合适索引
2. **缓存策略**: 季度数据相对固定，可考虑缓存
3. **分页优化**: 使用游标分页提升大数据量查询性能
4. **异步更新**: 季度统计数据可通过定时任务异步计算

## 接口实现优先级

1. **高优先级**: 知识豆分析数据接口（核心功能）
2. **中优先级**: 部门树结构接口（筛选功能）
3. **低优先级**: 用户搜索接口（便利功能）

## 数据完整性检查

1. 确保用户积分数据完整性
2. 验证部门关联关系正确性
3. 检查时间范围计算准确性
4. 排名计算逻辑验证

---

## 实际实现接口

### 已实现接口

1. **GET /api/points/admin/view** - 获取知识豆查阅数据
   - 支持分页查询 (page, pageSize)
   - 支持时间范围筛选 (startDate, endDate, quarterYear, quarter)
   - 支持部门筛选 (departmentIds)
   - 支持用户筛选 (userId)
   - 返回统计信息和用户数据列表

2. **GET /api/points/admin/export** - 导出知识豆查阅报告
   - 支持相同的筛选条件
   - 返回Excel/CSV格式文件

### 使用现有接口

- 部门列表：`/departmentManagement/getList`
- 部门用户：`/departmentManagement/departmentsUsers`

## 实现文件

### 后端文件
- `app/Controller/Points/PointController.php` - 添加查阅接口方法
- `app/Core/Services/Points/PointService.php` - 实现业务逻辑

### 前端文件
- `src/views/setting/pointsManagement/components/PointsViewManagement.vue` - 查阅组件
- `src/views/setting/pointsManagement/index.vue` - 集成查阅tab页
- `src/api/points/index.ts` - API接口定义

## 完成功能

✅ 季度时间筛选功能
✅ 部门多选筛选功能
✅ 人员筛选功能
✅ 数据统计和概览
✅ 分页查询和排序
✅ 数据导出功能
✅ 权限控制（仅管理员可访问）

## 后续优化建议

1. 对大数据量查询实现缓存机制
2. 添加图表展示和趋势分析
3. 支持更多导出格式和异步导出
4. 增加查询条件保存和历史记录功能