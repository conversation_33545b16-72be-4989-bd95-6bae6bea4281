# 检查清单拖拽排序功能

**日期：** 2025-07-09  
**任务：** 为CheckList组件添加拖拽排序功能  

## 任务概述

为 `@tchip_bi_frontend/src/views/project/issue/components/CheckList/CheckList.vue` 文件添加拖拽排序功能，使用户可以通过拖拽来重新排列检查清单项的顺序。

## 实现内容

### 1. 后端修改

#### 1.1 CheckListService.php 新增排序方法

**文件路径：** `/home/<USER>/Project/tchipbi/tchip_bi_backend/app/Core/Services/Project/CheckList/CheckListService.php`

**新增方法：**
```php
/**
 * 更新检查清单项排序
 * @param int $checklistId 检查清单ID
 * @param array $itemIds 排序后的项目ID数组
 * @return bool 是否成功
 */
public function updateChecklistItemsSort(int $checklistId, array $itemIds)
{
    Db::beginTransaction();
    try {
        // 验证检查清单是否存在
        $checklist = $this->checklistModel::query()->find($checklistId);
        if (!$checklist) {
            throw new AppException(StatusCode::ERR_SERVER, __('检查清单不存在'));
        }
        
        // 验证所有项目ID是否属于该检查清单
        $existingItems = $this->checklistItemModel::query()
            ->where('checklist_id', $checklistId)
            ->whereNull('deleted_at')
            ->pluck('id')
            ->toArray();
        
        foreach ($itemIds as $itemId) {
            if (!in_array($itemId, $existingItems)) {
                throw new AppException(StatusCode::ERR_SERVER, __('项目ID不属于该检查清单'));
            }
        }
        
        // 更新排序
        foreach ($itemIds as $index => $itemId) {
            $this->checklistItemModel::query()
                ->where('id', $itemId)
                ->update(['sort_order' => $index + 1]);
        }
        
        Db::commit();
        return true;
        
    } catch (\Exception $e) {
        Db::rollBack();
        throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
    }
}
```

#### 1.2 CheckListController.php 新增排序接口

**文件路径：** `/home/<USER>/Project/tchipbi/tchip_bi_backend/app/Controller/Project/CheckList/CheckListController.php`

**新增方法：**
```php
/**
 * @ControllerNameAnnotation("检查清单-更新项目排序")
 */
public function updateChecklistItemsSort(): ResponseInterface
{
    $checklistId = (int)$this->request->input('checklist_id', 0);
    $itemIds = $this->request->input('item_ids', []);
    
    // 校验参数
    if (empty($checklistId)) {
        return $this->response->error('检查清单ID不能为空', 422);
    }
    
    if (empty($itemIds) || !is_array($itemIds)) {
        return $this->response->error('项目ID数组不能为空', 422);
    }
    
    $result = $this->service->updateChecklistItemsSort($checklistId, $itemIds);
    return $this->response->success($result);
}
```

### 2. 前端修改

#### 2.1 API 接口添加

**文件路径：** `/home/<USER>/Project/tchipbi/tchip_bi_frontend/src/api/projectCheckList.ts`

**新增接口：**
```typescript
/**
 * 更新检查清单项排序
 * @param data 请求参数
 * @returns Promise
 */
export function updateChecklistItemsSort(data: any) {
  return request({
    url: '/project/issue/checklist/updateChecklistItemsSort',
    method: 'post',
    data,
  })
}
```

#### 2.2 CheckList.vue 组件修改

**文件路径：** `/home/<USER>/Project/tchipbi/tchip_bi_frontend/src/views/project/issue/components/CheckList/CheckList.vue`

**主要修改内容：**

1. **引入 Sortable.js 库**
   ```javascript
   import Sortable from 'sortablejs'
   ```

2. **添加拖拽列**
   ```vue
   <!-- 拖拽列 -->
   <el-table-column width="50" align="center">
     <template #default="{ row }">
       <div
         v-if="!row.isNewRow && !row.isAddButtonRow && !localMode"
         class="drag-handle"
         style="cursor: move; display: flex; align-items: center; justify-content: center"
       >
         <vab-icon 
           icon="move" 
           is-custom-svg 
           style="width: 16px; height: 16px; color: #999"
         />
       </div>
     </template>
   </el-table-column>
   ```

3. **拖拽功能实现**
   ```javascript
   // 拖拽相关
   let sortable = null
   
   // 初始化拖拽功能
   const initSortable = () => {
     if (props.localMode) return // 本地模式不支持拖拽
     
     nextTick(() => {
       if (tableRef.value && tableRef.value.$el) {
         const tbody = tableRef.value.$el.querySelector('.el-table__body-wrapper tbody')
         if (tbody) {
           // 如果已经存在sortable实例，先销毁
           if (sortable) {
             sortable.destroy()
             sortable = null
           }
           
           sortable = Sortable.create(tbody, {
             handle: '.drag-handle',
             animation: 150,
             ghostClass: 'sortable-ghost',
             chosenClass: 'sortable-chosen',
             dragClass: 'sortable-drag',
             onEnd: async (event) => {
               const { oldIndex, newIndex } = event
               if (oldIndex !== newIndex) {
                 await handleSortChange(oldIndex, newIndex)
               }
             },
             filter: '.issue-check-list-new-row, .issue-check-list-add-button-row',
           })
         }
       }
     })
   }
   
   // 处理拖拽排序
   const handleSortChange = async (oldIndex, newIndex) => {
     try {
       // 获取真实的数据项（排除新建行和添加按钮行）
       const realData = state.tableData.filter(item => !item.isNewRow && !item.isAddButtonRow)
       
       // 移动数组中的项
       const movedItem = realData.splice(oldIndex, 1)[0]
       realData.splice(newIndex, 0, movedItem)
       
       // 更新本地数据的排序
       state.tableData = realData
       
       // 准备排序后的ID数组
       const sortedIds = realData.map(item => item.id)
       
       // 调用API更新排序
       await updateChecklistItemsSort({
         checklist_id: state.checklistId,
         item_ids: sortedIds,
       })
       
       $baseMessage('排序已更新', 'success', 'vab-hey-message-success')
       
     } catch (error) {
       console.error('更新排序失败', error)
       $baseMessage('排序更新失败', 'error', 'vab-hey-message-error')
       // 失败时重新获取数据
       await fetchData()
     }
   }
   ```

4. **生命周期管理**
   ```javascript
   onMounted(async () => {
     if (props.issueId) {
       await fetchData()
     }
     emitProgressChange()
     initSortable()
   })
   
   onUnmounted(() => {
     // 组件销毁时清理拖拽实例
     if (sortable) {
       sortable.destroy()
       sortable = null
     }
   })
   ```

5. **样式添加**
   ```scss
   // 拖拽相关样式
   .drag-handle {
     &:hover {
       color: #409eff !important;
     }
   }
   
   // sortable.js 拖拽样式
   .sortable-ghost {
     opacity: 0.5;
   }
   
   .sortable-chosen {
     background-color: #e6f7ff !important;
   }
   
   .sortable-drag {
     opacity: 0.8;
   }
   ```

## 技术特点

1. **依赖库检查**：项目已安装 `sortablejs@1.15.2`，无需额外安装
2. **兼容性考虑**：本地模式（localMode）下不启用拖拽功能
3. **错误处理**：包含完整的错误处理机制，失败时自动重新获取数据
4. **性能优化**：自动销毁和重新创建拖拽实例，避免内存泄漏
5. **用户体验**：拖拽时有视觉反馈，支持动画效果

## 功能验证

1. 检查清单项可以通过拖拽图标进行排序
2. 排序后立即保存到后端数据库
3. 拖拽仅在非本地模式下生效
4. 新建行和添加按钮行不参与拖拽
5. 拖拽失败时有错误提示并自动恢复

## 注意事项

1. 该功能仅在非本地模式（localMode=false）下生效
2. 拖拽句柄使用 `vab-icon` 组件的 `move` 图标
3. 排序更新是实时的，无需手动保存
4. 数据库中的 `sort_order` 字段会根据拖拽后的顺序自动更新