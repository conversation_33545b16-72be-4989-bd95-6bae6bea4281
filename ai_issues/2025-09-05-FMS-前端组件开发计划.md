### [计划] FMS 前端组件开发计划（2025-09-05）

作者：qinsx

---

背景：基于后端骨架，前端需实现目录/文件/标签/ACL/分享/回收站/日志各模块，统一组件规范与权限控制。

范围与交付：
- 视图与组件：`src/views/fms` 树（目录、文件、标签、ACL、分享、回收站、日志、通用）
- 规范：$baseMessage/$baseConfirm、firefly-dialog、VabQueryForm、BEM 与文件名关联
- API：`src/api/fms` 七大模块 + Axios 统一封装与类型定义
- 权限：permissionStore + usePermission + v-permission + 路由守卫
- 时间线：6 周分阶段推进，最后联调与验收

风险与对策：
- 权限判定与 UI 同步：集中封装 `usePermission`，按钮级指令兜底
- 对话框一致性：统一 `firefly-dialog`，减少样式与交互偏差
- 错误处理一致性：Axios 拦截 + `$baseMessage` 统一

链接：`/doc/fms/FMS-前端组件开发计划.md`

— 记录人：qinsx


