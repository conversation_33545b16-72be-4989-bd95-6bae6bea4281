# 测试文件管理系统 Docker 部署说明

## 目录映射说明

系统通过 Docker 容器运行，需要正确配置目录映射以确保文件能够正常访问。

### 1. Docker 容器目录映射

在 `tchip_bi_backend/docker_compose/docker-compose.yml` 中配置了以下目录映射：

```yaml
volumes:
  - ../:/var/www                           # 项目代码目录
  - /tmp/test:/data/test-source           # 测试文件源目录
  - /data/test-files:/data/test-files     # 重排后的文件目录
```

**映射关系说明：**
- **宿主机目录** `/tmp/test` -> **容器内目录** `/data/test-source`
  - 这是待处理的测试文件存放位置
  - 原始测试文件应该放在宿主机的 `/tmp/test` 目录下
  
- **宿主机目录** `/data/test-files` -> **容器内目录** `/data/test-files`
  - 这是重排后的文件存储位置
  - 处理完成的文件会按照产品/SN/日期的结构组织

### 2. 环境变量配置

在 `.env` 文件中配置了相关路径：

```bash
# 测试文件管理配置
TEST_FILE_SOURCE_DIR=/data/test-source    # 容器内的源目录路径
TEST_FILE_TARGET_DIR=/data/test-files     # 容器内的目标目录路径
TEST_FILE_BATCH_SIZE=100                  # 单次处理文件数量
TEST_FILE_TASK_TIMEOUT=3600              # 任务超时时间(秒)
TEST_FILE_KEEP_SOURCE=true               # 是否保留源文件
TEST_FILE_AUTO_PARSE=true                # 是否自动解析测试结果
```

### 3. 文件目录结构要求

源文件应按照以下结构组织：
```
/tmp/test/                               # 宿主机源目录
├── 20250115/                          # 日期文件夹
│   ├── AIO-3576-JD4/                  # 产品名称
│   │   ├── SN001/                     # 序列号
│   │   │   ├── 20250115_143000/      # 测试时间
│   │   │   │   ├── AgingTest/        # 老化测试目录
│   │   │   │   │   └── agingTest_result.txt
│   │   │   │   ├── FactoryTest/      # 厂测目录
│   │   │   │   │   └── factoryTest_result.txt
│   │   │   │   └── cpuid.txt         # CPU ID文件
```

重排后的目录结构：
```
/data/test-files/                       # 宿主机目标目录
├── AIO-3576-JD4/                      # 产品名称
│   ├── SN001/                         # 序列号
│   │   ├── 20250115_143000/           # 测试时间
│   │   │   ├── AgingTest/
│   │   │   │   └── agingTest_result.txt
│   │   │   ├── FactoryTest/
│   │   │   │   └── factoryTest_result.txt
│   │   │   └── cpuid.txt
```

## 部署步骤

### 1. 准备宿主机目录

```bash
# 创建必要的目录
sudo mkdir -p /tmp/test
sudo mkdir -p /data/test-files

# 设置权限
sudo chmod 755 /tmp/test
sudo chmod 755 /data/test-files
```

### 2. 启动 Docker 容器

```bash
cd tchip_bi_backend/docker_compose
docker-compose up -d
```

### 3. 执行数据库迁移

```bash
# 进入容器
docker exec -it dev-tchip-bi bash

# 执行迁移
cd /var/www
php bin/hyperf.php migrate
```

### 4. 测试文件处理

1. 将测试文件放置到宿主机的 `/tmp/test` 目录
2. 通过前端界面创建重排任务
3. 系统会自动扫描并处理文件
4. 处理完成的文件会出现在 `/data/test-files` 目录

## 注意事项

1. **权限问题**：确保 Docker 容器有权限读写映射的目录
2. **路径配置**：容器内的路径配置使用 `/data/test-source` 和 `/data/test-files`
3. **文件命名**：源文件必须按照指定的目录结构组织，否则无法正确解析
4. **日志查看**：可以在容器内查看日志文件了解处理详情

## 故障排查

### 1. 文件无法访问
- 检查目录映射是否正确
- 检查文件权限是否正确
- 查看容器日志：`docker logs dev-tchip-bi`

### 2. 任务执行失败
- 检查源目录是否存在文件
- 检查目标目录是否有写入权限
- 查看应用日志：`/var/www/runtime/logs/`

### 3. 数据库连接问题
- 确认数据库服务正常运行
- 检查 `.env` 文件中的数据库配置
- 执行数据库迁移确保表结构正确

## API 测试

可以使用以下命令测试 API：

```bash
# 获取文件列表
curl http://localhost:8057/api/test-file/list

# 创建重排任务
curl -X POST http://localhost:8057/api/test-file/reorganize/create \
  -H "Content-Type: application/json" \
  -d '{
    "source_dir": "/data/test-source",
    "target_dir": "/data/test-files"
  }'

# 获取任务列表
curl http://localhost:8057/api/test-file/reorganize/tasks
```
