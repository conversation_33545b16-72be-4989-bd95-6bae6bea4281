# TestFile目录树厂测老化筛选优化

## 📋 优化概述

**项目名称**: TestFile文件管理系统  
**优化日期**: 2025-09-01  
**优化目标**: 为完整树结构API添加厂测和老化测试的筛选功能  

## 🎯 优化目标

在之前的重构基础上，进一步优化左侧目录树的筛选功能，使其支持：
1. **文件类型筛选**: 支持老化测试、厂测、其他文件类型的筛选
2. **老化测试筛选**: 支持按运行时间范围筛选
3. **厂测筛选**: 支持按测试结果、设备名、固件版本、测试项筛选

## 🔧 技术方案

### 筛选条件传递流程

```
前端筛选条件构造
    ↓
API请求参数
    ↓
Controller参数接收
    ↓
Service业务逻辑处理
    ↓
数据库查询过滤
    ↓
返回过滤后的树结构
```

## 📁 代码变更详情

### 前端变更

#### 1. FileManagerView.vue

**优化buildAllFilters方法**:
```javascript
const buildAllFilters = () => {
    const filter = {}
    const op = {}
    const advancedFilters = {}

    // 基础筛选条件
    if (queryForm.product) {
        filter.product = queryForm.product
        op.product = 'LIKE'
    }

    if (queryForm.sn) {
        filter.sn = queryForm.sn
        op.sn = 'LIKE'
    }

    if (queryForm.dateRange?.length === 2) {
        filter.start_date = queryForm.dateRange[0]
        filter.end_date = queryForm.dateRange[1]
    }
    
    // 高级筛选条件
    if (queryForm.fileType) {
        advancedFilters.file_type = queryForm.fileType
    }
    
    // 老化测试筛选条件
    if (queryForm.fileType === 'aging_test' && hasAgingFilters()) {
        advancedFilters.aging_filters = getActiveAgingFilters()
    }
    
    // 厂测筛选条件
    if (queryForm.fileType === 'factory_test' && hasFactoryFilters()) {
        advancedFilters.factory_filters = getActiveFactoryFilters()
    }

    return { filter, op, advancedFilters }
}
```

**优化initTreeData方法**:
```javascript
// 添加高级筛选条件（文件类型、老化测试、厂测）
if (filters.advancedFilters && Object.keys(filters.advancedFilters).length > 0) {
    // 将高级筛选条件合并到params中
    Object.assign(params, filters.advancedFilters)
}
```

### 后端变更

#### 1. TestFileController.php

**修改getFullTree方法**:
```php
public function getFullTree()
{
    // 使用统一的 getParams 方法获取参数
    list($filter, $op, $sort, $order, $limit) = $this->getParams();
    $search = $this->request->input('search', '');
    
    // 获取额外的筛选参数
    $fileType = $this->request->input('file_type', '');
    $agingFilters = $this->request->input('aging_filters', []);
    $factoryFilters = $this->request->input('factory_filters', []);
    
    try {
        // 调用 service 方法获取完整树结构
        $result = $this->fileService->getFullTreeData(
            $filter,
            $op,
            $search,
            $fileType,
            $agingFilters,
            $factoryFilters
        );
        return $this->response->success($result);
    } catch (\Exception $e) {
        return $this->response->error(StatusCode::ERR_EXCEPTION, $e->getMessage());
    }
}
```

#### 2. FileManagerService.php

**重写getFullTreeData方法**:
- 新增参数：`$fileType`, `$agingFilters`, `$factoryFilters`
- 添加文件类型筛选逻辑
- 添加老化测试筛选逻辑（调用`filterAgingTestFiles`方法）
- 添加厂测筛选逻辑（调用`filterFactoryTestFiles`方法）
- 新增辅助方法：`hasAgingFilters`和`hasFactoryFilters`

**关键筛选逻辑**:
```php
// 文件类型筛选
if (!empty($fileType)) {
    $baseQuery->where('file_type', $fileType);
}

// 老化测试特有筛选条件
if ($fileType === 'aging_test' && !empty($agingFilters)) {
    $fileSyncIds = $this->filterAgingTestFiles($agingFilters);
    if (!empty($fileSyncIds)) {
        $baseQuery->whereIn('id', $fileSyncIds);
    } else if ($this->hasAgingFilters($agingFilters)) {
        // 如果有筛选条件但没有匹配的文件，返回空结果
        return ['tree' => [], 'total' => 0];
    }
}

// 厂测特有筛选条件
if ($fileType === 'factory_test' && !empty($factoryFilters)) {
    $fileSyncIds = $this->filterFactoryTestFiles($factoryFilters);
    if (!empty($fileSyncIds)) {
        $baseQuery->whereIn('id', $fileSyncIds);
    } else if ($this->hasFactoryFilters($factoryFilters)) {
        // 如果有筛选条件但没有匹配的文件，返回空结果
        return ['tree' => [], 'total' => 0];
    }
}
```

## 🚀 功能特性

### 1. 老化测试筛选
- **运行时间范围**：支持设置最小和最大运行时间（小时）
- **精确过滤**：基于`runtime_seconds`字段进行精确计算
- **空结果处理**：当有筛选条件但无匹配结果时，返回空树

### 2. 厂测筛选
- **测试结果**：成功/失败
- **设备名**：模糊匹配
- **固件版本**：模糊匹配
- **测试项**：多选，支持MEMORY、TIME、HDMI_EDID、USB、BLUETOOTH、LED、SATA

### 3. 联动筛选
- 所有筛选条件联动生效
- 筛选后的目录树只显示符合条件的数据路径
- 保持三层树形结构的完整性

## 📊 数据流图

```
用户操作
    ↓
构建筛选条件
    ├── 基础筛选（产品、SN、日期）
    ├── 文件类型筛选
    ├── 老化测试筛选（运行时间）
    └── 厂测筛选（结果、设备、版本、测试项）
         ↓
    API请求参数封装
         ↓
    后端接收并解析
         ↓
    数据库查询优化
         ├── 先筛选测试结果表
         ├── 获取符合条件的file_sync_id
         └── 在file_sync表中应用筛选
              ↓
         构建树形结构
              ↓
         返回过滤后的完整树
```

## 🎨 用户体验优化

### 交互优化
1. **实时筛选**：应用筛选条件后立即更新目录树
2. **筛选状态提示**：显示当前激活的筛选条件数量
3. **重置功能**：一键清除所有筛选条件

### 性能优化
1. **查询优化**：先在测试结果表中筛选，减少file_sync表的查询量
2. **空结果快速返回**：有筛选条件但无匹配时直接返回空树
3. **索引利用**：充分利用数据库索引提升查询性能

## 🧪 测试要点

### 功能测试
1. **单一筛选**：分别测试每个筛选条件
2. **组合筛选**：测试多个条件的组合效果
3. **边界测试**：测试极值条件（如运行时间0小时、999小时）
4. **空结果测试**：验证无匹配数据时的表现

### 性能测试
1. **大数据量**：测试大量文件时的筛选性能
2. **复杂条件**：测试多个条件组合时的响应速度
3. **并发测试**：多用户同时筛选的性能表现

## 🔄 后续优化建议

### 短期优化
1. **筛选条件缓存**：缓存常用的筛选组合结果
2. **预加载优化**：根据用户习惯预加载可能的筛选结果
3. **筛选历史**：记录用户的筛选历史，提供快速选择

### 长期优化
1. **智能推荐**：基于历史数据推荐筛选条件
2. **自定义筛选**：允许用户保存常用筛选组合
3. **批量操作**：支持对筛选结果进行批量操作

## 📝 API文档

### 请求示例
```javascript
POST /api/testfile/tree/full

{
  "filter": {
    "product": "产品A",
    "sn": "SN123",
    "start_date": "2025-01-01",
    "end_date": "2025-12-31"
  },
  "op": {
    "product": "LIKE",
    "sn": "LIKE"
  },
  "file_type": "aging_test",
  "aging_filters": {
    "runtime_hours_min": 24,
    "runtime_hours_max": 168
  },
  "factory_filters": {
    "factory_test_result": "成功",
    "device_name": "设备A",
    "firmware_version": "v1.0",
    "test_items": ["MEMORY", "USB"]
  }
}
```

### 响应示例
```javascript
{
  "success": true,
  "data": {
    "tree": [
      {
        "id": "产品A",
        "label": "产品A",
        "type": "product",
        "children": [
          {
            "id": "产品A/SN123",
            "label": "SN123",
            "type": "sn",
            "children": [
              {
                "id": "产品A/SN123/20250101",
                "label": "20250101",
                "type": "date",
                "children": null,
                "isLeaf": true
              }
            ],
            "isLeaf": false
          }
        ],
        "isLeaf": false
      }
    ],
    "total": 1
  }
}
```

---

**优化完成时间**: 2025-09-01  
**优化负责人**: AI Assistant  
**文档版本**: v1.0
