# 知识豆积分系统详细设计方案

## 1. 系统概述

基于现有TChipBI项目的Wiki知识库系统，设计和实现"知识豆"积分激励系统。该系统旨在通过数字化激励机制，提升员工参与知识分享的积极性。

### 1.1 技术栈
- **后端**: Hyperf 2.2 + PHP 7.4+ + MySQL 8.0 + Redis
- **前端**: Vue 3 + Element Plus + TypeScript + Pinia
- **数据库**: 基于现有数据库架构扩展

### 1.2 核心功能
- 积分获取与消费机制
- 用户等级体系
- 成就系统
- 积分统计与排行榜
- 管理后台

## 2. 数据库设计

### 2.1 用户积分表 (user_points)

```sql
CREATE TABLE `user_points` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `current_points` int(11) NOT NULL DEFAULT 0 COMMENT '当前积分',
  `total_points` int(11) NOT NULL DEFAULT 0 COMMENT '累计积分',
  `consumed_points` int(11) NOT NULL DEFAULT 0 COMMENT '已消费积分',
  `level` tinyint(4) NOT NULL DEFAULT 1 COMMENT '用户等级：1青铜 2白银 3黄金 4钻石 5传奇',
  `level_name` varchar(50) NOT NULL DEFAULT '青铜智者' COMMENT '等级名称',
  `version` int(11) NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  INDEX `idx_current_points` (`current_points`),
  INDEX `idx_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户积分表';
```

### 2.2 积分记录表 (point_records)

```sql
CREATE TABLE `point_records` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `point_type` varchar(50) NOT NULL COMMENT '积分类型：publish_article, get_like, add_essence, delete_article等',
  `point_change` int(11) NOT NULL COMMENT '积分变化量(正数增加，负数减少)',
  `current_points` int(11) NOT NULL COMMENT '变化后积分',
  `source_type` varchar(50) NOT NULL COMMENT '来源类型：wiki_document, wiki_like等',
  `source_id` bigint(20) unsigned DEFAULT NULL COMMENT '来源ID',
  `description` varchar(255) NOT NULL COMMENT '描述信息',
  `remark` text COMMENT '备注',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '操作人',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_point_type` (`point_type`),
  INDEX `idx_source` (`source_type`, `source_id`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分记录表';
```

### 2.3 成就定义表 (achievements)

```sql
CREATE TABLE `achievements` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '成就名称',
  `description` text NOT NULL COMMENT '成就描述',
  `icon` varchar(255) DEFAULT NULL COMMENT '成就图标',
  `badge_image` varchar(255) DEFAULT NULL COMMENT '徽章图片',
  `category` varchar(50) NOT NULL DEFAULT 'general' COMMENT '成就分类：general, article, interaction等',
  `condition_type` varchar(50) NOT NULL COMMENT '条件类型：article_count, like_count, total_views等',
  `condition_value` int(11) NOT NULL COMMENT '条件数值',
  `reward_points` int(11) NOT NULL DEFAULT 0 COMMENT '奖励积分',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_category` (`category`),
  INDEX `idx_condition_type` (`condition_type`),
  INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='成就定义表';
```

### 2.4 用户成就表 (user_achievements)

```sql
CREATE TABLE `user_achievements` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `achievement_id` bigint(20) unsigned NOT NULL COMMENT '成就ID',
  `achieved_at` timestamp NOT NULL COMMENT '获得时间',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_achievement` (`user_id`, `achievement_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_achievement_id` (`achievement_id`),
  INDEX `idx_achieved_at` (`achieved_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户成就表';
```

### 2.5 积分配置表 (point_configs)

```sql
CREATE TABLE `point_configs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `action_type` varchar(50) NOT NULL COMMENT '行为类型',
  `action_name` varchar(100) NOT NULL COMMENT '行为名称',
  `points` int(11) NOT NULL COMMENT '积分值',
  `max_daily_count` int(11) DEFAULT NULL COMMENT '每日最大次数限制',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `description` text COMMENT '描述',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_action_type` (`action_type`),
  INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分配置表';
```

### 2.6 等级配置表 (level_configs)

```sql
CREATE TABLE `level_configs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `level` tinyint(4) NOT NULL COMMENT '等级',
  `level_name` varchar(50) NOT NULL COMMENT '等级名称',
  `min_points` int(11) NOT NULL COMMENT '最低积分要求',
  `max_points` int(11) DEFAULT NULL COMMENT '最高积分(NULL表示无上限)',
  `color` varchar(20) DEFAULT NULL COMMENT '等级颜色',
  `icon` varchar(255) DEFAULT NULL COMMENT '等级图标',
  `privileges` json DEFAULT NULL COMMENT '等级特权',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_level` (`level`),
  INDEX `idx_min_points` (`min_points`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='等级配置表';
```

## 3. 后端API设计

### 3.1 控制器文件结构

```
app/Controller/
├── PointController.php           # 积分管理控制器
├── AchievementController.php     # 成就系统控制器
├── PointRankController.php       # 积分排行榜控制器
└── PointConfigController.php     # 积分配置控制器（管理员）
```

### 3.2 核心服务类

```
app/Core/Services/
├── PointService.php              # 积分服务
├── AchievementService.php        # 成就服务
├── LevelService.php              # 等级服务
└── PointStatisticsService.php    # 积分统计服务
```

### 3.3 主要API接口

#### 3.3.1 积分管理API

```php
// GET /api/points/my - 获取当前用户积分信息
{
    "current_points": 150,
    "total_points": 200,
    "consumed_points": 50,
    "level": 2,
    "level_name": "白银智者",
    "next_level_points": 350
}

// GET /api/points/records - 获取积分记录
{
    "data": [
        {
            "point_type": "publish_article",
            "point_change": 10,
            "description": "发布文档《Vue3最佳实践》",
            "created_at": "2025-07-12 10:00:00"
        }
    ],
    "pagination": {...}
}

// GET /api/points/statistics - 获取积分统计
{
    "today_points": 20,
    "week_points": 80,
    "month_points": 150,
    "rank": 15
}
```

#### 3.3.2 成就系统API

```php
// GET /api/achievements - 获取成就列表
{
    "data": [
        {
            "id": 1,
            "name": "萤火之光",
            "description": "发布第1篇知识文章",
            "is_achieved": true,
            "achieved_at": "2025-07-01 09:00:00",
            "progress": 100
        }
    ]
}

// GET /api/achievements/my - 获取我的成就
// POST /api/achievements/check - 检查成就触发
```

#### 3.3.3 排行榜API

```php
// GET /api/rank/points - 积分排行榜
{
    "data": [
        {
            "rank": 1,
            "user_id": 1,
            "username": "张三",
            "avatar": "avatar.jpg",
            "current_points": 5000,
            "level_name": "传奇智者"
        }
    ]
}

// GET /api/rank/achievements - 成就排行榜
// GET /api/rank/articles - 文章贡献排行榜
```

## 4. 前端设计

### 4.1 页面结构

```
src/views/points/
├── index.vue                     # 积分系统主页
├── my-points/
│   ├── index.vue                # 我的积分页面
│   ├── records.vue              # 积分记录
│   └── statistics.vue           # 积分统计
├── achievements/
│   ├── index.vue                # 成就系统
│   └── my-achievements.vue      # 我的成就
├── rank/
│   ├── index.vue                # 排行榜
│   ├── points.vue               # 积分排行
│   └── achievements.vue         # 成就排行
└── admin/
    ├── config.vue               # 积分配置管理
    ├── levels.vue               # 等级管理
    └── achievements.vue         # 成就管理
```

### 4.2 核心组件

```
src/components/points/
├── PointCard.vue                 # 积分卡片组件
├── LevelBadge.vue               # 等级徽章组件
├── AchievementCard.vue          # 成就卡片组件
├── PointsProgress.vue           # 积分进度条
├── RankList.vue                 # 排行榜列表
└── PointsStatistics.vue         # 积分统计组件
```

### 4.3 状态管理

```typescript
// src/store/modules/points.ts
export interface PointsState {
  userPoints: UserPoints | null
  pointRecords: PointRecord[]
  achievements: Achievement[]
  userAchievements: UserAchievement[]
  pointRankings: PointRanking[]
  achievementRankings: AchievementRanking[]
}
```

## 5. 核心功能实现

### 5.1 积分触发机制

#### 5.1.1 Wiki文档发布积分

```php
// 在TchipWikiController中的store方法添加积分逻辑
public function store(WikiDocumentRequest $request)
{
    $document = $this->wikiService->createDocument($request->all());
    
    // 触发积分奖励
    $this->pointService->addPoints(
        $document->created_by,
        'publish_article',
        'wiki_document',
        $document->id,
        "发布Wiki文档《{$document->title}》"
    );
    
    return $this->success($document);
}
```

#### 5.1.2 点赞积分触发

```php
// 在点赞接口中添加积分逻辑
public function like(Request $request)
{
    $like = $this->wikiService->toggleLike($request->all());
    
    if ($like->wasRecentlyCreated) {
        // 给被点赞的文档作者增加积分
        $document = WikiDocument::find($like->document_id);
        $this->pointService->addPoints(
            $document->created_by,
            'get_like',
            'wiki_like',
            $like->id,
            "文档《{$document->title}》获得点赞"
        );
    }
    
    return $this->success($like);
}
```

### 5.2 成就检查机制

```php
// PointService中的成就检查
public function checkAchievements(int $userId, string $triggerType = null): void
{
    $achievements = Achievement::where('is_active', 1)->get();
    
    foreach ($achievements as $achievement) {
        if ($this->achievementService->checkCondition($userId, $achievement)) {
            $this->achievementService->grantAchievement($userId, $achievement->id);
        }
    }
}
```

### 5.3 等级升级机制

```php
// LevelService中的等级计算
public function updateUserLevel(int $userId): void
{
    $userPoints = UserPoints::where('user_id', $userId)->first();
    $newLevel = LevelConfig::where('min_points', '<=', $userPoints->current_points)
        ->orderBy('level', 'desc')
        ->first();
    
    if ($newLevel && $newLevel->level > $userPoints->level) {
        $userPoints->update([
            'level' => $newLevel->level,
            'level_name' => $newLevel->level_name
        ]);
        
        // 触发等级升级事件
        event(new UserLevelUp($userId, $newLevel->level));
    }
}
```

## 6. 前端页面示例

### 6.1 我的积分页面

```vue
<template>
  <div class="my-points-page">
    <!-- 积分概览卡片 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <PointCard 
          title="当前积分" 
          :value="userPoints.current_points"
          icon="star"
          color="primary"
        />
      </el-col>
      <el-col :span="6">
        <PointCard 
          title="累计积分" 
          :value="userPoints.total_points"
          icon="trophy"
          color="success"
        />
      </el-col>
      <el-col :span="6">
        <LevelBadge 
          :level="userPoints.level"
          :level-name="userPoints.level_name"
          :current-points="userPoints.current_points"
          :next-level-points="nextLevelPoints"
        />
      </el-col>
      <el-col :span="6">
        <AchievementSummary 
          :total="achievements.length"
          :achieved="achievedCount"
        />
      </el-col>
    </el-row>

    <!-- 积分记录 -->
    <el-card class="mt-4">
      <template #header>
        <span>积分记录</span>
      </template>
      <PointRecords :records="pointRecords" />
    </el-card>

    <!-- 成就展示 -->
    <el-card class="mt-4">
      <template #header>
        <span>我的成就</span>
      </template>
      <AchievementGrid :achievements="userAchievements" />
    </el-card>
  </div>
</template>
```

### 6.2 排行榜页面

```vue
<template>
  <div class="rank-page">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="积分排行" name="points">
        <RankList 
          :data="pointRankings"
          type="points"
          :current-user-rank="currentUserRank"
        />
      </el-tab-pane>
      <el-tab-pane label="成就排行" name="achievements">
        <RankList 
          :data="achievementRankings"
          type="achievements"
        />
      </el-tab-pane>
      <el-tab-pane label="文章贡献" name="articles">
        <RankList 
          :data="articleRankings"
          type="articles"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
```

## 7. 数据迁移和初始化

### 7.1 基础数据初始化

```php
// 等级配置初始化
$levels = [
    ['level' => 1, 'level_name' => '青铜智者', 'min_points' => 0, 'max_points' => 99],
    ['level' => 2, 'level_name' => '白银智者', 'min_points' => 100, 'max_points' => 499],
    ['level' => 3, 'level_name' => '黄金智者', 'min_points' => 500, 'max_points' => 999],
    ['level' => 4, 'level_name' => '钻石智者', 'min_points' => 1000, 'max_points' => 4999],
    ['level' => 5, 'level_name' => '传奇智者', 'min_points' => 5000, 'max_points' => null],
];

// 积分配置初始化
$pointConfigs = [
    ['action_type' => 'publish_article', 'action_name' => '发布知识文档', 'points' => 10],
    ['action_type' => 'get_like', 'action_name' => '文档获得点赞', 'points' => 1],
    ['action_type' => 'add_essence', 'action_name' => '文档被加精', 'points' => 50],
    ['action_type' => 'delete_article', 'action_name' => '文档被删除', 'points' => -10],
    ['action_type' => 'offline_training', 'action_name' => '线下培训', 'points' => 300],
];

// 成就配置初始化
$achievements = [
    ['name' => '萤火之光', 'description' => '发布第1篇知识文章', 'condition_type' => 'article_count', 'condition_value' => 1],
    ['name' => '知识分子', 'description' => '累计发布20个知识文章', 'condition_type' => 'article_count', 'condition_value' => 20],
    ['name' => '精华达人', 'description' => '累计10个知识文章被加精', 'condition_type' => 'essence_count', 'condition_value' => 10],
    ['name' => '万众瞩目', 'description' => '所发布的知识文章总阅读量达100,000', 'condition_type' => 'total_views', 'condition_value' => 100000],
    ['name' => '智慧之星', 'description' => '知识文章累计被点赞超过1000次', 'condition_type' => 'like_count', 'condition_value' => 1000],
];
```

### 7.2 历史数据处理

```php
// 为现有用户初始化积分
public function initExistingUsersPoints(): void
{
    $users = User::all();
    
    foreach ($users as $user) {
        // 计算历史积分
        $articleCount = WikiDocument::where('created_by', $user->id)->count();
        $totalLikes = WikiDocument::where('created_by', $user->id)->sum('like_count');
        
        $totalPoints = $articleCount * 10 + $totalLikes * 1;
        
        UserPoints::create([
            'user_id' => $user->id,
            'current_points' => $totalPoints,
            'total_points' => $totalPoints,
            'consumed_points' => 0,
            'level' => $this->levelService->calculateLevel($totalPoints),
            'level_name' => $this->levelService->getLevelName($totalPoints),
        ]);
    }
}
```

## 8. 部署和测试

### 8.1 部署步骤

1. **数据库迁移**
   ```bash
   php bin/hyperf.php migrate
   ```

2. **数据初始化**
   ```bash
   php bin/hyperf.php points:init
   ```

3. **前端构建**
   ```bash
   npm run build:dev
   ```

4. **清除缓存**
   ```bash
   php bin/hyperf.php cache:clear
   ```

### 8.2 测试计划

1. **单元测试**: 积分计算、等级升级、成就检查逻辑
2. **集成测试**: API接口测试、数据库操作测试
3. **功能测试**: 前端页面交互测试
4. **性能测试**: 大数据量下的积分计算性能
5. **安全测试**: 积分作弊防护测试

## 9. 运维和监控

### 9.1 监控指标

- 积分系统API响应时间
- 积分计算错误率
- 用户参与度统计
- 系统性能指标

### 9.2 日志记录

- 积分变动日志
- 成就获得日志
- 等级升级日志
- 异常操作日志

## 10. 扩展功能

### 10.1 积分商城
- 积分兑换礼品
- 虚拟商品购买
- 特权购买功能

### 10.2 团队协作
- 部门积分排行
- 团队成就系统
- 协作奖励机制

### 10.3 数据分析
- 用户行为分析
- 积分趋势分析
- 知识贡献报告

---

此设计方案基于现有TChipBI系统架构，充分利用已有的Wiki功能和用户体系，实现了完整的积分激励系统。系统设计遵循可扩展性、高性能和易维护的原则，为企业知识管理提供有效的激励机制。