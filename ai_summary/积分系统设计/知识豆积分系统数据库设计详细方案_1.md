# 知识豆积分系统数据库设计详细方案

## 🎯 设计目标

基于现有TChipBI系统，设计完整的"知识豆"积分系统数据库架构，支持多样化的积分获取方式、完善的等级体系、丰富的成就系统，为用户提供持续的激励机制。

## 📊 数据库表详细设计

### 1. 用户积分表 (user_points)

```sql
CREATE TABLE `user_points` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID，关联users表',
  `current_points` int(11) NOT NULL DEFAULT 0 COMMENT '当前可用积分',
  `total_points` int(11) NOT NULL DEFAULT 0 COMMENT '累计获得积分',
  `consumed_points` int(11) NOT NULL DEFAULT 0 COMMENT '已消费积分',
  `level` tinyint(4) NOT NULL DEFAULT 1 COMMENT '用户等级：1青铜 2白银 3黄金 4钻石 5传奇',
  `level_name` varchar(50) NOT NULL DEFAULT '青铜智者' COMMENT '等级名称',
  `last_points_at` timestamp NULL DEFAULT NULL COMMENT '最后获得积分时间',
  `level_updated_at` timestamp NULL DEFAULT NULL COMMENT '等级更新时间',
  `version` int(11) NOT NULL DEFAULT 1 COMMENT '乐观锁版本号，防止并发问题',
  `daily_points` int(11) NOT NULL DEFAULT 0 COMMENT '今日获得积分',
  `weekly_points` int(11) NOT NULL DEFAULT 0 COMMENT '本周获得积分', 
  `monthly_points` int(11) NOT NULL DEFAULT 0 COMMENT '本月获得积分',
  `year_points` int(11) NOT NULL DEFAULT 0 COMMENT '本年获得积分',
  `last_daily_reset` date DEFAULT NULL COMMENT '日积分最后重置日期',
  `last_weekly_reset` date DEFAULT NULL COMMENT '周积分最后重置日期',
  `last_monthly_reset` date DEFAULT NULL COMMENT '月积分最后重置日期',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_current_points` (`current_points`),
  KEY `idx_total_points` (`total_points`),
  KEY `idx_level` (`level`),
  KEY `idx_daily_points` (`daily_points`),
  KEY `idx_last_points_at` (`last_points_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户积分主表';
```

### 2. 积分记录表 (point_records)

```sql
CREATE TABLE `point_records` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `point_type` varchar(50) NOT NULL COMMENT '积分类型：publish_article, get_like, add_essence, delete_article, offline_training, special_reward等',
  `point_change` int(11) NOT NULL COMMENT '积分变化量(正数增加，负数减少)',
  `current_points` int(11) NOT NULL COMMENT '变化后的当前积分',
  `source_type` varchar(50) NOT NULL COMMENT '来源类型：wiki_document, wiki_like, wiki_comment, training, manual等',
  `source_id` bigint(20) unsigned DEFAULT NULL COMMENT '来源对象ID',
  `source_title` varchar(255) DEFAULT NULL COMMENT '来源对象标题',
  `description` varchar(500) NOT NULL COMMENT '积分变化描述',
  `remark` text COMMENT '管理员备注(手动操作时)',
  `multiplier` decimal(3,2) DEFAULT 1.00 COMMENT '积分倍数(活动期间可能有倍数奖励)',
  `bonus_reason` varchar(255) DEFAULT NULL COMMENT '额外奖励原因',
  `is_daily_first` tinyint(1) DEFAULT 0 COMMENT '是否为当日首次该类型操作',
  `is_reversed` tinyint(1) DEFAULT 0 COMMENT '是否已被撤销',
  `reversed_at` timestamp NULL DEFAULT NULL COMMENT '撤销时间',
  `reversed_reason` varchar(255) DEFAULT NULL COMMENT '撤销原因',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '操作人(系统自动为NULL)',
  `ip_address` varchar(45) DEFAULT NULL COMMENT '操作IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_point_type` (`point_type`),
  KEY `idx_source` (`source_type`, `source_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_point_change` (`point_change`),
  KEY `idx_is_reversed` (`is_reversed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分变动记录表';
```

### 3. 积分配置表 (point_configs)

```sql
CREATE TABLE `point_configs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `action_type` varchar(50) NOT NULL COMMENT '行为类型标识',
  `action_name` varchar(100) NOT NULL COMMENT '行为名称',
  `points` int(11) NOT NULL COMMENT '基础积分值',
  `max_daily_count` int(11) DEFAULT NULL COMMENT '每日最大获得次数(NULL表示无限制)',
  `max_daily_points` int(11) DEFAULT NULL COMMENT '每日最大积分(NULL表示无限制)',
  `first_time_bonus` int(11) DEFAULT 0 COMMENT '首次操作额外奖励',
  `consecutive_bonus` json DEFAULT NULL COMMENT '连续操作奖励配置',
  `quality_multiplier` json DEFAULT NULL COMMENT '质量倍数配置',
  `time_decay` json DEFAULT NULL COMMENT '时间衰减配置',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `category` varchar(50) DEFAULT 'general' COMMENT '分类：content, interaction, training, special',
  `priority` int(11) DEFAULT 0 COMMENT '优先级(用于排序显示)',
  `description` text COMMENT '详细说明',
  `conditions` json DEFAULT NULL COMMENT '触发条件配置',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_action_type` (`action_type`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_category` (`category`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分规则配置表';
```

### 4. 等级配置表 (level_configs)

```sql
CREATE TABLE `level_configs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `level` tinyint(4) NOT NULL COMMENT '等级数值',
  `level_name` varchar(50) NOT NULL COMMENT '等级名称',
  `level_title` varchar(100) DEFAULT NULL COMMENT '等级称号',
  `min_points` int(11) NOT NULL COMMENT '最低积分要求',
  `max_points` int(11) DEFAULT NULL COMMENT '最高积分(NULL表示无上限)',
  `color` varchar(20) DEFAULT NULL COMMENT '等级颜色(十六进制)',
  `icon` varchar(255) DEFAULT NULL COMMENT '等级图标URL',
  `badge_image` varchar(255) DEFAULT NULL COMMENT '等级徽章图片URL',
  `privileges` json DEFAULT NULL COMMENT '等级特权配置',
  `upgrade_reward` int(11) DEFAULT 0 COMMENT '升级奖励积分',
  `description` text COMMENT '等级描述',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_level` (`level`),
  KEY `idx_min_points` (`min_points`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户等级配置表';
```

### 5. 成就定义表 (achievements)

```sql
CREATE TABLE `achievements` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `code` varchar(50) NOT NULL COMMENT '成就代码(唯一标识)',
  `name` varchar(100) NOT NULL COMMENT '成就名称',
  `description` text NOT NULL COMMENT '成就描述',
  `icon` varchar(255) DEFAULT NULL COMMENT '成就图标URL',
  `badge_image` varchar(255) DEFAULT NULL COMMENT '成就徽章图片URL',
  `category` varchar(50) NOT NULL DEFAULT 'general' COMMENT '成就分类：article, interaction, training, special, milestone',
  `difficulty` tinyint(4) DEFAULT 1 COMMENT '难度等级：1简单 2普通 3困难 4史诗 5传说',
  `condition_type` varchar(50) NOT NULL COMMENT '条件类型：article_count, like_count, total_views, consecutive_days等',
  `condition_value` int(11) NOT NULL COMMENT '条件数值',
  `condition_config` json DEFAULT NULL COMMENT '复杂条件配置',
  `reward_points` int(11) NOT NULL DEFAULT 0 COMMENT '奖励积分',
  `reward_items` json DEFAULT NULL COMMENT '奖励物品配置',
  `is_hidden` tinyint(1) DEFAULT 0 COMMENT '是否隐藏成就(用户看不到)',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `is_repeatable` tinyint(1) DEFAULT 0 COMMENT '是否可重复获得',
  `display_order` int(11) DEFAULT 0 COMMENT '显示顺序',
  `unlock_level` tinyint(4) DEFAULT 1 COMMENT '解锁等级要求',
  `prerequisite_achievements` json DEFAULT NULL COMMENT '前置成就要求',
  `valid_from` timestamp NULL DEFAULT NULL COMMENT '生效开始时间',
  `valid_to` timestamp NULL DEFAULT NULL COMMENT '生效结束时间',
  `tips` varchar(500) DEFAULT NULL COMMENT '获得提示',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_category` (`category`),
  KEY `idx_difficulty` (`difficulty`),
  KEY `idx_condition_type` (`condition_type`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_is_hidden` (`is_hidden`),
  KEY `idx_display_order` (`display_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='成就定义表';
```

### 6. 用户成就表 (user_achievements)

```sql
CREATE TABLE `user_achievements` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `achievement_id` bigint(20) unsigned NOT NULL COMMENT '成就ID',
  `achievement_code` varchar(50) NOT NULL COMMENT '成就代码(冗余字段，便于查询)',
  `progress` int(11) DEFAULT 0 COMMENT '当前进度值',
  `target_value` int(11) NOT NULL COMMENT '目标值',
  `progress_percentage` decimal(5,2) DEFAULT 0.00 COMMENT '完成百分比',
  `is_completed` tinyint(1) DEFAULT 0 COMMENT '是否已完成',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `notified_at` timestamp NULL DEFAULT NULL COMMENT '通知时间',
  `is_displayed` tinyint(1) DEFAULT 1 COMMENT '是否在用户页面显示',
  `completion_count` int(11) DEFAULT 0 COMMENT '完成次数(可重复成就)',
  `last_updated_at` timestamp NULL DEFAULT NULL COMMENT '进度最后更新时间',
  `metadata` json DEFAULT NULL COMMENT '扩展数据',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_achievement` (`user_id`, `achievement_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_achievement_id` (`achievement_id`),
  KEY `idx_is_completed` (`is_completed`),
  KEY `idx_completed_at` (`completed_at`),
  KEY `idx_progress_percentage` (`progress_percentage`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户成就记录表';
```

### 7. 积分日志表 (point_operation_logs)

```sql
CREATE TABLE `point_operation_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `operation_type` enum('add','subtract','reset','transfer') NOT NULL COMMENT '操作类型',
  `before_points` int(11) NOT NULL COMMENT '操作前积分',
  `after_points` int(11) NOT NULL COMMENT '操作后积分',
  `point_change` int(11) NOT NULL COMMENT '积分变化量',
  `operation_reason` varchar(255) NOT NULL COMMENT '操作原因',
  `operator_id` bigint(20) unsigned DEFAULT NULL COMMENT '操作员ID(系统操作为NULL)',
  `operator_type` enum('system','admin','user') DEFAULT 'system' COMMENT '操作员类型',
  `related_record_id` bigint(20) unsigned DEFAULT NULL COMMENT '关联的积分记录ID',
  `batch_id` varchar(50) DEFAULT NULL COMMENT '批量操作ID',
  `ip_address` varchar(45) DEFAULT NULL COMMENT '操作IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_batch_id` (`batch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分操作日志表';
```

### 8. 用户积分统计表 (user_point_statistics)

```sql
CREATE TABLE `user_point_statistics` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `stat_type` enum('daily','weekly','monthly','yearly') NOT NULL COMMENT '统计类型',
  `points_earned` int(11) DEFAULT 0 COMMENT '获得积分',
  `points_consumed` int(11) DEFAULT 0 COMMENT '消费积分',
  `net_points` int(11) DEFAULT 0 COMMENT '净积分',
  `article_published` int(11) DEFAULT 0 COMMENT '发布文章数',
  `likes_received` int(11) DEFAULT 0 COMMENT '获得点赞数',
  `comments_made` int(11) DEFAULT 0 COMMENT '评论数',
  `documents_viewed` int(11) DEFAULT 0 COMMENT '浏览文档数',
  `active_days` int(11) DEFAULT 0 COMMENT '活跃天数',
  `rank_in_period` int(11) DEFAULT 0 COMMENT '期间排名',
  `achievements_unlocked` int(11) DEFAULT 0 COMMENT '解锁成就数',
  `level_at_period_start` tinyint(4) DEFAULT 1 COMMENT '期间开始时等级',
  `level_at_period_end` tinyint(4) DEFAULT 1 COMMENT '期间结束时等级',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_date_type` (`user_id`, `stat_date`, `stat_type`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_stat_type` (`stat_type`),
  KEY `idx_points_earned` (`points_earned`),
  KEY `idx_rank_in_period` (`rank_in_period`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户积分统计表';
```

## 📋 初始化数据设计

### 等级配置初始数据

```sql
INSERT INTO `level_configs` (`level`, `level_name`, `level_title`, `min_points`, `max_points`, `color`, `upgrade_reward`, `description`, `privileges`) VALUES
(1, '青铜智者', '知识探索者', 0, 99, '#CD7F32', 0, '初入知识殿堂，开始你的学习之旅', '{"basic_post": true, "basic_comment": true}'),
(2, '白银智者', '知识分享者', 100, 499, '#C0C0C0', 20, '积极分享知识，帮助他人成长', '{"image_upload": true, "topic_create": true}'),
(3, '黄金智者', '知识贡献者', 500, 999, '#FFD700', 50, '持续贡献优质内容，成为社区中坚', '{"file_upload": true, "essence_recommend": true}'),
(4, '钻石智者', '知识专家', 1000, 4999, '#B9F2FF', 100, '专业知识深厚，引领行业发展', '{"moderator_rights": true, "featured_author": true}'),
(5, '传奇智者', '知识领袖', 5000, NULL, '#FF6B6B', 200, '知识领域的标杆，影响力深远', '{"all_permissions": true, "exclusive_features": true}');
```

### 积分配置初始数据

```sql
INSERT INTO `point_configs` (`action_type`, `action_name`, `points`, `max_daily_count`, `max_daily_points`, `first_time_bonus`, `category`, `description`) VALUES
('publish_article', '发布知识文档', 10, NULL, NULL, 5, 'content', '发布原创知识文档，分享专业见解'),
('get_like', '文档获得点赞', 1, NULL, 50, 0, 'interaction', '你的文档获得其他用户点赞'),
('add_essence', '文档被加精', 50, NULL, NULL, 0, 'special', '文档被管理员认定为精华内容'),
('delete_article', '文档被删除', -10, NULL, NULL, 0, 'content', '发布的文档因违规被删除'),
('offline_training', '线下培训认证', 300, 1, 300, 0, 'training', '参加线下培训并通过人事认证'),
('daily_checkin', '每日签到', 2, 1, 2, 3, 'general', '每日登录系统签到'),
('comment_article', '评论文档', 1, 10, 10, 1, 'interaction', '对他人文档发表有价值评论'),
('share_article', '分享文档', 2, 5, 10, 0, 'interaction', '分享优质文档给其他用户'),
('complete_profile', '完善个人资料', 20, 1, 20, 0, 'general', '完善个人资料信息'),
('invite_user', '邀请新用户', 30, 3, 90, 0, 'special', '成功邀请新用户加入平台');
```

### 成就配置初始数据

```sql
INSERT INTO `achievements` (`code`, `name`, `description`, `category`, `difficulty`, `condition_type`, `condition_value`, `reward_points`, `tips`) VALUES
('first_article', '萤火之光', '发布第1篇知识文章', 'article', 1, 'article_count', 1, 15, '开始你的知识分享之旅'),
('knowledge_contributor', '知识分子', '累计发布20篇知识文章', 'article', 2, 'article_count', 20, 150, '持续分享，积累知识财富'),
('essence_master', '精华达人', '累计10篇文章被加精', 'article', 3, 'essence_count', 10, 300, '追求质量，创作精品内容'),
('view_milestone', '万众瞩目', '文章总阅读量达100,000', 'milestone', 4, 'total_views', 100000, 500, '你的内容影响力巨大'),
('like_collector', '智慧之星', '累计获得1000次点赞', 'interaction', 4, 'like_count', 1000, 400, '深受社区用户喜爱'),
('early_bird', '早起鸟儿', '连续7天早晨8点前登录', 'special', 2, 'early_login_streak', 7, 50, '早起的鸟儿有虫吃'),
('night_owl', '夜猫子', '连续7天晚上10点后发布内容', 'special', 2, 'night_post_streak', 7, 50, '夜深人静时的思考者'),
('social_butterfly', '社交达人', '获得100个用户关注', 'interaction', 3, 'follower_count', 100, 200, '社区中的明星用户'),
('helpful_commenter', '评论之王', '发表500条有用评论', 'interaction', 3, 'comment_count', 500, 250, '乐于助人的评论达人'),
('consistent_contributor', '持续贡献者', '连续30天每天发布内容', 'milestone', 4, 'daily_post_streak', 30, 600, '坚持不懈的知识贡献者');
```

## 🔧 扩展功能设计

### 积分倍数和活动系统

```sql
-- 积分活动表
CREATE TABLE `point_events` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '活动名称',
  `description` text COMMENT '活动描述',
  `event_type` enum('multiplier','bonus','special') DEFAULT 'multiplier' COMMENT '活动类型',
  `action_types` json NOT NULL COMMENT '影响的行为类型',
  `multiplier` decimal(3,2) DEFAULT 1.00 COMMENT '积分倍数',
  `bonus_points` int(11) DEFAULT 0 COMMENT '额外奖励积分',
  `max_participants` int(11) DEFAULT NULL COMMENT '最大参与人数',
  `start_time` timestamp NOT NULL COMMENT '活动开始时间',
  `end_time` timestamp NOT NULL COMMENT '活动结束时间',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_time_range` (`start_time`, `end_time`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分活动表';
```

### 积分商城系统

```sql
-- 积分商品表
CREATE TABLE `point_shop_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '商品名称',
  `description` text COMMENT '商品描述',
  `category` varchar(50) DEFAULT 'general' COMMENT '商品分类',
  `points_cost` int(11) NOT NULL COMMENT '积分价格',
  `stock_quantity` int(11) DEFAULT -1 COMMENT '库存数量(-1表示无限)',
  `purchase_limit` int(11) DEFAULT NULL COMMENT '购买限制',
  `level_requirement` tinyint(4) DEFAULT 1 COMMENT '等级要求',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否上架',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_points_cost` (`points_cost`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分商城商品表';

-- 积分消费记录表
CREATE TABLE `point_consumptions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `item_id` bigint(20) unsigned DEFAULT NULL COMMENT '商品ID',
  `consumption_type` enum('shop_purchase','custom_reward','admin_deduct') DEFAULT 'shop_purchase',
  `points_spent` int(11) NOT NULL COMMENT '消费积分',
  `description` varchar(255) NOT NULL COMMENT '消费说明',
  `status` enum('pending','completed','cancelled','refunded') DEFAULT 'completed',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分消费记录表';
```

## 📊 数据关系图

```
用户表 (users)
    ↓ 1:1
用户积分表 (user_points)
    ↓ 1:N
积分记录表 (point_records)

积分配置表 (point_configs)
    ↓ 影响
积分记录表 (point_records)

等级配置表 (level_configs)
    ↓ 关联
用户积分表 (user_points)

成就定义表 (achievements)
    ↓ 1:N
用户成就表 (user_achievements)
    ↑ N:1
用户表 (users)

用户积分表 (user_points)
    ↓ 1:N
积分操作日志表 (point_operation_logs)

用户表 (users)
    ↓ 1:N
用户积分统计表 (user_point_statistics)
```

## 🔒 数据安全和性能优化

### 数据完整性保障
1. **乐观锁机制**: 用户积分表使用version字段防止并发修改
2. **事务保证**: 积分变动操作必须在事务中执行
3. **日志记录**: 所有积分操作都有详细日志记录
4. **数据校验**: 定期校验积分数据一致性

### 性能优化策略
1. **索引优化**: 基于查询模式建立合理索引
2. **分表策略**: 积分记录表可按时间分表
3. **缓存设计**: 热点数据Redis缓存
4. **批量处理**: 统计数据批量计算更新

### 数据归档方案
1. **历史数据归档**: 超过1年的积分记录归档存储
2. **统计数据保留**: 统计表数据永久保留
3. **日志清理**: 操作日志定期清理，保留关键记录

---

**总结**: 该数据库设计方案为知识豆积分系统提供了完整的数据支撑，包含积分管理、等级体系、成就系统、统计分析等各个方面，具备良好的扩展性和性能表现。