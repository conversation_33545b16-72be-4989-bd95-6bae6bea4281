# Wiki文档审核功能前端实现总结

## 项目概述

为TChip BI项目的Wiki知识库模块添加了完整的前端文档审核功能，包括API接口、审核按钮、审核弹窗和权限控制等功能。

## 实现的功能

### 1. API接口层 (biWiki.ts)

新增了11个文档状态管理相关的API接口：

| 接口名称 | 功能描述 | 参数 |
|---------|----------|------|
| `setDocumentAuditStatus` | 设置审核状态 | `{doc_id, audit_status, reason}` |
| `setDocumentPremiumStatus` | 设置精华认证 | `{doc_id, is_premium, reason}` |
| `setDocumentTrainingStatus` | 设置培训认证 | `{doc_id, is_training, reason, expired_at}` |
| `batchAuditDocuments` | 批量审核文档 | `{doc_ids, audit_status, reason}` |
| `getDocumentStatusHistory` | 获取状态历史 | `{doc_id}` |
| `getPendingAuditDocuments` | 获取待审核文档 | `{limit, offset}` |
| `getPremiumDocuments` | 获取精华文档 | `{limit, offset}` |
| `getTrainingDocuments` | 获取培训文档 | `{limit, offset}` |
| `getExpiringTrainingDocuments` | 获取即将过期培训文档 | `{days}` |
| `getDocumentStatusTags` | 获取文档状态标签 | `{doc_id}` |
| `canPublishDocument` | 检查是否可发布 | `{doc_id}` |

### 2. 审核弹窗组件 (WikiDocumentAuditDialog.vue)

#### 主要功能
- **复选框选项**：
  - ✅ 加10豆：审核通过并发放积分奖励
  - ✅ 精华：标记为精华文档
  - ✅ 培训认证：认证为培训材料（支持设置有效期）
- **审核操作**：
  - 审核通过：根据选择的选项进行相应的状态设置
  - 审核拒绝：直接拒绝文档审核
- **表单验证**：支持审核意见输入，培训认证过期时间选择

#### 技术特性
- 使用`firefly-dialog`组件，保持与项目UI风格一致
- 响应式表单设计，支持动态显示培训认证有效期选择
- 完整的错误处理和用户反馈
- 支持多个状态的组合设置

### 3. 审核按钮集成 (WikiContentEditor.vue)

#### 按钮位置
按照要求在"编辑"按钮和"标签"按钮之间添加了"审核"按钮，共有两个位置：
- 富文本编辑模式下的按钮组
- Markdown编辑模式下的按钮组

#### 按钮样式
- 使用`type="warning"`突出审核功能
- 使用`vab-icon`图标组件，图标为"audit"
- 保持与其他按钮一致的样式风格

### 4. 权限控制系统

#### 审核权限逻辑
实现了`hasAuditPermission()`方法，权限控制如下：
- ✅ **超管（Admin/Director）**：拥有所有审核权限
- ✅ **空间所有者（owner）**：拥有该空间的审核权限
- ✅ **空间管理员（editor）**：拥有该空间的审核权限
- ❌ **空间成员（member）**：无审核权限
- ❌ **访客**：无审核权限

#### 权限验证
- 按钮显示：通过`v-if="!inEditMode && hasAuditPermission()"`控制
- 操作验证：点击时再次验证权限，无权限时显示错误提示

### 5. 事件处理机制

#### 组件通信
- **审核成功事件**：`@audit-success`向父组件传递审核结果
- **弹窗控制**：通过`v-model`双向绑定控制弹窗显示
- **数据刷新**：审核成功后触发父组件数据刷新

#### 事件参数
```javascript
// 审核成功事件参数
{
  action: 'approve' | 'reject',
  options: {
    grantPoints: boolean,
    isPremium: boolean,
    isTraining: boolean,
    trainingExpiry: string,
    reason: string
  },
  documentId: number
}
```

## 技术实现细节

### 1. 组件结构
```
WikiContentEditor.vue
├── 审核按钮 (两个位置)
├── WikiDocumentAuditDialog.vue (审核弹窗)
├── 权限控制逻辑
└── 事件处理方法
```

### 2. 状态管理
```javascript
// 响应式数据
const showAuditDialog = ref(false)

// 表单数据
const auditForm = ref({
  grantPoints: false,    // 是否发放积分
  isPremium: false,      // 是否精华
  isTraining: false,     // 是否培训认证
  trainingExpiry: '',    // 培训认证过期时间
  reason: ''             // 审核意见
})
```

### 3. API调用逻辑
审核通过时的操作顺序：
1. 首先调用`setDocumentAuditStatus`设置审核状态为通过
2. 如果勾选精华认证，调用`setDocumentPremiumStatus`
3. 如果勾选培训认证，调用`setDocumentTrainingStatus`
4. 所有操作完成后触发成功事件

## 用户交互流程

### 审核操作流程
1. **权限检查**：具有审核权限的用户可以看到"审核"按钮
2. **打开弹窗**：点击审核按钮打开审核弹窗
3. **选择选项**：
   - 勾选"加10豆"：审核通过且发放积分
   - 勾选"精华"：标记为精华文档
   - 勾选"培训认证"：认证为培训材料，可设置有效期
4. **填写意见**：可选填写审核意见
5. **提交审核**：
   - 点击"审核通过"：执行相应的状态设置
   - 点击"审核拒绝"：直接拒绝审核

### 用户反馈
- **成功提示**：审核操作完成后显示成功消息
- **错误提示**：权限不足或操作失败时显示错误消息
- **加载状态**：操作过程中显示加载状态

## 样式设计

### 审核弹窗样式
- **文档信息卡片**：蓝色左边框，灰色背景，突出显示文档信息
- **选项布局**：垂直排列，每个选项包含主标签和描述文字
- **培训认证扩展**：勾选时显示有效期选择，有独立的背景色区分
- **按钮组**：右对齐，取消、审核通过、审核拒绝三个按钮

### 审核按钮样式
- **颜色**：使用`type="warning"`的橙色主题
- **图标**：使用audit图标，保持视觉一致性
- **布局**：与编辑、标签按钮保持一致的间距和大小

## 文件清单

### 新增文件
- `src/views/oa/Wiki/components/WikiDocumentAuditDialog.vue` - 审核弹窗组件

### 修改文件
- `src/api/biWiki.ts` - 新增11个API接口
- `src/views/oa/Wiki/components/WikiContentEditor.vue` - 集成审核按钮和逻辑

## 部署注意事项

1. **图标资源**：确保`audit`图标在项目中可用
2. **权限数据**：确保`currentUserRole`属性正确传递
3. **API接口**：确保后端接口已部署且可访问
4. **样式兼容**：审核弹窗样式与项目整体风格保持一致

## 后续扩展建议

1. **状态显示**：在文档列表中显示文档的审核状态标签
2. **批量操作**：支持批量选择文档进行审核
3. **审核历史**：查看文档的完整审核历史记录
4. **通知机制**：审核结果通知文档作者
5. **统计面板**：审核工作量统计和报表功能

## 测试建议

### 功能测试
- [ ] 不同权限用户的按钮显示测试
- [ ] 审核弹窗的各个选项组合测试
- [ ] 培训认证有效期选择功能测试
- [ ] 审核通过/拒绝的后端交互测试

### 权限测试
- [ ] 超管权限测试
- [ ] 空间所有者权限测试
- [ ] 空间管理员权限测试
- [ ] 普通成员权限限制测试

### 界面测试
- [ ] 弹窗样式和响应式布局测试
- [ ] 按钮样式和交互状态测试
- [ ] 错误提示和成功反馈测试