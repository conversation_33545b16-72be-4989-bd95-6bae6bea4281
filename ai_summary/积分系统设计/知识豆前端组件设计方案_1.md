# 知识豆前端组件设计方案

## 🎯 设计目标

基于现有TChipBI前端架构（Vue 3 + Element Plus），设计完整的积分系统前端组件，提供直观、友好的用户体验，确保组件的可复用性和可维护性。

## 🧩 组件架构设计

### 1. 组件层次结构
```
积分系统前端组件
├── 页面组件 (Pages)
│   ├── PointsIndex.vue (积分系统主页)
│   ├── MyPoints.vue (我的积分)
│   ├── PointsRanking.vue (积分排行榜)
│   ├── Achievements.vue (成就系统)
│   └── PointsAdmin.vue (积分管理后台)
├── 业务组件 (Business)
│   ├── PointsCard.vue (积分卡片)
│   ├── LevelBadge.vue (等级徽章)
│   ├── AchievementCard.vue (成就卡片)
│   ├── PointsProgress.vue (积分进度条)
│   ├── PointsRecords.vue (积分记录)
│   ├── RankingList.vue (排行榜列表)
│   └── PointsStatistics.vue (积分统计)
├── 基础组件 (Base)
│   ├── PointsDisplay.vue (积分数值显示)
│   ├── LevelIcon.vue (等级图标)
│   ├── ProgressRing.vue (环形进度条)
│   ├── AnimatedCounter.vue (动画计数器)
│   └── TooltipInfo.vue (提示信息)
└── 功能组件 (Functional)
    ├── PointsNotification.vue (积分通知)
    ├── AchievementModal.vue (成就弹窗)
    ├── PointsChart.vue (积分图表)
    └── LevelUpAnimation.vue (升级动画)
```

## 📄 页面组件设计

### 1. 我的积分页面 (MyPoints.vue)
```vue
<template>
  <div class="my-points-page">
    <!-- 积分概览卡片区域 -->
    <el-row :gutter="20" class="points-overview">
      <el-col :span="6">
        <PointsCard
          title="当前积分"
          :value="userPoints.current_points"
          icon="star"
          color="primary"
          :loading="loading"
        />
      </el-col>
      <el-col :span="6">
        <PointsCard
          title="累计积分"
          :value="userPoints.total_points"
          icon="trophy"
          color="success"
          :loading="loading"
        />
      </el-col>
      <el-col :span="6">
        <LevelCard
          :level="userPoints.level"
          :level-name="userPoints.level_name"
          :current-points="userPoints.current_points"
          :next-level-points="nextLevelPoints"
          :loading="loading"
        />
      </el-col>
      <el-col :span="6">
        <AchievementSummary
          :total="achievementStats.total"
          :completed="achievementStats.completed"
          :in-progress="achievementStats.inProgress"
          :loading="loading"
        />
      </el-col>
    </el-row>

    <!-- 详细信息选项卡 -->
    <el-card class="mt-4">
      <el-tabs v-model="activeTab" class="points-tabs">
        <el-tab-pane label="积分记录" name="records">
          <PointsRecords
            :records="pointRecords"
            :loading="recordsLoading"
            @load-more="loadMoreRecords"
          />
        </el-tab-pane>
        
        <el-tab-pane label="统计分析" name="statistics">
          <PointsStatistics
            :user-id="userId"
            :chart-data="statisticsData"
            :loading="statisticsLoading"
          />
        </el-tab-pane>
        
        <el-tab-pane label="我的成就" name="achievements">
          <UserAchievements
            :achievements="userAchievements"
            :loading="achievementsLoading"
            @achievement-click="showAchievementDetail"
          />
        </el-tab-pane>
        
        <el-tab-pane label="积分商城" name="shop">
          <PointsShop
            :items="shopItems"
            :user-points="userPoints.current_points"
            :loading="shopLoading"
            @purchase="handlePurchase"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 成就详情弹窗 -->
    <AchievementModal
      v-model="achievementModalVisible"
      :achievement="selectedAchievement"
      @close="achievementModalVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { usePointsStore } from '@/store/modules/points'
import { useUserStore } from '@/store/modules/user'

// 组件引入
import PointsCard from '@/components/points/PointsCard.vue'
import LevelCard from '@/components/points/LevelCard.vue'
import AchievementSummary from '@/components/points/AchievementSummary.vue'
import PointsRecords from '@/components/points/PointsRecords.vue'
import PointsStatistics from '@/components/points/PointsStatistics.vue'
import UserAchievements from '@/components/points/UserAchievements.vue'
import PointsShop from '@/components/points/PointsShop.vue'
import AchievementModal from '@/components/points/AchievementModal.vue'

// 状态管理
const pointsStore = usePointsStore()
const userStore = useUserStore()

// 响应式数据
const loading = ref(true)
const activeTab = ref('records')
const achievementModalVisible = ref(false)
const selectedAchievement = ref(null)

// 计算属性
const userId = computed(() => userStore.userInfo?.id)
const userPoints = computed(() => pointsStore.userPoints)
const nextLevelPoints = computed(() => pointsStore.nextLevelPoints)

// 生命周期
onMounted(async () => {
  await loadUserPointsData()
  loading.value = false
})

// 方法
const loadUserPointsData = async () => {
  try {
    await Promise.all([
      pointsStore.fetchUserPoints(),
      pointsStore.fetchPointRecords(),
      pointsStore.fetchUserAchievements(),
      pointsStore.fetchAchievementStats()
    ])
  } catch (error) {
    console.error('加载用户积分数据失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.my-points-page {
  padding: 20px;
  
  .points-overview {
    margin-bottom: 20px;
  }
  
  .points-tabs {
    .el-tab-pane {
      min-height: 400px;
    }
  }
}
</style>
```

### 2. 积分排行榜页面 (PointsRanking.vue)
```vue
<template>
  <div class="points-ranking-page">
    <!-- 排行榜头部 -->
    <el-card class="ranking-header">
      <div class="header-content">
        <h2>积分排行榜</h2>
        <div class="ranking-filters">
          <el-select v-model="rankingType" @change="loadRankingData">
            <el-option label="总积分排行" value="total_points" />
            <el-option label="月度积分排行" value="monthly_points" />
            <el-option label="周积分排行" value="weekly_points" />
            <el-option label="贡献度排行" value="contribution" />
          </el-select>
          
          <el-select v-model="departmentFilter" @change="loadRankingData">
            <el-option label="全公司" value="" />
            <el-option 
              v-for="dept in departments" 
              :key="dept.id"
              :label="dept.name" 
              :value="dept.id" 
            />
          </el-select>
        </div>
      </div>
    </el-card>

    <!-- 前三名展示 -->
    <div class="top-three-section">
      <el-row :gutter="20">
        <el-col :span="8" v-for="(user, index) in topThreeUsers" :key="user.id">
          <TopRankerCard
            :user="user"
            :rank="index + 1"
            :ranking-type="rankingType"
            class="top-ranker-card"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 完整排行榜 -->
    <el-card class="ranking-list-card">
      <RankingList
        :rankings="rankings"
        :current-user-rank="currentUserRank"
        :ranking-type="rankingType"
        :loading="loading"
        @load-more="loadMoreRankings"
      />
    </el-card>

    <!-- 当前用户排名 -->
    <el-card v-if="currentUserRank" class="current-user-rank">
      <CurrentUserRank
        :rank="currentUserRank"
        :ranking-type="rankingType"
        :total-users="totalUsers"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { usePointsStore } from '@/store/modules/points'

// 组件引入
import TopRankerCard from '@/components/points/TopRankerCard.vue'
import RankingList from '@/components/points/RankingList.vue'
import CurrentUserRank from '@/components/points/CurrentUserRank.vue'

// 响应式数据
const rankingType = ref('total_points')
const departmentFilter = ref('')
const loading = ref(true)
const rankings = ref([])
const topThreeUsers = ref([])
const currentUserRank = ref(null)
const totalUsers = ref(0)
const departments = ref([])

// 样式
const style = {
  '.points-ranking-page': {
    padding: '20px',
    
    '.ranking-header': {
      marginBottom: '20px',
      
      '.header-content': {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        
        'h2': {
          margin: 0,
          color: '#303133'
        },
        
        '.ranking-filters': {
          display: 'flex',
          gap: '12px'
        }
      }
    },
    
    '.top-three-section': {
      marginBottom: '20px',
      
      '.top-ranker-card': {
        height: '200px'
      }
    },
    
    '.ranking-list-card': {
      marginBottom: '20px'
    },
    
    '.current-user-rank': {
      backgroundColor: '#f0f9ff',
      borderColor: '#409eff'
    }
  }
}
</script>
```

## 🧩 业务组件设计

### 1. 积分卡片组件 (PointsCard.vue)
```vue
<template>
  <el-card class="points-card" :class="`points-card--${color}`">
    <div class="card-content">
      <div class="card-icon">
        <el-icon :size="32" :class="`icon--${color}`">
          <component :is="iconComponent" />
        </el-icon>
      </div>
      
      <div class="card-info">
        <h3 class="card-title">{{ title }}</h3>
        <div class="card-value">
          <AnimatedCounter
            v-if="!loading"
            :value="value"
            :duration="1000"
            :format="formatValue"
          />
          <el-skeleton-item v-else variant="text" style="width: 80px" />
        </div>
        
        <div v-if="trend" class="card-trend">
          <el-icon :class="trendClass">
            <ArrowUp v-if="trend > 0" />
            <ArrowDown v-if="trend < 0" />
            <Minus v-if="trend === 0" />
          </el-icon>
          <span :class="trendClass">{{ formatTrend(trend) }}</span>
        </div>
      </div>
    </div>
    
    <div v-if="showProgress && progress !== undefined" class="card-progress">
      <el-progress
        :percentage="progress"
        :stroke-width="4"
        :show-text="false"
        :color="progressColor"
      />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Star, Trophy, Gift, TrendCharts } from '@element-plus/icons-vue'
import AnimatedCounter from '@/components/points/AnimatedCounter.vue'

// 属性定义
interface Props {
  title: string
  value: number
  icon?: string
  color?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  loading?: boolean
  trend?: number
  progress?: number
  showProgress?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  color: 'primary',
  loading: false,
  showProgress: false
})

// 计算属性
const iconComponent = computed(() => {
  const iconMap = {
    star: Star,
    trophy: Trophy,
    gift: Gift,
    chart: TrendCharts
  }
  return iconMap[props.icon] || Star
})

const trendClass = computed(() => {
  if (props.trend > 0) return 'trend-up'
  if (props.trend < 0) return 'trend-down'
  return 'trend-neutral'
})

const progressColor = computed(() => {
  const colorMap = {
    primary: '#409eff',
    success: '#67c23a',
    warning: '#e6a23c',
    danger: '#f56c6c',
    info: '#909399'
  }
  return colorMap[props.color]
})

// 方法
const formatValue = (value: number): string => {
  if (value >= 10000) {
    return `${(value / 10000).toFixed(1)}万`
  }
  return value.toLocaleString()
}

const formatTrend = (trend: number): string => {
  const sign = trend > 0 ? '+' : ''
  return `${sign}${trend}%`
}
</script>

<style lang="scss" scoped>
.points-card {
  height: 140px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
  
  .card-content {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    height: 100px;
    
    .card-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      border-radius: 12px;
      background: rgba(64, 158, 255, 0.1);
      
      .icon--primary { color: #409eff; }
      .icon--success { color: #67c23a; }
      .icon--warning { color: #e6a23c; }
      .icon--danger { color: #f56c6c; }
      .icon--info { color: #909399; }
    }
    
    .card-info {
      flex: 1;
      
      .card-title {
        margin: 0 0 8px 0;
        font-size: 14px;
        color: #666;
        font-weight: normal;
      }
      
      .card-value {
        margin-bottom: 4px;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
      }
      
      .card-trend {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        
        .trend-up { color: #67c23a; }
        .trend-down { color: #f56c6c; }
        .trend-neutral { color: #909399; }
      }
    }
  }
  
  .card-progress {
    margin-top: 12px;
  }
  
  // 卡片颜色变体
  &.points-card--primary {
    border-left: 4px solid #409eff;
  }
  
  &.points-card--success {
    border-left: 4px solid #67c23a;
  }
  
  &.points-card--warning {
    border-left: 4px solid #e6a23c;
  }
  
  &.points-card--danger {
    border-left: 4px solid #f56c6c;
  }
}
</style>
```

### 2. 等级徽章组件 (LevelBadge.vue)
```vue
<template>
  <div class="level-badge" :class="`level-badge--${levelClass}`">
    <div class="badge-container">
      <!-- 等级图标 -->
      <div class="level-icon">
        <img v-if="levelConfig.icon" :src="levelConfig.icon" :alt="levelName" />
        <el-icon v-else :size="size > 'small' ? 24 : 16">
          <Crown v-if="level >= 4" />
          <Medal v-else-if="level >= 2" />
          <Star v-else />
        </el-icon>
      </div>
      
      <!-- 等级信息 -->
      <div v-if="showText" class="level-info">
        <div class="level-name">{{ levelName }}</div>
        <div v-if="showProgress" class="level-progress">
          <div class="progress-text">
            {{ currentPoints }}/{{ nextLevelPoints }} 
            <span class="progress-percentage">({{ progressPercentage }}%)</span>
          </div>
          <el-progress
            :percentage="progressPercentage"
            :stroke-width="4"
            :show-text="false"
            :color="levelConfig.color"
          />
        </div>
      </div>
    </div>
    
    <!-- 悬浮提示 -->
    <el-tooltip v-if="showTooltip" :content="tooltipContent" placement="top">
      <div class="tooltip-trigger"></div>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Crown, Medal, Star } from '@element-plus/icons-vue'
import { useLevelConfig } from '@/composables/useLevelConfig'

interface Props {
  level: number
  levelName: string
  currentPoints: number
  nextLevelPoints?: number
  size?: 'small' | 'medium' | 'large'
  showText?: boolean
  showProgress?: boolean
  showTooltip?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  showText: true,
  showProgress: false,
  showTooltip: true
})

// 组合式函数
const { getLevelConfig } = useLevelConfig()

// 计算属性
const levelConfig = computed(() => getLevelConfig(props.level))

const levelClass = computed(() => {
  const classMap = {
    1: 'bronze',
    2: 'silver', 
    3: 'gold',
    4: 'diamond',
    5: 'legend'
  }
  return classMap[props.level] || 'bronze'
})

const progressPercentage = computed(() => {
  if (!props.nextLevelPoints || !props.showProgress) return 0
  const levelStartPoints = levelConfig.value.minPoints
  const levelRange = props.nextLevelPoints - levelStartPoints
  const currentProgress = props.currentPoints - levelStartPoints
  return Math.round((currentProgress / levelRange) * 100)
})

const tooltipContent = computed(() => {
  const config = levelConfig.value
  let content = `${props.levelName} (等级 ${props.level})`
  if (config.description) {
    content += `\n${config.description}`
  }
  if (props.showProgress && props.nextLevelPoints) {
    const needed = props.nextLevelPoints - props.currentPoints
    content += `\n距离下一级还需 ${needed} 积分`
  }
  return content
})
</script>

<style lang="scss" scoped>
.level-badge {
  position: relative;
  
  .badge-container {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .level-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
      position: relative;
      
      img {
        width: 24px;
        height: 24px;
        object-fit: contain;
      }
      
      // 等级光环效果
      &::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border-radius: 50%;
        background: conic-gradient(from 0deg, transparent, currentColor, transparent);
        z-index: -1;
        animation: rotate 3s linear infinite;
      }
    }
    
    .level-info {
      flex: 1;
      
      .level-name {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .level-progress {
        .progress-text {
          font-size: 12px;
          color: #666;
          margin-bottom: 4px;
          
          .progress-percentage {
            color: #409eff;
            font-weight: 500;
          }
        }
      }
    }
  }
  
  .tooltip-trigger {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
  }
  
  // 等级颜色变体
  &.level-badge--bronze .level-icon {
    background: linear-gradient(135deg, #CD7F32, #D4A574);
    color: #8B4513;
  }
  
  &.level-badge--silver .level-icon {
    background: linear-gradient(135deg, #C0C0C0, #E8E8E8);
    color: #696969;
  }
  
  &.level-badge--gold .level-icon {
    background: linear-gradient(135deg, #FFD700, #FFF4B8);
    color: #B8860B;
  }
  
  &.level-badge--diamond .level-icon {
    background: linear-gradient(135deg, #B9F2FF, #E0F6FF);
    color: #4682B4;
  }
  
  &.level-badge--legend .level-icon {
    background: linear-gradient(135deg, #FF6B6B, #FF9999);
    color: #DC143C;
  }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
```

### 3. 成就卡片组件 (AchievementCard.vue)
```vue
<template>
  <div 
    class="achievement-card" 
    :class="{
      'achievement-card--completed': achievement.isCompleted,
      'achievement-card--locked': achievement.isLocked,
      'achievement-card--rare': achievement.difficulty >= 4
    }"
    @click="$emit('click', achievement)"
  >
    <!-- 成就图标 -->
    <div class="achievement-icon">
      <img 
        v-if="achievement.icon" 
        :src="achievement.icon" 
        :alt="achievement.name"
        :class="{ 'grayscale': !achievement.isCompleted }"
      />
      <el-icon v-else :size="32" :class="{ 'grayscale': !achievement.isCompleted }">
        <Trophy />
      </el-icon>
      
      <!-- 稀有度装饰 -->
      <div v-if="achievement.difficulty >= 4" class="rarity-glow"></div>
      
      <!-- 完成状态标识 -->
      <div v-if="achievement.isCompleted" class="completion-badge">
        <el-icon :size="16">
          <Check />
        </el-icon>
      </div>
    </div>
    
    <!-- 成就信息 -->
    <div class="achievement-info">
      <h4 class="achievement-name">{{ achievement.name }}</h4>
      <p class="achievement-description">{{ achievement.description }}</p>
      
      <!-- 进度条 -->
      <div v-if="!achievement.isCompleted && !achievement.isLocked" class="achievement-progress">
        <div class="progress-info">
          <span class="progress-text">
            {{ achievement.progress }}/{{ achievement.targetValue }}
          </span>
          <span class="progress-percentage">
            {{ Math.round(achievement.progressPercentage) }}%
          </span>
        </div>
        <el-progress
          :percentage="achievement.progressPercentage"
          :stroke-width="4"
          :show-text="false"
          :color="progressColor"
        />
      </div>
      
      <!-- 奖励信息 -->
      <div class="achievement-reward">
        <el-icon :size="14">
          <Star />
        </el-icon>
        <span>{{ achievement.rewardPoints }} 知识豆</span>
      </div>
      
      <!-- 难度指示器 -->
      <div class="achievement-difficulty">
        <el-rate
          :model-value="achievement.difficulty"
          :max="5"
          disabled
          :size="12"
          :colors="difficultyColors"
        />
      </div>
    </div>
    
    <!-- 锁定状态遮罩 -->
    <div v-if="achievement.isLocked" class="locked-overlay">
      <el-icon :size="24">
        <Lock />
      </el-icon>
      <span>未解锁</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Trophy, Check, Star, Lock } from '@element-plus/icons-vue'

interface Achievement {
  id: number
  name: string
  description: string
  icon?: string
  difficulty: number
  progress: number
  targetValue: number
  progressPercentage: number
  rewardPoints: number
  isCompleted: boolean
  isLocked: boolean
}

interface Props {
  achievement: Achievement
}

const props = defineProps<Props>()

// 计算属性
const progressColor = computed(() => {
  const percentage = props.achievement.progressPercentage
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 50) return '#e6a23c'
  return '#409eff'
})

const difficultyColors = computed(() => {
  return ['#C0C0C0', '#C0C0C0', '#FFD700', '#FF6B6B', '#9C27B0']
})

// 事件定义
defineEmits<{
  click: [achievement: Achievement]
}>()
</script>

<style lang="scss" scoped>
.achievement-card {
  position: relative;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #c6e2ff;
  }
  
  .achievement-icon {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64px;
    height: 64px;
    margin: 0 auto 12px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    
    img, .el-icon {
      transition: all 0.3s ease;
      
      &.grayscale {
        filter: grayscale(100%) brightness(0.7);
      }
    }
    
    .rarity-glow {
      position: absolute;
      top: -4px;
      left: -4px;
      right: -4px;
      bottom: -4px;
      border-radius: 50%;
      background: conic-gradient(
        from 0deg,
        #ff6b6b,
        #4ecdc4,
        #45b7d1,
        #96ceb4,
        #ffd93d,
        #ff6b6b
      );
      z-index: -1;
      animation: rotate 3s linear infinite;
    }
    
    .completion-badge {
      position: absolute;
      top: -2px;
      right: -2px;
      width: 20px;
      height: 20px;
      background: #67c23a;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }
  }
  
  .achievement-info {
    text-align: center;
    
    .achievement-name {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
    
    .achievement-description {
      margin: 0 0 12px 0;
      font-size: 12px;
      color: #666;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .achievement-progress {
      margin-bottom: 12px;
      
      .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
        font-size: 12px;
        
        .progress-text {
          color: #666;
        }
        
        .progress-percentage {
          color: #409eff;
          font-weight: 500;
        }
      }
    }
    
    .achievement-reward {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      margin-bottom: 8px;
      font-size: 12px;
      color: #e6a23c;
      font-weight: 500;
    }
    
    .achievement-difficulty {
      display: flex;
      justify-content: center;
    }
  }
  
  .locked-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #999;
    font-size: 12px;
  }
  
  // 状态变体
  &.achievement-card--completed {
    border-color: #67c23a;
    background: linear-gradient(135deg, #f0f9ff 0%, #e6fffa 100%);
    
    .achievement-icon {
      background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
    }
  }
  
  &.achievement-card--locked {
    opacity: 0.6;
  }
  
  &.achievement-card--rare {
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        45deg,
        transparent 30%,
        rgba(255, 107, 107, 0.1) 50%,
        transparent 70%
      );
      z-index: 0;
    }
  }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
```

## 🔧 功能组件设计

### 1. 积分通知组件 (PointsNotification.vue)
```vue
<template>
  <transition name="notification" appear>
    <div v-if="visible" class="points-notification" :class="`notification--${type}`">
      <div class="notification-content">
        <div class="notification-icon">
          <el-icon :size="24">
            <Star v-if="type === 'points'" />
            <Trophy v-if="type === 'achievement'" />
            <TrendCharts v-if="type === 'level'" />
          </el-icon>
        </div>
        
        <div class="notification-text">
          <div class="notification-title">{{ title }}</div>
          <div class="notification-message">{{ message }}</div>
        </div>
        
        <div class="notification-value">
          <AnimatedCounter
            :value="value"
            :duration="800"
            :format="formatValue"
          />
        </div>
      </div>
      
      <div class="notification-close" @click="close">
        <el-icon :size="16">
          <Close />
        </el-icon>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Star, Trophy, TrendCharts, Close } from '@element-plus/icons-vue'
import AnimatedCounter from './AnimatedCounter.vue'

interface Props {
  type: 'points' | 'achievement' | 'level'
  title: string
  message: string
  value: number
  duration?: number
  autoClose?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  duration: 5000,
  autoClose: true
})

const emit = defineEmits<{
  close: []
}>()

const visible = ref(true)

const formatValue = (value: number): string => {
  if (props.type === 'points') {
    return `+${value}`
  }
  return value.toString()
}

const close = () => {
  visible.value = false
  setTimeout(() => {
    emit('close')
  }, 300)
}

onMounted(() => {
  if (props.autoClose && props.duration > 0) {
    setTimeout(close, props.duration)
  }
})
</script>

<style lang="scss" scoped>
.points-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  min-width: 320px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  
  .notification-content {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .notification-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }
    
    .notification-text {
      flex: 1;
      
      .notification-title {
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 2px;
      }
      
      .notification-message {
        font-size: 12px;
        color: #666;
      }
    }
    
    .notification-value {
      font-size: 18px;
      font-weight: 600;
    }
  }
  
  .notification-close {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #999;
    
    &:hover {
      color: #666;
    }
  }
  
  // 类型变体
  &.notification--points {
    border-left: 4px solid #67c23a;
    
    .notification-icon {
      background: rgba(103, 194, 58, 0.1);
      color: #67c23a;
    }
    
    .notification-value {
      color: #67c23a;
    }
  }
  
  &.notification--achievement {
    border-left: 4px solid #e6a23c;
    
    .notification-icon {
      background: rgba(230, 162, 60, 0.1);
      color: #e6a23c;
    }
    
    .notification-value {
      color: #e6a23c;
    }
  }
  
  &.notification--level {
    border-left: 4px solid #409eff;
    
    .notification-icon {
      background: rgba(64, 158, 255, 0.1);
      color: #409eff;
    }
    
    .notification-value {
      color: #409eff;
    }
  }
}

// 过渡动画
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.notification-leave-to {
  transform: translateX(100%);
  opacity: 0;
}
</style>
```

### 2. 升级动画组件 (LevelUpAnimation.vue)
```vue
<template>
  <teleport to="body">
    <div v-if="visible" class="level-up-animation">
      <div class="animation-container">
        <!-- 背景光效 -->
        <div class="background-glow"></div>
        
        <!-- 升级文字 -->
        <div class="level-up-text">
          <h1 class="main-text">等级提升！</h1>
          <div class="level-info">
            <div class="old-level">
              <LevelBadge
                :level="oldLevel"
                :level-name="oldLevelName"
                :current-points="0"
                :show-progress="false"
                size="large"
              />
            </div>
            
            <div class="arrow">
              <el-icon :size="32">
                <ArrowRight />
              </el-icon>
            </div>
            
            <div class="new-level">
              <LevelBadge
                :level="newLevel"
                :level-name="newLevelName"
                :current-points="0"
                :show-progress="false"
                size="large"
              />
            </div>
          </div>
          
          <div class="reward-info">
            <p>获得升级奖励：<span class="reward-points">+{{ rewardPoints }}</span> 知识豆</p>
          </div>
        </div>
        
        <!-- 粒子效果 -->
        <div class="particles">
          <div v-for="i in 20" :key="i" class="particle" :style="getParticleStyle(i)"></div>
        </div>
        
        <!-- 关闭按钮 -->
        <button class="close-button" @click="close">
          <el-icon :size="24">
            <Close />
          </el-icon>
        </button>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ArrowRight, Close } from '@element-plus/icons-vue'
import LevelBadge from './LevelBadge.vue'

interface Props {
  oldLevel: number
  newLevel: number
  oldLevelName: string
  newLevelName: string
  rewardPoints: number
  visible: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
}>()

const close = () => {
  emit('close')
}

const getParticleStyle = (index: number) => {
  const angle = (index / 20) * 360
  const delay = Math.random() * 2
  const duration = 2 + Math.random() * 2
  
  return {
    '--angle': `${angle}deg`,
    '--delay': `${delay}s`,
    '--duration': `${duration}s`
  }
}

// 自动播放音效（如果有的话）
onMounted(() => {
  // 这里可以添加音效播放逻辑
  // playLevelUpSound()
})
</script>

<style lang="scss" scoped>
.level-up-animation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.5s ease;
  
  .animation-container {
    position: relative;
    text-align: center;
    
    .background-glow {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 400px;
      height: 400px;
      background: radial-gradient(
        circle,
        rgba(255, 215, 0, 0.3) 0%,
        rgba(255, 215, 0, 0.1) 50%,
        transparent 100%
      );
      border-radius: 50%;
      animation: pulse 2s ease-in-out infinite;
    }
    
    .level-up-text {
      position: relative;
      z-index: 1;
      
      .main-text {
        font-size: 48px;
        font-weight: 700;
        color: #ffd700;
        margin: 0 0 32px 0;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        animation: bounce 1s ease;
      }
      
      .level-info {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 32px;
        margin-bottom: 24px;
        
        .old-level,
        .new-level {
          animation: slideIn 0.8s ease;
        }
        
        .new-level {
          animation-delay: 0.3s;
        }
        
        .arrow {
          color: #ffd700;
          animation: pulse 1.5s ease-in-out infinite;
        }
      }
      
      .reward-info {
        font-size: 18px;
        color: white;
        margin: 0;
        
        .reward-points {
          color: #67c23a;
          font-weight: 600;
          font-size: 24px;
        }
      }
    }
    
    .particles {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      
      .particle {
        position: absolute;
        width: 6px;
        height: 6px;
        background: #ffd700;
        border-radius: 50%;
        animation: particleFloat var(--duration) ease-out var(--delay) infinite;
        transform: rotate(var(--angle)) translateX(0);
      }
    }
    
    .close-button {
      position: absolute;
      top: -40px;
      right: -40px;
      width: 40px;
      height: 40px;
      background: rgba(255, 255, 255, 0.2);
      border: none;
      border-radius: 50%;
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
      }
    }
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes particleFloat {
  0% {
    transform: rotate(var(--angle)) translateX(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: rotate(var(--angle)) translateX(200px) scale(0);
    opacity: 0;
  }
}
</style>
```

## 📱 响应式设计

### 1. 移动端适配
```scss
// 响应式断点
$breakpoints: (
  xs: 480px,
  sm: 768px,
  md: 992px,
  lg: 1200px,
  xl: 1920px
);

// 移动端样式
@media (max-width: 768px) {
  .my-points-page {
    padding: 12px;
    
    .points-overview {
      .el-col {
        margin-bottom: 12px;
      }
    }
    
    .points-card {
      height: auto;
      min-height: 100px;
      
      .card-content {
        flex-direction: column;
        text-align: center;
        gap: 8px;
        
        .card-icon {
          width: 40px;
          height: 40px;
        }
        
        .card-value {
          font-size: 20px;
        }
      }
    }
  }
  
  .achievement-card {
    .achievement-icon {
      width: 48px;
      height: 48px;
    }
    
    .achievement-name {
      font-size: 14px;
    }
  }
}
```

### 2. 暗色主题适配
```scss
// 暗色主题变量
:root {
  --bg-color: #ffffff;
  --text-color: #303133;
  --border-color: #e4e7ed;
  --card-bg: #ffffff;
}

[data-theme="dark"] {
  --bg-color: #1a1a1a;
  --text-color: #e5eaf3;
  --border-color: #414243;
  --card-bg: #262727;
}

// 组件暗色主题样式
.points-card {
  background: var(--card-bg);
  border-color: var(--border-color);
  color: var(--text-color);
}
```

---

**总结**: 前端组件设计方案提供了完整的积分系统界面组件，包含页面组件、业务组件、基础组件和功能组件。设计遵循现有的Element Plus风格，确保了与现有系统的一致性，同时添加了丰富的交互效果和动画，提升了用户体验。所有组件都考虑了响应式设计和可访问性，确保在不同设备上都能良好运行。