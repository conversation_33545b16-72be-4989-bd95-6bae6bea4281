# Wiki文档状态管理优化总结

## 优化概述

根据用户要求，对Wiki文档状态管理系统进行了重要优化，确保`bi_document_status`表中一个状态类型+一个doc_id只绑定一行数据，采用upsert（更新或插入）的数据存储逻辑。

## 主要修改

### 1. 后端数据存储逻辑优化

#### DocumentStatusService.php 修改
- **修改位置**: `app/Core/Services/TchipWiki/DocumentStatusService.php`
- **核心变更**: 将`create()`改为`updateOrCreate()`
- **修改前**：每次都创建新记录，导致同一文档同一状态类型有多条记录
- **修改后**：使用upsert逻辑，确保一个状态类型+一个doc_id只有一条记录

```php
// 修改前
$newStatus = DocumentStatusModel::create([
    'doc_id' => $docId,
    'status_type' => $statusType,
    'status_value' => $statusValue,
    'created_by' => $operatorId,
    'expired_at' => $expiredAt,
    'reason' => $reason,
]);

// 修改后  
$newStatus = WikiDocumentStatusModel::updateOrCreate(
    [
        'doc_id' => $docId,
        'status_type' => $statusType,
    ],
    [
        'status_value' => $statusValue,
        'created_by' => $operatorId,
        'expired_at' => $expiredAt,
        'reason' => $reason,
    ]
);
```

#### 模型优化
- **文件**: `app/Model/TchipBi/WikiDocumentStatusModel.php`
- **表名修正**: 从`wiki_document_status`修正为`bi_document_status`
- **查询优化**: `getCurrentStatus`方法去掉`orderBy('created_at', 'desc')`，因为现在每个状态只有一条记录

#### 关联关系优化
- **文件**: `app/Model/TchipBi/WikiDocumentModel.php`  
- **修改**: 去掉状态关联中的`orderBy('created_at', 'desc')`排序
- **原因**: 现在每个状态类型只有一条记录，不需要按时间排序

### 2. 文档列表数据增强

#### TchipWikiService.php 修改
- **方法**: `getList()`
- **增强内容**: 
  - 加载文档状态关联数据：`auditStatus`, `premiumStatus`, `trainingStatus`
  - 为每个文档添加`status_tags`字段，包含状态标签信息

#### 状态标签逻辑
```php
// 添加文档状态标签
$item['status_tags'] = [];

// 审核状态
if (isset($item['audit_status']) && $item['audit_status']['status_value'] == 1) {
    $item['status_tags'][] = [
        'type' => 'audit',
        'label' => '已审核', 
        'color' => 'success'
    ];
}

// 精华认证
if (isset($item['premium_status']) && $item['premium_status']['status_value'] == 1) {
    $item['status_tags'][] = [
        'type' => 'premium',
        'label' => '精华',
        'color' => 'warning'
    ];
}

// 培训认证（含过期检查）
if (isset($item['training_status']) && $item['training_status']['status_value'] == 1) {
    $isExpired = false;
    if (isset($item['training_status']['expired_at'])) {
        $expiredAt = \Carbon\Carbon::parse($item['training_status']['expired_at']);
        $isExpired = $expiredAt->isPast();
    }
    
    if (!$isExpired) {
        $item['status_tags'][] = [
            'type' => 'training',
            'label' => '培训',
            'color' => 'info'
        ];
    }
}
```

### 3. 前端适配

#### 审核弹窗优化
- **文件**: `src/views/oa/Wiki/components/WikiDocumentAuditDialog.vue`
- **变更**: 用户已经移除了培训认证的过期时间选择功能
- **现状**: 支持"加10豆"、"精华"、"培训认证"三个选项的组合选择

## 数据结构优化效果

### 优化前
```
bi_document_status表：
doc_id | status_type | status_value | created_at
12345  | audit      | 0           | 2024-01-01 10:00:00
12345  | audit      | 1           | 2024-01-01 11:00:00  
12345  | premium    | 1           | 2024-01-01 12:00:00
12345  | premium    | 0           | 2024-01-01 13:00:00
```

### 优化后
```
bi_document_status表：
doc_id | status_type | status_value | created_at
12345  | audit      | 1           | 2024-01-01 11:00:00
12345  | premium    | 0           | 2024-01-01 13:00:00
```

## 业务逻辑保证

### 1. 数据一致性
- ✅ 每个文档的每种状态类型只有一条记录
- ✅ 状态变更时更新现有记录而不是新增
- ✅ 保留操作痕迹（操作人、时间、原因）

### 2. 事件触发
- ✅ 状态变更仍然触发积分事件
- ✅ 正确传递新旧状态值用于积分计算
- ✅ 支持多种状态的组合操作

### 3. 查询优化
- ✅ 去掉不必要的排序操作
- ✅ 简化状态查询逻辑
- ✅ 提升查询性能

## API接口兼容性

所有现有API接口保持兼容，包括：
- ✅ `setDocumentAuditStatus`
- ✅ `setDocumentPremiumStatus`  
- ✅ `setDocumentTrainingStatus`
- ✅ `batchAuditDocuments`
- ✅ 其他查询接口

## 前端数据格式

### getList接口返回格式增强
```json
{
  "data": [
    {
      "doc_id": 12345,
      "title": "示例文档",
      "status_tags": [
        {
          "type": "audit",
          "label": "已审核",
          "color": "success"
        },
        {
          "type": "premium", 
          "label": "精华",
          "color": "warning"
        }
      ],
      "audit_status": {
        "status_value": 1,
        "reason": "审核通过",
        "created_at": "2024-01-01 11:00:00"
      },
      "premium_status": {
        "status_value": 1,
        "reason": "精华文档认证",  
        "created_at": "2024-01-01 12:00:00"
      }
    }
  ]
}
```

## 测试建议

### 数据库层测试
- [ ] 验证upsert逻辑：同一文档同一状态类型只有一条记录
- [ ] 验证状态变更：更新现有记录而不是新增
- [ ] 验证级联关联：文档删除时状态记录的处理

### 业务逻辑测试  
- [ ] 审核流程：从待审核到通过/拒绝
- [ ] 认证流程：精华认证和培训认证的设置/取消
- [ ] 组合操作：同时设置多个状态的场景
- [ ] 积分触发：状态变更正确触发积分事件

### 前端界面测试
- [ ] 审核弹窗：三个选项的组合选择
- [ ] 文档列表：状态标签正确显示
- [ ] 权限控制：审核按钮的权限验证

## 影响范围

### 数据库
- ✅ `bi_document_status`表数据存储逻辑变更
- ✅ 现有数据需要清理重复记录（建议）

### 后端代码
- ✅ `DocumentStatusService.php` - 核心业务逻辑
- ✅ `WikiDocumentStatusModel.php` - 数据模型
- ✅ `WikiDocumentModel.php` - 关联关系  
- ✅ `TchipWikiService.php` - 文档列表增强

### 前端代码
- ✅ 审核弹窗组件已适配新逻辑
- ✅ API接口调用保持不变

## 部署注意事项

1. **数据迁移**：建议在部署前清理`bi_document_status`表中的重复记录
2. **缓存清理**：如有相关缓存需要清理
3. **监控观察**：部署后观察积分事件触发是否正常
4. **回滚准备**：如有问题可快速回滚到旧逻辑

## 优化收益

1. **数据质量**：确保数据一致性，避免重复记录
2. **查询性能**：减少不必要的排序和过滤操作
3. **存储空间**：减少冗余数据存储
4. **维护成本**：简化数据模型，降低维护复杂度
5. **业务清晰**：一个状态一条记录，业务逻辑更清晰