# Wiki用户等级标签集成总结

## 修改概述

在WikiContentEditor.vue文件中添加对LevelTag组件的使用，以便在显示用户信息时展示用户的知识豆等级标签。同时修改后端服务以提供必要的用户等级数据。

## 实现方案

### 后端修改

#### 1. 添加用户积分关联关系

在 `app/Model/TchipBi/UserModel.php` 文件中添加了 `userPoints` 关联方法：

```php
/**
 * 关联用户知识豆信息
 */
public function userPoints()
{
    return $this->hasOne(\App\Model\Points\UserPointsModel::class, 'user_id', 'id');
}
```

#### 2. 修改Wiki服务获取用户等级信息

在 `app/Core/Services/TchipWiki/TchipWikiService.php` 的 `getWikiCatalogList` 方法中修改了作者信息获取逻辑：

```php
// 获取创建者信息（包含用户等级信息）
$authorIds = array_column($paginate['data'], 'created_by');
$authorIds = array_unique($authorIds);
$authors = UserModel::with('userPoints:user_id,level,level_name')
    ->whereIn('id', $authorIds)
    ->get()
    ->keyBy('id');
$authors = $authors ? $authors->toArray() : [];
```

### 前端修改

#### 1. 引入LevelTag组件

在 `WikiContentEditor.vue` 中添加了LevelTag组件的引入：

```javascript
import LevelTag from '@/components/Points/LevelTag.vue'
```

#### 2. 在用户信息展示处添加等级标签

在三个位置添加了等级标签显示：

**富文本编辑器部分 - 更新人信息：**
```vue
<span class="user-name bi-wiki-content-editor-user-name">
  {{ updaterInfo.name }}&nbsp;
  <LevelTag
    v-if="updaterInfo.user_points && updaterInfo.user_points.level"
    :level="updaterInfo.user_points.level"
    :level-name="updaterInfo.user_points.level_name"
    variant="simple"
    size="small"
  />
</span>
```

**Markdown编辑器部分 - 更新人信息：**
同样的LevelTag组件配置

**创建人信息部分：**
```vue
<span class="user-name bi-wiki-content-editor-user-name">
  {{ authorInfo.name }}&nbsp;
  <LevelTag
    v-if="authorInfo.user_points && authorInfo.user_points.level"
    :level="authorInfo.user_points.level"
    :level-name="authorInfo.user_points.level_name"
    variant="simple"
    size="small"
  />
</span>
```

## 技术细节

### 数据结构

用户等级信息通过以下数据结构传递：

```javascript
{
  user_points: {
    level: 3,           // 等级数值
    level_name: "黄金"   // 等级名称
  }
}
```

### LevelTag组件配置

- `variant="simple"`：使用简洁样式，无背景色
- `size="small"`：使用小号尺寸，适合在用户名旁显示
- 只在有等级信息时才显示（使用 `v-if` 条件渲染）

## 显示效果

修改后的用户信息显示格式：
- **更新人信息**：`用户名 [等级标签] 更新于时间`
- **创建人信息**：`用户名 [等级标签] 创建于时间`

等级标签将以彩色标签的形式显示在用户名后，根据用户的知识豆等级显示不同的颜色和名称。

## 注意事项

1. **兼容性**：使用了条件渲染确保在没有等级信息时不会出错
2. **样式一致性**：使用 `simple` 变体保持界面简洁
3. **数据获取**：后端需要在获取文档详情时同时获取更新人的等级信息
4. **性能考虑**：通过关联查询一次性获取所有需要的用户等级信息

## 进一步扩展

如需进一步完善，可以考虑：
1. 在其他用户信息显示位置添加等级标签
2. 添加等级标签的点击事件，显示更详细的用户信息
3. 为等级标签添加悬停提示，显示等级详情
4. 优化数据获取策略，减少重复查询

## 完成时间

2025年7月28日