# Wiki文档状态管理系统实现总结

## 项目概述

本次实现为TChip BI项目的Wiki知识库模块添加了完整的文档状态管理系统，支持文档的审核、精华认证、培训认证等功能，并集成了积分奖励机制。

## 实现的功能

### 1. 数据库设计

#### 状态类型枚举表（bi_document_status_types）
- **作用**: 定义可扩展的状态类型
- **字段**:
  - `status_type`: 状态类型标识（主键）
  - `description`: 业务含义描述
  - `is_active`: 是否启用
- **预设状态类型**:
  - `premium`: 精华认证
  - `training`: 培训认证
  - `audit`: 审核状态

#### 文档状态记录表（bi_document_status）
- **作用**: 记录文档的状态变更历史
- **字段**:
  - `doc_id`: 文档ID
  - `status_type`: 状态类型
  - `status_value`: 状态值（0:待审核 1:通过 2:拒绝）
  - `created_by`: 操作人ID
  - `expired_at`: 过期时间（可选）
  - `reason`: 操作原因

### 2. 模型层实现

#### DocumentStatusTypeModel
- 状态类型枚举模型
- 提供状态类型的基础操作和验证

#### DocumentStatusModel
- 文档状态记录模型
- 支持状态历史查询、过期检查等功能

#### WikiDocumentModel扩展
- 新增状态关联关系
- 添加状态检查便捷方法（`isPremium()`, `isTraining()`, `isAuditApproved()`）

### 3. 服务层实现

#### DocumentStatusService
- **核心功能**:
  - 设置文档状态（支持精华认证、培训认证、审核）
  - 批量审核文档
  - 获取各类状态的文档列表
  - 状态历史查询
  - 过期检查

- **主要方法**:
  - `setDocumentStatus()`: 通用状态设置
  - `setPremiumStatus()`: 精华认证
  - `setTrainingStatus()`: 培训认证（支持过期时间）
  - `setAuditStatus()`: 审核状态
  - `batchAuditDocuments()`: 批量审核

### 4. 事件驱动积分系统

#### WikiDocumentStatusChangedEvent
- 文档状态变更事件
- 包含状态变更的详细信息

#### WikiDocumentPointsListener（已扩展）
- 原有的Wiki文档积分监听器，现已扩展支持状态变更
- **积分奖励逻辑**:
  - 审核通过：发放文档发布积分
  - 精华认证：发放精华文档积分
  - 培训认证：发放培训材料积分
  - 取消认证：相应扣除积分

## 业务流程

### 1. 文档发布流程
1. 用户创建文档（不自动获得积分）
2. 管理员审核文档
3. 审核通过后用户获得发布积分
4. 可选择申请精华认证或培训认证

### 2. 认证流程
1. 管理员评估文档质量
2. 设置相应的认证状态
3. 系统自动发放认证积分
4. 培训认证支持设置有效期

### 3. 积分机制
- **发布文档**: 审核通过后发放积分
- **精华认证**: 额外积分奖励
- **培训认证**: 额外积分奖励
- **取消认证**: 扣除相应积分（带源验证）

## 技术特性

### 1. 可扩展性
- 状态类型通过枚举表管理，新增状态无需修改表结构
- 支持状态过期机制
- 完整的操作历史记录

### 2. 数据完整性
- 外键约束确保数据一致性
- 复合索引优化查询性能
- 软删除支持

### 3. 事件驱动
- 状态变更通过事件系统处理
- 积分奖励与业务逻辑解耦
- 完整的日志记录

## 文件清单

### 数据库迁移文件
- `migrations/2025_08_01_100000_create_document_status_types_table.php`
- `migrations/2025_08_01_100001_create_document_status_table.php`

### 模型文件
- `app/Model/TchipBi/DocumentStatusTypeModel.php`
- `app/Model/TchipBi/DocumentStatusModel.php`
- `app/Model/TchipBi/WikiDocumentModel.php`（已扩展）

### 服务层文件
- `app/Core/Services/TchipWiki/DocumentStatusService.php`

### 事件系统文件
- `app/Event/Wiki/WikiDocumentStatusChangedEvent.php`
- `app/Listener/Wiki/WikiDocumentPointsListener.php`（已扩展）

### 控制器文件
- `app/Controller/TchipWiki/TchipWikiController.php`（已扩展）

## API接口说明

### 文档状态管理接口
控制器位置：`app/Controller/TchipWiki/TchipWikiController.php`

| 接口名称 | 方法 | 功能描述 |
|---------|------|----------|
| `setDocumentAuditStatus` | POST | 设置审核状态 |
| `setDocumentPremiumStatus` | POST | 设置精华认证 |
| `setDocumentTrainingStatus` | POST | 设置培训认证 |
| `batchAuditDocuments` | POST | 批量审核文档 |
| `getDocumentStatusHistory` | GET | 获取状态历史 |
| `getPendingAuditDocuments` | GET | 获取待审核文档 |
| `getPremiumDocuments` | GET | 获取精华文档 |
| `getTrainingDocuments` | GET | 获取培训文档 |
| `getExpiringTrainingDocuments` | GET | 获取即将过期培训文档 |
| `getDocumentStatusTags` | GET | 获取文档状态标签 |
| `canPublishDocument` | GET | 检查是否可发布 |

## 使用示例

### 通过API审核文档
```bash
# 审核通过
POST /tchip_wiki/index/setDocumentAuditStatus
{
    "doc_id": 123,
    "audit_status": 1,
    "reason": "内容规范，准予发布"
}

# 设置精华认证
POST /tchip_wiki/index/setDocumentPremiumStatus
{
    "doc_id": 123,
    "is_premium": true,
    "reason": "优质内容，评为精华"
}
```

### 服务层调用
```php
$statusService = new DocumentStatusService($container);

// 审核通过
$statusService->setAuditStatus(
    $docId, 
    DocumentStatusModel::STATUS_APPROVED, 
    $operatorId, 
    '内容规范，准予发布'
);

// 设置精华认证
$statusService->setPremiumStatus(
    $docId, 
    true, 
    $operatorId, 
    '优质内容，评为精华'
);
```

### 查询状态
```php
// 检查文档状态
$document = WikiDocumentModel::find($docId);
$isPremium = $document->isPremium();
$isAuditApproved = $document->isAuditApproved();

// 获取状态标签
$tags = $statusService->getDocumentStatusTags($docId);
```

## 部署说明

1. 运行数据库迁移：
   ```bash
   php bin/hyperf.php migrate
   ```

2. 确保事件监听器已注册（WikiDocumentPointsListener通过`@Listener`注解自动注册）

3. 配置积分类型（如需要新的积分类型）

## 注意事项

1. 使用`Carbon::now()`替代`now()`函数
2. 所有状态变更都会触发积分事件
3. 培训认证支持过期时间设置
4. 积分扣除时启用源验证，确保数据准确性
5. 复用了现有的WikiDocumentPointsListener，避免创建重复监听器
6. 所有接口都已集成到TchipWikiController中，保持代码组织一致性

## 后续扩展建议

1. 添加状态变更通知机制
2. 实现状态审批工作流
3. 增加批量操作界面
4. 添加状态统计报表功能