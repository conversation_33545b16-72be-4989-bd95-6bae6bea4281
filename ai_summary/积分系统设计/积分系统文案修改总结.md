# 积分系统文案修改总结 - "积分"改为"知识豆"

## 修改概述

根据最近三次积分系统相关提交记录，对前端项目中所有"积分"相关显示文本统一修改为"知识豆"，以提升用户体验和产品品牌一致性。

## 涉及的提交记录

1. **ba0fbafc** - `feat: 积分系统添加用户积分管理、积分配置、成就管理、等级管理 [issue-id=23487]`
2. **67b7ca6b** - `feat: 添加积分排行榜相关逻辑、显示 [issue-id=23487]`
3. **2ab7e880** - `feat: 新增知识库积分系统前端接口、布局、store(文档操作积分相关) [issue-id=23487]`

## 修改的文件清单

### 1. 页面视图文件 (8个)
- `src/views/setting/pointsManagement/index.vue` - 积分管理主页面
- `src/views/points/index.vue` - 积分系统主页面
- `src/views/points/rankings/index.vue` - 积分排行榜页面
- `src/views/points/records/index.vue` - 积分记录页面
- `src/views/points/achievements/index.vue` - 积分成就页面
- `src/views/setting/pointsManagement/components/LevelManagement.vue` - 等级管理组件
- `src/views/setting/pointsManagement/components/UserPointsManagement.vue` - 用户积分管理组件
- `src/views/setting/pointsManagement/components/AchievementManagement.vue` - 成就管理组件

### 2. 管理功能组件 (2个)
- `src/views/setting/pointsManagement/components/PointConfigManagement.vue` - 积分配置管理组件
- `src/views/points/components/PointsMenu.vue` - 积分菜单组件

### 3. API接口文件 (2个)
- `src/api/points/index.ts` - 积分API主文件
- `src/api/points/events.ts` - 积分事件API

### 4. 状态管理文件 (1个)
- `src/store/points/index.ts` - 积分状态管理

### 5. 类型定义文件 (3个)
- `src/types/points/index.ts` - 积分类型定义
- `src/types/points/events.ts` - 积分事件类型定义
- `src/types/points/achievements.ts` - 积分成就类型定义

### 6. 公共组件文件 (1个)
- `src/components/Points/index.ts` - 积分组件索引

### 7. 文档文件 (2个)
- `src/views/points/README.md` - 积分系统说明文档
- `src/views/setting/pointsManagement/STYLE_GUIDE.md` - 样式指南文档

### 8. 其他相关文件 (1个)
- `src/views/oa/Wiki/index.ts` - Wiki索引文件（包含积分相关内容）

## 修改详情

### 主要修改内容包括：

1. **UI界面文本**
   - "积分范围" → "知识豆范围"
   - "当前积分" → "当前知识豆"
   - "今日积分" → "今日知识豆"
   - "本周积分" → "本周知识豆"
   - "本月积分" → "本月知识豆"
   - "年度积分" → "年度知识豆"
   - "最后积分时间" → "最后知识豆时间"

2. **功能操作文本**
   - "批量添加积分" → "批量添加知识豆"
   - "批量扣除积分" → "批量扣除知识豆"
   - "批量初始化积分" → "批量初始化知识豆"
   - "调整积分" → "调整知识豆"
   - "增加积分" → "增加知识豆"
   - "扣除积分" → "扣除知识豆"

3. **表单验证文本**
   - "请输入最小积分" → "请输入最小知识豆"
   - "请输入最大积分" → "请输入最大知识豆"
   - "请输入积分数量" → "请输入知识豆数量"

4. **提示信息文本**
   - "等级系统基于用户的当前积分自动计算" → "等级系统基于用户的当前知识豆自动计算"
   - "积分范围不能重叠，系统会自动校验" → "知识豆范围不能重叠，系统会自动校验"
   - "等级升级时会自动发放升级奖励积分" → "等级升级时会自动发放升级奖励知识豆"

5. **消息反馈文本**
   - "积分调整成功" → "知识豆调整成功"
   - "积分调整失败" → "知识豆调整失败"
   - "获取用户积分数据失败" → "获取用户知识豆数据失败"

6. **文档和注释**
   - 更新了README.md和STYLE_GUIDE.md中的相关描述
   - 修改了代码注释中的"积分"用词

## 技术实现

使用批量替换方式，对每个文件执行：
```bash
# 全局替换所有"积分"为"知识豆"
sed -i 's/积分/知识豆/g' [文件路径]
```

## 验证结果

经过最终检查，所有文件中的"积分"都已成功替换为"知识豆"，不存在遗漏。

## 影响范围

此次修改仅涉及前端显示文本，不影响：
- 后端API接口
- 数据库字段名称
- 代码逻辑功能
- 数据结构

## 注意事项

1. 此次修改只是前端显示文案的调整，后端接口和数据库字段名称保持不变
2. 所有功能逻辑保持一致，只是用户看到的文字从"积分"变更为"知识豆"
3. 建议在后续开发中保持"知识豆"的命名统一性

## 完成时间

2025年7月28日