# 知识豆积分规则配置详细方案

## 🎯 规则设计目标

建立公平、合理、可持续的积分获取机制，既要激励用户积极参与，又要防止刷分行为，确保积分体系的健康发展。

## 📋 基础积分规则

### 1. 内容创作类积分规则

#### 1.1 发布知识文档 (+10知识豆)
```json
{
  "action_type": "publish_article",
  "base_points": 10,
  "conditions": {
    "min_content_length": 100,
    "required_fields": ["title", "content"],
    "auto_review": true
  },
  "bonuses": {
    "first_time_bonus": 5,
    "original_content_bonus": 5,
    "high_quality_bonus": {
      "criteria": "word_count > 1000 AND has_images",
      "points": 5
    }
  },
  "limitations": {
    "max_daily_count": null,
    "cooling_period": "10m"
  },
  "quality_multipliers": {
    "excellent": 2.0,
    "good": 1.5,
    "average": 1.0,
    "poor": 0.5
  }
}
```

#### 1.2 更新完善文档 (+3知识豆)
```json
{
  "action_type": "update_article",
  "base_points": 3,
  "conditions": {
    "min_change_percentage": 20,
    "significant_improvement": true
  },
  "limitations": {
    "max_daily_count": 10,
    "same_document_interval": "24h"
  }
}
```

#### 1.3 文档被删除 (-10知识豆)
```json
{
  "action_type": "delete_article",
  "base_points": -10,
  "conditions": {
    "deletion_reason": ["spam", "inappropriate", "duplicate"],
    "admin_confirmed": true
  },
  "appeal_mechanism": {
    "can_appeal": true,
    "appeal_period": "7d",
    "restore_points_if_successful": true
  }
}
```

### 2. 互动交流类积分规则

#### 2.1 文档获得点赞 (+1知识豆)
```json
{
  "action_type": "get_like",
  "base_points": 1,
  "conditions": {
    "liker_min_level": 1,
    "not_self_like": true,
    "unique_user": true
  },
  "limitations": {
    "max_daily_points": 50,
    "max_per_document": 100
  },
  "decay_rules": {
    "time_based": {
      "first_week": 1.0,
      "first_month": 0.8,
      "after_month": 0.5
    }
  }
}
```

#### 2.2 给他人点赞 (+0.5知识豆)
```json
{
  "action_type": "give_like",
  "base_points": 0.5,
  "conditions": {
    "target_content_quality": "good_or_above",
    "not_mutual_like_farming": true
  },
  "limitations": {
    "max_daily_count": 20,
    "max_daily_points": 10
  }
}
```

#### 2.3 发表评论 (+1知识豆)
```json
{
  "action_type": "comment_article",
  "base_points": 1,
  "conditions": {
    "min_comment_length": 20,
    "constructive_content": true,
    "not_spam": true
  },
  "bonuses": {
    "first_comment_on_document": 1,
    "detailed_comment_bonus": {
      "criteria": "length > 100 AND helpful_rating > 0.8",
      "points": 2
    }
  },
  "limitations": {
    "max_daily_count": 10,
    "same_document_limit": 3
  }
}
```

#### 2.4 评论被点赞 (+0.5知识豆)
```json
{
  "action_type": "comment_liked",
  "base_points": 0.5,
  "conditions": {
    "min_comment_quality": "helpful",
    "unique_liker": true
  },
  "limitations": {
    "max_daily_points": 20
  }
}
```

### 3. 特殊奖励类积分规则

#### 3.1 文档被加精 (+50知识豆)
```json
{
  "action_type": "add_essence",
  "base_points": 50,
  "conditions": {
    "admin_review": true,
    "quality_score": "> 4.5",
    "community_value": "high"
  },
  "bonuses": {
    "first_essence_bonus": 20,
    "multiple_essence_bonus": {
      "5_essence": 50,
      "10_essence": 100,
      "20_essence": 200
    }
  },
  "special_effects": {
    "level_acceleration": true,
    "featured_display": "7d"
  }
}
```

#### 3.2 线下培训认证 (+300知识豆)
```json
{
  "action_type": "offline_training",
  "base_points": 300,
  "conditions": {
    "hr_verification": true,
    "training_completion": true,
    "valid_certificate": true
  },
  "training_types": {
    "technical_training": 300,
    "management_training": 250,
    "soft_skills": 200,
    "certification_exam": 400
  },
  "limitations": {
    "max_daily_count": 1,
    "same_training_cooldown": "30d"
  }
}
```

#### 3.3 社区活动特别奖励 (+N知识豆)
```json
{
  "action_type": "special_reward",
  "base_points": "variable",
  "activity_types": {
    "monthly_sharing_master": {
      "points": 500,
      "criteria": "most_articles_in_month",
      "winner_count": 3
    },
    "tool_usage_master": {
      "points": 300,
      "criteria": "best_tool_documentation",
      "winner_count": 5
    },
    "community_contributor": {
      "points": 200,
      "criteria": "helpful_community_actions",
      "winner_count": 10
    },
    "knowledge_contest": {
      "points_range": [100, 500],
      "based_on_ranking": true
    }
  }
}
```

### 4. 日常行为类积分规则

#### 4.1 每日签到 (+2知识豆)
```json
{
  "action_type": "daily_checkin",
  "base_points": 2,
  "consecutive_bonuses": {
    "7_days": 5,
    "15_days": 10,
    "30_days": 20,
    "100_days": 50
  },
  "limitations": {
    "max_daily_count": 1,
    "reset_time": "00:00"
  }
}
```

#### 4.2 分享文档 (+2知识豆)
```json
{
  "action_type": "share_article",
  "base_points": 2,
  "share_channels": {
    "internal_team": 2,
    "external_platform": 3,
    "social_media": 1
  },
  "limitations": {
    "max_daily_count": 5,
    "same_document_limit": 2
  }
}
```

#### 4.3 完善个人资料 (+20知识豆)
```json
{
  "action_type": "complete_profile",
  "base_points": 20,
  "profile_sections": {
    "basic_info": 5,
    "skills_tags": 5,
    "bio_description": 5,
    "avatar_upload": 3,
    "contact_info": 2
  },
  "limitations": {
    "one_time_only": true,
    "update_bonus": 5
  }
}
```

## 🏆 等级提升奖励规则

### 等级升级积分奖励
```json
{
  "level_upgrade_rewards": {
    "bronze_to_silver": {
      "points": 20,
      "privileges": ["image_upload", "topic_create"],
      "badge": "silver_badge.png"
    },
    "silver_to_gold": {
      "points": 50,
      "privileges": ["file_upload", "essence_recommend"],
      "badge": "gold_badge.png"
    },
    "gold_to_diamond": {
      "points": 100,
      "privileges": ["moderator_rights", "featured_author"],
      "badge": "diamond_badge.png"
    },
    "diamond_to_legend": {
      "points": 200,
      "privileges": ["all_permissions", "exclusive_features"],
      "badge": "legend_badge.png"
    }
  }
}
```

## 🛡️ 防刷机制设计

### 1. 时间限制机制
```json
{
  "time_limitations": {
    "cooling_periods": {
      "publish_article": "10m",
      "comment_article": "1m",
      "give_like": "5s"
    },
    "daily_limits": {
      "max_articles": 10,
      "max_comments": 50,
      "max_likes_given": 100
    },
    "rate_limiting": {
      "requests_per_minute": 30,
      "burst_allowance": 10
    }
  }
}
```

### 2. 质量检测机制
```json
{
  "quality_controls": {
    "content_filters": {
      "min_word_count": 50,
      "duplicate_detection": true,
      "spam_keywords": ["刷分", "互赞", "水贴"],
      "ai_generated_detection": true
    },
    "behavior_analysis": {
      "suspicious_patterns": [
        "rapid_posting",
        "identical_content",
        "mutual_following",
        "bot_like_behavior"
      ],
      "penalty_actions": [
        "points_deduction",
        "temporary_suspension",
        "manual_review"
      ]
    }
  }
}
```

### 3. 用户信誉系统
```json
{
  "reputation_system": {
    "trust_score": {
      "new_user": 0.5,
      "verified_user": 1.0,
      "trusted_user": 1.5,
      "expert_user": 2.0
    },
    "point_multipliers": {
      "low_trust": 0.5,
      "normal_trust": 1.0,
      "high_trust": 1.2,
      "expert_trust": 1.5
    },
    "trust_factors": [
      "account_age",
      "contribution_quality",
      "community_feedback",
      "admin_verification"
    ]
  }
}
```

## 📊 动态调整机制

### 1. 积分价值平衡
```json
{
  "dynamic_balancing": {
    "difficulty_adjustment": {
      "easy_actions": {
        "threshold": "too_many_points_earned",
        "adjustment": "decrease_base_points"
      },
      "hard_actions": {
        "threshold": "too_few_participants",
        "adjustment": "increase_rewards"
      }
    },
    "seasonal_events": {
      "knowledge_week": {
        "multiplier": 2.0,
        "duration": "7d",
        "affected_actions": ["publish_article", "add_essence"]
      },
      "activity_month": {
        "bonus_points": 100,
        "participation_threshold": "10_actions"
      }
    }
  }
}
```

### 2. 市场经济模型
```json
{
  "economic_model": {
    "point_inflation_control": {
      "total_points_monitor": true,
      "inflation_threshold": "20%_monthly_growth",
      "deflation_mechanisms": [
        "point_expiry",
        "consumption_incentives",
        "premium_features"
      ]
    },
    "supply_demand_balance": {
      "high_demand_actions": "increase_point_cost",
      "low_participation_actions": "increase_rewards",
      "market_signals": [
        "user_activity_level",
        "content_quality_score",
        "community_health_index"
      ]
    }
  }
}
```

## 🎮 游戏化元素

### 1. 连续行为奖励
```json
{
  "streak_bonuses": {
    "daily_posting": {
      "3_days": 5,
      "7_days": 15,
      "15_days": 40,
      "30_days": 100
    },
    "weekly_goals": {
      "5_articles_per_week": 25,
      "10_comments_per_week": 15,
      "3_essence_articles": 75
    },
    "monthly_challenges": {
      "knowledge_master": {
        "requirement": "20_articles + 5_essence",
        "reward": 300
      },
      "community_helper": {
        "requirement": "100_helpful_comments",
        "reward": 200
      }
    }
  }
}
```

### 2. 随机奖励机制
```json
{
  "random_rewards": {
    "lucky_points": {
      "trigger_chance": 0.1,
      "point_range": [5, 50],
      "trigger_actions": ["publish_article", "get_like"]
    },
    "surprise_boxes": {
      "daily_chance": 0.05,
      "contents": [
        {"type": "points", "value": [10, 100]},
        {"type": "multiplier", "value": "2x_24h"},
        {"type": "badge", "value": "rare_badge"}
      ]
    }
  }
}
```

## 📈 积分统计分析

### 1. 用户行为分析
```json
{
  "analytics_tracking": {
    "user_journey": {
      "onboarding_completion": "profile_setup + first_article",
      "engagement_milestones": [10, 50, 100, 500, 1000],
      "retention_indicators": [
        "weekly_active",
        "monthly_active", 
        "content_creation_frequency"
      ]
    },
    "content_performance": {
      "article_success_metrics": [
        "view_count",
        "like_ratio",
        "comment_count",
        "share_count"
      ],
      "quality_indicators": [
        "time_spent_reading",
        "bounce_rate",
        "follow_up_actions"
      ]
    }
  }
}
```

### 2. 系统健康监控
```json
{
  "system_monitoring": {
    "point_distribution": {
      "gini_coefficient": "< 0.7",
      "top_1_percent_share": "< 30%",
      "median_user_points": "target_range"
    },
    "activity_health": {
      "daily_active_users": "trend_monitoring",
      "content_creation_rate": "quality_over_quantity",
      "community_interaction": "balanced_participation"
    },
    "economic_indicators": {
      "point_velocity": "circulation_speed",
      "value_stability": "purchasing_power",
      "user_satisfaction": "survey_feedback"
    }
  }
}
```

## 🔧 配置管理系统

### 1. 热更新配置
```json
{
  "hot_config_updates": {
    "point_values": {
      "updateable_without_restart": true,
      "effective_immediately": false,
      "grace_period": "24h"
    },
    "limits_and_restrictions": {
      "daily_limits": "immediate_effect",
      "cooling_periods": "next_action_effect",
      "quality_thresholds": "gradual_rollout"
    },
    "feature_toggles": {
      "new_point_types": "beta_users_first",
      "experimental_bonuses": "a_b_testing",
      "emergency_adjustments": "immediate_all_users"
    }
  }
}
```

### 2. 版本管理
```json
{
  "configuration_versioning": {
    "semantic_versioning": "major.minor.patch",
    "backward_compatibility": "3_versions",
    "migration_scripts": "automatic_upgrade",
    "rollback_capability": "emergency_revert",
    "change_log": {
      "user_facing_changes": "notification_required",
      "internal_optimizations": "admin_log_only",
      "bug_fixes": "changelog_entry"
    }
  }
}
```

---

**总结**: 该积分规则配置方案建立了完善的激励机制，既保证了系统的公平性和可持续性，又通过游戏化元素提升了用户参与度。配置系统支持热更新和版本管理，能够根据运营数据和用户反馈灵活调整规则。