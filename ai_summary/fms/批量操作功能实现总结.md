# FMS 批量操作功能实现总结

**实现日期**: 2025-01-30  
**功能优先级**: 🔴 高优先级（必须实现）  
**实现状态**: ✅ 已完成

## 功能概述

为 FMS 文件管理系统的 `UnifiedFileList.vue` 组件实现批量操作功能，包括批量选择、删除、移动、下载等功能，并包含权限验证机制。

## 实现范围

### 支持的批量操作
1. **批量选择** - 表格选择列，支持全选/反选
2. **批量下载** - 仅支持文件，目录不支持下载
3. **批量移动** - 支持文件和目录的批量移动
4. **批量删除** - 支持文件和目录的批量软删除

### 用户交互特性
- **动态工具栏**: 选中项目时显示批量操作工具栏
- **操作统计**: 显示已选择的项目数量
- **权限控制**: 基于可见性和用户权限进行操作限制
- **操作反馈**: 显示操作进度和结果统计
- **错误处理**: 部分成功时显示详细的成功/失败统计

## 前端实现

### 1. UnifiedFileList.vue 组件修改

#### 批量操作工具栏
```vue
<!-- 批量操作工具栏 -->
<div v-if="selectedItems.length > 0" class="unified-file-list__batch-toolbar">
  <div class="unified-file-list__batch-info">
    已选择 {{ selectedItems.length }} 项
  </div>
  <div class="unified-file-list__batch-actions">
    <el-button type="primary" @click="handleBatchDownload" :disabled="!canBatchDownload">
      批量下载
    </el-button>
    <el-button type="warning" @click="handleBatchMove">
      批量移动
    </el-button>
    <el-button type="danger" @click="handleBatchDelete">
      批量删除
    </el-button>
    <el-button type="text" @click="clearSelection">
      取消选择
    </el-button>
  </div>
</div>
```

#### 表格选择列
```vue
<el-table ref="tableRef" @selection-change="handleSelectionChange">
  <el-table-column type="selection" width="55" />
  <!-- 其他列 -->
</el-table>
```

#### 核心方法实现
```javascript
// 批量下载实现
const handleBatchDownload = async () => {
  const filesToDownload = selectedItems.value.filter(item => item.item_type === 'file')
  
  if (filesToDownload.length === 1) {
    // 单个文件直接下载
    await handleDownload(filesToDownload[0])
  } else {
    // 多个文件批量下载（逐个触发）
    for (const file of filesToDownload) {
      await handleDownload(file)
      await new Promise(resolve => setTimeout(resolve, 500)) // 避免浏览器阻止
    }
  }
}

// 批量删除实现
const performBatchDelete = async () => {
  // 分离文件和目录
  const files = selectedItems.value.filter(item => item.item_type === 'file')
  const directories = selectedItems.value.filter(item => item.item_type === 'directory')
  
  const results = []
  
  // 批量删除文件
  if (files.length > 0) {
    const fileIds = files.map(file => file.id)
    const fileResult = await fileApi.batchDelete(fileIds)
    results.push(...fileResult.results)
  }
  
  // 批量删除目录
  if (directories.length > 0) {
    const directoryIds = directories.map(dir => dir.id)
    const dirResult = await directoryApi.batchDelete(directoryIds)
    results.push(...dirResult.results)
  }
  
  // 统计结果并显示反馈
  const successCount = results.filter(r => r.success).length
  const failCount = results.filter(r => !r.success).length
  
  $baseMessage(
    `成功删除 ${successCount} 项${failCount > 0 ? `，失败 ${failCount} 项` : ''}`,
    successCount === selectedItems.value.length ? 'success' : 'warning'
  )
}
```

### 2. 权限控制
```javascript
// 是否可以批量下载（只有文件可以下载）
const canBatchDownload = computed(() => {
  return selectedItems.value.some(item => item.item_type === 'file')
})

// 权限验证在后端API层面进行
// 前端主要进行基础的类型检查和用户体验优化
```

## 后端实现

### 1. FileController 批量操作接口

#### 批量删除文件
```php
/**
 * 批量删除文件
 * @RequestMapping(path="batch/delete", methods="POST")
 */
public function batchDelete(RequestInterface $request)
{
    $params = $this->getFileParams($request, [
        'file_ids' => 'required|array',
        'file_ids.*' => 'integer|min:1',
    ]);

    $result = $this->fileService->batchDeleteFiles($params['file_ids']);
    return $this->response->success($result, '批量删除完成');
}
```

#### 批量移动文件
```php
/**
 * 批量移动文件
 * @RequestMapping(path="batch/move", methods="POST")
 */
public function batchMove(RequestInterface $request)
{
    $params = $this->getFileParams($request, [
        'file_ids' => 'required|array',
        'file_ids.*' => 'integer|min:1',
        'directory_id' => 'required|integer',
    ]);

    $result = $this->fileService->batchMoveFiles($params['file_ids'], $params['directory_id']);
    return $this->response->success($result, '批量移动完成');
}
```

### 2. FileService 批量操作实现

#### 批量删除服务
```php
public function batchDeleteFiles(array $fileIds): array
{
    $results = [];
    $successCount = 0;
    $failCount = 0;

    foreach ($fileIds as $fileId) {
        try {
            $this->deleteFile($fileId);
            $results[] = [
                'id' => $fileId,
                'success' => true,
                'message' => '删除成功'
            ];
            $successCount++;
        } catch (\Exception $e) {
            $results[] = [
                'id' => $fileId,
                'success' => false,
                'message' => $e->getMessage()
            ];
            $failCount++;
        }
    }

    return [
        'results' => $results,
        'summary' => [
            'total' => count($fileIds),
            'success' => $successCount,
            'failed' => $failCount
        ]
    ];
}
```

#### 批量移动服务
```php
public function batchMoveFiles(array $fileIds, int $targetDirectoryId): array
{
    $results = [];
    $successCount = 0;
    $failCount = 0;

    foreach ($fileIds as $fileId) {
        try {
            $this->moveFile($fileId, $targetDirectoryId);
            $results[] = [
                'id' => $fileId,
                'success' => true,
                'message' => '移动成功'
            ];
            $successCount++;
        } catch (\Exception $e) {
            $results[] = [
                'id' => $fileId,
                'success' => false,
                'message' => $e->getMessage()
            ];
            $failCount++;
        }
    }

    return [
        'results' => $results,
        'summary' => [
            'total' => count($fileIds),
            'success' => $successCount,
            'failed' => $failCount
        ]
    ];
}
```

### 3. DirectoryController 和 DirectoryService

同样实现了目录的批量删除和批量移动功能，API路径为：
- `POST /api/fms/directories/batch/delete`
- `POST /api/fms/directories/batch/move`

## API 接口规范

### 前端 API 调用

#### files.js 新增接口
```javascript
// 批量删除文件
export const batchDelete = (fileIds) => {
  return request({
    url: `${BASE_URL}/batch/delete`,
    method: 'POST',
    data: { file_ids: fileIds },
  })
}

// 批量移动文件
export const batchMove = (fileIds, directoryId) => {
  return request({
    url: `${BASE_URL}/batch/move`,
    method: 'POST',
    data: { file_ids: fileIds, directory_id: directoryId },
  })
}
```

#### directories.js 新增接口
```javascript
// 批量删除目录
export const batchDelete = (directoryIds) => {
  return request({
    url: `${BASE_URL}/batch/delete`,
    method: 'POST',
    data: { directory_ids: directoryIds },
  })
}

// 批量移动目录
export const batchMove = (directoryIds, parentId) => {
  return request({
    url: `${BASE_URL}/batch/move`,
    method: 'POST',
    data: { directory_ids: directoryIds, parent_id: parentId },
  })
}
```

## 技术特点

### 1. 用户体验
- **智能工具栏**: 仅在有选择时显示，节省界面空间
- **操作反馈**: 详细的成功/失败统计信息
- **防误操作**: 删除操作需要确认对话框
- **渐进式下载**: 多文件下载时逐个触发，避免浏览器限制

### 2. 性能优化
- **批量API**: 后端提供专门的批量操作接口
- **事务处理**: 每个操作独立处理，部分失败不影响其他操作
- **异步处理**: 前端异步处理批量操作，不阻塞界面

### 3. 错误处理
- **详细反馈**: 显示每个操作的成功/失败状态
- **部分成功**: 支持部分操作成功的场景
- **错误信息**: 提供具体的错误原因

## 测试建议

1. **功能测试**
   - 测试不同数量的批量选择（1个、多个、全选）
   - 测试混合选择（文件+目录）的批量操作
   - 测试权限限制下的批量操作

2. **边界测试**
   - 测试空选择的处理
   - 测试大量项目的批量操作性能
   - 测试网络异常时的错误处理

3. **用户体验测试**
   - 测试操作反馈的及时性和准确性
   - 测试工具栏的显示/隐藏逻辑
   - 测试取消选择的功能

## 后续优化建议

1. **进度显示**: 大量操作时显示进度条
2. **操作历史**: 记录批量操作的历史记录
3. **撤销功能**: 支持批量删除的撤销操作
4. **权限细化**: 更精细的权限控制机制

---

**实现完成**: ✅ 批量操作功能已完全实现并可正常使用
