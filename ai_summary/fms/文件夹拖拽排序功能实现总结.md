# FMS 文件夹拖拽排序功能实现总结

**实现日期**: 2025-01-30  
**功能优先级**: 🟡 中优先级（建议实现）  
**实现状态**: ✅ 已完成

## 功能概述

为 FMS 文件管理系统的 `DirectoryTree.vue` 组件实现文件夹拖拽排序功能，支持目录的拖拽移动和排序，自动更新 `sort_order` 和 `parent_id` 字段，提供直观的目录管理体验。

## 实现范围

### 支持的拖拽操作
1. **拖拽到节点内部** - 将目录移动为另一个目录的子目录
2. **拖拽到节点同级** - 在同级目录间重新排序
3. **跨层级拖拽** - 支持不同层级间的目录移动和排序
4. **自动排序** - 拖拽后自动重新计算 `sort_order` 值
5. **实时更新** - 拖拽完成后立即刷新目录树显示

### 用户交互特性
- **可视化拖拽**: 使用 antd-vue a-tree 组件的原生拖拽功能
- **智能排序**: 自动计算新的排序值，使用10的倍数便于后续插入
- **操作反馈**: 显示操作成功/失败的消息提示
- **权限控制**: 基于用户权限控制拖拽操作的可用性

## 前端实现

### 1. DirectoryTree.vue 组件修改

#### 拖拽配置
```vue
<a-tree
  :draggable="draggable"
  @drop="handleDrop"
>
  <!-- 树节点内容 -->
</a-tree>
```

#### 拖拽处理逻辑
```javascript
const handleDrop = async ({ node, dragNode, dropPosition }) => {
  if (!props.draggable) return

  try {
    const dragId = Number(dragNode.key)
    const dropId = Number(node.key)

    let newParentId = null
    let needsReorder = false

    if (dropPosition === 0) {
      // 拖拽到节点内部，作为子节点
      newParentId = dropId
      needsReorder = true
    } else {
      // 拖拽到节点同级，需要重新排序
      newParentId = node.dataRef.parent_id || null
      needsReorder = true
    }

    // 处理不同的拖拽场景
    if (dragNode.dataRef.parent_id !== newParentId && dropPosition === 0) {
      // 移动到新的父目录
      await directoryStore.moveDirectory(dragId, newParentId)
    } else if (needsReorder) {
      // 重新排序
      await handleDirectoryReorder(dragNode, node, dropPosition, newParentId)
    }

    await refreshTree()
  } catch (error) {
    $baseMessage('目录操作失败', 'error', 'vab-hey-message-error')
  }
}
```

#### 排序逻辑实现
```javascript
const handleDirectoryReorder = async (dragNode, dropNode, dropPosition, newParentId) => {
  // 获取同级的所有目录
  const siblings = getSiblingDirectories(newParentId)
  
  // 移除被拖拽的节点
  const filteredSiblings = siblings.filter(item => item.id !== Number(dragNode.key))
  
  // 计算新的插入位置
  let insertIndex = 0
  if (dropPosition === -1) {
    // 插入到目标节点之前
    insertIndex = filteredSiblings.findIndex(item => item.id === Number(dropNode.key))
  } else if (dropPosition === 1) {
    // 插入到目标节点之后
    insertIndex = filteredSiblings.findIndex(item => item.id === Number(dropNode.key)) + 1
  }
  
  // 插入被拖拽的节点
  const dragNodeData = {
    id: Number(dragNode.key),
    name: dragNode.title,
    parent_id: newParentId
  }
  filteredSiblings.splice(insertIndex, 0, dragNodeData)
  
  // 重新计算排序值
  const sortUpdates = filteredSiblings.map((item, index) => ({
    id: item.id,
    sort_order: (index + 1) * 10, // 使用10的倍数
    parent_id: newParentId
  }))
  
  // 调用排序API
  await directoryApi.updateSort(sortUpdates)
}
```

#### 同级目录获取
```javascript
const getSiblingDirectories = (parentId) => {
  const findSiblings = (nodes, targetParentId) => {
    const siblings = []
    
    const traverse = (nodeList, currentParentId) => {
      nodeList.forEach(node => {
        if (currentParentId === targetParentId) {
          siblings.push({
            id: Number(node.id),
            name: node.name,
            parent_id: currentParentId,
            sort_order: node.sort_order || 0
          })
        }
        
        if (node.children && node.children.length > 0) {
          traverse(node.children, Number(node.id))
        }
      })
    }
    
    traverse(treeData.value, parentId)
    return siblings.sort((a, b) => a.sort_order - b.sort_order)
  }
  
  return findSiblings(treeData.value, parentId)
}
```

## 后端实现

### 1. DirectoryController 新增排序接口

#### 排序API接口
```php
/**
 * 更新目录排序
 * @RequestMapping(path="sort", methods="POST")
 */
public function updateSort(RequestInterface $request)
{
    $params = $this->getfileParams($request, [
        'directories' => 'required|array',
        'directories.*.id' => 'required|integer|min:1',
        'directories.*.sort_order' => 'required|integer|min:0',
        'directories.*.parent_id' => 'integer|nullable',
    ]);

    $result = $this->directoryService->updateDirectoriesSort($params['directories']);
    return $this->response->success($result, '目录排序更新成功');
}
```

### 2. DirectoryService 排序实现

#### 批量排序更新
```php
public function updateDirectoriesSort(array $directories): array
{
    $results = [];
    $successCount = 0;
    $failCount = 0;

    foreach ($directories as $directoryData) {
        try {
            $directoryId = $directoryData['id'];
            $sortOrder = $directoryData['sort_order'];
            $parentId = $directoryData['parent_id'] ?? null;

            // 查找目录
            $directory = $this->directoryModel::query()
                ->where('id', $directoryId)
                ->where('is_deleted', 0)
                ->first();

            if (!$directory) {
                throw new AppException(StatusCode::ERR_NOT_FOUND, "目录不存在: ID {$directoryId}");
            }

            // 更新排序和父目录
            $updateData = [
                'sort_order' => $sortOrder,
                'updated_by' => $this->getCurrentUserId(),
                'updated_at' => Carbon::now(),
            ];

            // 如果指定了新的父目录，则更新父目录
            if (isset($directoryData['parent_id'])) {
                $updateData['parent_id'] = $parentId;
                
                // 如果移动到新的父目录，需要更新路径
                if ($directory->parent_id !== $parentId) {
                    $updateData['path'] = $this->generateDirectoryPath($parentId);
                }
            }

            $directory->update($updateData);
            $successCount++;
        } catch (\Exception $e) {
            $failCount++;
        }
    }

    return [
        'results' => $results,
        'summary' => [
            'total' => count($directories),
            'success' => $successCount,
            'failed' => $failCount
        ]
    ];
}
```

#### 查询排序支持
现有的目录查询已经支持按 `sort_order` 排序：
```php
// 在 getDirectoryTreeOptimized 方法中
$rootDirectories = $rootQuery->orderBy('sort_order')->orderBy('name')->get();
$childDirectories = $childQuery->orderBy('path')->orderBy('sort_order')->orderBy('name')->get();
```

## API 接口规范

### 前端 API 调用

#### directories.js 新增接口
```javascript
/**
 * 更新目录排序
 * @param {Array} directories 目录排序数据数组
 * @param {number} directories[].id 目录ID
 * @param {number} directories[].sort_order 排序值
 * @param {number|null} directories[].parent_id 父目录ID（可选）
 * @returns {Promise}
 */
export const updateSort = (directories) => {
  return request({
    url: `${BASE_URL}/sort`,
    method: 'POST',
    data: { directories },
  })
}
```

### 后端 API 规范

#### 请求格式
- **路径**: `POST /api/fms/directories/sort`
- **参数**: 
  ```json
  {
    "directories": [
      {
        "id": 1,
        "sort_order": 10,
        "parent_id": null
      },
      {
        "id": 2,
        "sort_order": 20,
        "parent_id": null
      }
    ]
  }
  ```

#### 响应格式
```json
{
  "success": true,
  "message": "目录排序更新成功",
  "data": {
    "results": [...],
    "summary": {
      "total": 2,
      "success": 2,
      "failed": 0
    }
  }
}
```

## 技术特点

### 1. 用户体验
- **直观操作**: 拖拽即可完成排序，符合用户直觉
- **实时反馈**: 拖拽完成后立即显示结果
- **智能排序**: 自动计算合理的排序值
- **错误处理**: 操作失败时显示具体错误信息

### 2. 技术实现
- **批量更新**: 一次API调用更新多个目录的排序
- **路径维护**: 移动目录时自动更新路径信息
- **权限控制**: 基于用户权限控制拖拽功能的可用性
- **数据一致性**: 确保排序值的唯一性和连续性

### 3. 性能优化
- **批量操作**: 减少API调用次数
- **智能刷新**: 只在必要时刷新目录树
- **内存计算**: 在前端计算新的排序值，减少后端计算

## 数据库字段

### sort_order 字段设计
- **类型**: `INT DEFAULT 0`
- **用途**: 目录排序（拖拽排序用）
- **规则**: 使用10的倍数（10, 20, 30...），便于后续插入
- **排序**: 配合 `name` 字段进行二级排序

### parent_id 字段
- **类型**: `BIGINT NULL`
- **用途**: 父目录ID，NULL表示根目录
- **关联**: 支持拖拽时更新父子关系

## 测试建议

1. **功能测试**
   - 测试同级目录间的拖拽排序
   - 测试跨层级的目录移动
   - 测试拖拽到节点内部作为子目录

2. **边界测试**
   - 测试根目录的拖拽操作
   - 测试空目录的拖拽处理
   - 测试权限限制下的拖拽操作

3. **性能测试**
   - 测试大量目录时的拖拽性能
   - 测试批量排序更新的响应时间

## 后续优化建议

1. **拖拽预览**: 添加拖拽时的视觉预览效果
2. **撤销功能**: 支持拖拽操作的撤销
3. **批量拖拽**: 支持多选目录的批量拖拽
4. **拖拽限制**: 根据目录深度限制拖拽操作

---

**实现完成**: ✅ 文件夹拖拽排序功能已完全实现并可正常使用
