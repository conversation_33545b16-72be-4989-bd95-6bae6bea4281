# FMS 高级搜索筛选面板实现总结

**实现日期**: 2025-01-30  
**功能优先级**: 🟡 中优先级（建议实现）  
**实现状态**: ✅ 已完成

## 功能概述

为 FMS 文件管理系统新建 `AdvancedSearchPanel.vue` 组件，实现文件格式、大小范围、时间范围、可见性等多维度筛选功能，提供更精确的文件搜索体验。

## 实现范围

### 支持的筛选条件
1. **文件格式筛选** - 基于 `mime_type` 字段，支持多选
   - 文档类：PDF、Word、Excel、PowerPoint、文本
   - 图片类：JPEG、PNG、GIF、WebP、SVG
   - 视频类：MP4、WebM、AVI、MOV
   - 音频类：MP3、WAV、OGG、AAC
   - 压缩包：ZIP、RAR、7Z、TAR

2. **文件大小范围** - 支持最小值/最大值设置
   - 单位选择：字节(B)、千字节(KB)、兆字节(MB)、吉字节(GB)
   - 自动单位转换为字节进行数据库查询

3. **时间范围筛选** - 支持日期时间范围选择
   - 时间类型：创建时间(`created_at`)、修改时间(`updated_at`)
   - 日期时间选择器，精确到分钟

4. **可见性筛选** - 基于 `visibility` 字段
   - 公开(`public`)、部门(`department`)、用户(`user`)、私有(`private`)

### 用户交互特性
- **可折叠面板**: 默认收起，点击展开/收起
- **实时搜索**: 点击搜索按钮立即应用筛选条件
- **一键重置**: 清空所有筛选条件并刷新数据
- **筛选状态持久化**: 筛选条件通过API参数传递

## 前端实现

### 1. AdvancedSearchPanel.vue 组件
**文件**: `tchip_bi_frontend/src/views/fms/components/AdvancedSearchPanel.vue`

#### 核心功能
```vue
<template>
  <div class="advanced-search-panel">
    <!-- 切换按钮 -->
    <div class="advanced-search-panel__toggle">
      <el-button @click="togglePanel">
        {{ expanded ? '收起筛选' : '高级筛选' }}
      </el-button>
    </div>

    <!-- 筛选表单 -->
    <el-collapse-transition>
      <div v-show="expanded" class="advanced-search-panel__content">
        <el-form :model="searchForm">
          <!-- 文件格式、大小、时间、可见性筛选 -->
        </el-form>
      </div>
    </el-collapse-transition>
  </div>
</template>
```

#### 数据结构
```javascript
const searchForm = reactive({
  fileTypes: [],        // 文件格式 MIME 类型数组
  sizeMin: '',          // 最小文件大小
  sizeMax: '',          // 最大文件大小
  sizeUnit: 'MB',       // 文件大小单位
  timeType: 'updated_at', // 时间类型
  timeRange: [],        // 时间范围 [开始时间, 结束时间]
  visibility: [],       // 可见性数组
})
```

#### 参数构建
```javascript
const buildSearchParams = () => {
  const params = {}

  // 文件格式筛选 - 合并多个MIME类型
  if (searchForm.fileTypes.length > 0) {
    const mimeTypes = searchForm.fileTypes.flatMap(type => type.split(','))
    params.mime_types = mimeTypes
  }

  // 文件大小筛选 - 转换为字节
  if (searchForm.sizeMin || searchForm.sizeMax) {
    const multiplier = { 'B': 1, 'KB': 1024, 'MB': 1024*1024, 'GB': 1024*1024*1024 }[searchForm.sizeUnit]
    if (searchForm.sizeMin) params.size_min = parseInt(searchForm.sizeMin) * multiplier
    if (searchForm.sizeMax) params.size_max = parseInt(searchForm.sizeMax) * multiplier
  }

  // 时间范围筛选
  if (searchForm.timeRange?.length === 2) {
    params.time_type = searchForm.timeType
    params.time_start = searchForm.timeRange[0]
    params.time_end = searchForm.timeRange[1]
  }

  // 可见性筛选
  if (searchForm.visibility.length > 0) {
    params.visibility = searchForm.visibility
  }

  return params
}
```

### 2. UnifiedFileList.vue 集成
**文件**: `tchip_bi_frontend/src/views/fms/components/UnifiedFileList.vue`

#### 组件集成
```vue
<template>
  <div class="unified-file-list">
    <!-- 高级搜索筛选面板 -->
    <AdvancedSearchPanel
      ref="advancedSearchRef"
      :searching="loading"
      @search="handleAdvancedSearch"
      @reset="handleSearchReset"
    />
    <!-- 文件列表表格 -->
  </div>
</template>
```

#### 事件处理
```javascript
// 高级搜索处理
const handleAdvancedSearch = (searchParams) => {
  advancedFilters.value = searchParams
  currentPage.value = 1 // 重置到第一页
  loadData()
}

// 搜索重置处理
const handleSearchReset = () => {
  advancedFilters.value = {}
  currentPage.value = 1
  loadData()
}

// API调用包含筛选参数
const params = {
  parent_id: props.directoryId,
  search: props.searchKeyword,
  page: currentPage.value,
  page_size: pageSize.value,
  union: 1,
  sort: sortField.value,
  order: sortOrder.value,
  ...advancedFilters.value, // 合并高级搜索筛选条件
}
```

## 后端实现

### 1. FileController 修改
**文件**: `tchip_bi_backend/app/Controller/Fms/FileController.php`

#### 参数验证
```php
$params = $this->getFileParams($request, [
    // 基础参数
    'directory_id' => 'integer|nullable',
    'search' => 'string|nullable|max:255',
    'page' => 'integer|min:1',
    'page_size' => 'integer|min:1|max:100',
    'sort' => 'string|nullable|in:name,size,created_at,updated_at',
    'order' => 'string|nullable|in:asc,desc',
    // 高级搜索参数
    'mime_types' => 'array|nullable',
    'size_min' => 'integer|nullable|min:0',
    'size_max' => 'integer|nullable|min:0',
    'time_type' => 'string|nullable|in:created_at,updated_at',
    'time_start' => 'string|nullable|date',
    'time_end' => 'string|nullable|date',
    'visibility' => 'array|nullable',
]);
```

#### 筛选条件构建
```php
// 构建高级搜索筛选条件
$filters = [];
if (!empty($params['mime_types'])) {
    $filters['mime_types'] = $params['mime_types'];
}
if (isset($params['size_min'])) {
    $filters['size_min'] = $params['size_min'];
}
if (isset($params['size_max'])) {
    $filters['size_max'] = $params['size_max'];
}
if (!empty($params['time_type']) && !empty($params['time_start']) && !empty($params['time_end'])) {
    $filters['time_type'] = $params['time_type'];
    $filters['time_start'] = $params['time_start'];
    $filters['time_end'] = $params['time_end'];
}
if (!empty($params['visibility'])) {
    $filters['visibility'] = $params['visibility'];
}
```

### 2. FileService 修改
**文件**: `tchip_bi_backend/app/Core/Services/Fms/FileService.php`

#### 筛选条件应用
```php
/**
 * 应用高级搜索筛选条件
 */
private function applyAdvancedFilters($query, array $filters): void
{
    // MIME类型筛选
    if (!empty($filters['mime_types'])) {
        $query->whereIn('mime_type', $filters['mime_types']);
    }

    // 文件大小筛选
    if (isset($filters['size_min'])) {
        $query->where('size', '>=', $filters['size_min']);
    }
    if (isset($filters['size_max'])) {
        $query->where('size', '<=', $filters['size_max']);
    }

    // 时间范围筛选
    if (!empty($filters['time_type']) && !empty($filters['time_start']) && !empty($filters['time_end'])) {
        $timeField = $filters['time_type'];
        $query->whereBetween($timeField, [$filters['time_start'], $filters['time_end']]);
    }

    // 可见性筛选
    if (!empty($filters['visibility'])) {
        $query->whereIn('visibility', $filters['visibility']);
    }
}
```

### 3. DirectoryService 联合查询支持
**文件**: `tchip_bi_backend/app/Core/Services/Fms/DirectoryService.php`

联合查询中的筛选条件仅应用于文件查询，目录查询不受影响，确保目录结构的完整性。

## 技术特点

### 1. 用户体验
- **渐进式展示**: 默认收起，按需展开
- **分组选项**: 文件格式按类型分组显示
- **智能转换**: 文件大小单位自动转换
- **实时反馈**: 搜索状态显示和加载提示

### 2. 性能优化
- **数据库层筛选**: 在数据库层面应用筛选条件
- **索引友好**: 筛选字段建议添加数据库索引
- **分页结合**: 筛选与分页结合，减少数据传输

### 3. 扩展性
- **模块化设计**: 筛选条件独立封装
- **配置化选项**: 文件格式选项可配置
- **API标准化**: 遵循RESTful API设计规范

## 测试建议

1. **功能测试**
   - 测试各个筛选条件的单独使用
   - 测试多个筛选条件的组合使用
   - 测试筛选条件的重置功能

2. **边界测试**
   - 测试文件大小的极值输入
   - 测试时间范围的边界情况
   - 测试空筛选条件的处理

3. **性能测试**
   - 测试大量数据时的筛选性能
   - 测试复杂筛选条件的查询效率

## 后续优化建议

1. **预设筛选**: 提供常用筛选条件的快捷选项
2. **筛选历史**: 记住用户的筛选偏好
3. **筛选统计**: 显示各筛选条件下的文件数量
4. **导出功能**: 支持筛选结果的导出

---

**实现完成**: ✅ 高级搜索筛选面板功能已完全实现并可正常使用
