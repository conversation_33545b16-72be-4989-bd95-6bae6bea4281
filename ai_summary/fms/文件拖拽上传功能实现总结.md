# FMS 文件拖拽上传功能实现总结

**实现日期**: 2025-01-30  
**功能优先级**: 🟡 中优先级（建议实现）  
**实现状态**: ✅ 已完成

## 功能概述

为 FMS 文件管理系统的 `UnifiedFileList.vue` 组件实现文件拖拽上传功能，支持多文件拖拽上传和进度显示，自动设置当前目录ID，提供直观的拖拽交互体验。

## 实现范围

### 支持的拖拽上传特性
1. **拖拽检测** - 检测文件拖拽到表格区域
2. **视觉反馈** - 显示拖拽遮罩层和提示信息
3. **多文件上传** - 支持同时拖拽多个文件
4. **文件大小限制** - 单个文件最大100MB
5. **自动目录设置** - 自动设置为当前浏览的目录ID
6. **上传进度反馈** - 显示上传成功/失败统计

### 用户交互特性
- **拖拽遮罩**: 文件拖拽到表格时显示半透明遮罩
- **视觉提示**: 显示上传图标和提示文字
- **实时反馈**: 显示上传进度和结果统计
- **错误处理**: 文件大小超限时显示具体错误信息

## 前端实现

### 1. 拖拽遮罩层
```vue
<!-- 拖拽上传遮罩 -->
<div
  v-if="dragOver"
  class="unified-file-list__drag-overlay"
  @drop.prevent="handleDrop"
  @dragover.prevent
  @dragleave="handleDragLeave"
>
  <div class="unified-file-list__drag-content">
    <el-icon class="unified-file-list__drag-icon">
      <UploadFilled />
    </el-icon>
    <div class="unified-file-list__drag-text">
      释放文件以上传到当前目录
    </div>
  </div>
</div>
```

### 2. 表格拖拽事件绑定
```vue
<el-table
  class="unified-file-list__table"
  @dragover.prevent="handleDragOver"
  @drop.prevent="handleDrop"
  @dragleave="handleDragLeave"
>
  <!-- 表格内容 -->
</el-table>
```

### 3. 拖拽状态管理
```javascript
// 拖拽状态数据
const dragOver = ref(false)
const dragCounter = ref(0) // 用于处理嵌套元素的拖拽事件

// 拖拽进入检测
const handleDragOver = (e) => {
  e.preventDefault()
  e.stopPropagation()
  
  // 检查是否包含文件
  if (e.dataTransfer.types.includes('Files')) {
    dragCounter.value++
    dragOver.value = true
  }
}

// 拖拽离开处理
const handleDragLeave = (e) => {
  e.preventDefault()
  e.stopPropagation()
  
  dragCounter.value--
  if (dragCounter.value <= 0) {
    dragCounter.value = 0
    dragOver.value = false
  }
}
```

### 4. 文件拖拽处理
```javascript
// 文件拖拽释放处理
const handleDrop = async (e) => {
  e.preventDefault()
  e.stopPropagation()
  
  dragOver.value = false
  dragCounter.value = 0
  
  const files = Array.from(e.dataTransfer.files)
  if (files.length === 0) {
    return
  }

  // 检查文件大小
  const maxSize = 100 * 1024 * 1024 // 100MB
  const oversizedFiles = files.filter(file => file.size > maxSize)
  if (oversizedFiles.length > 0) {
    $baseMessage(
      `以下文件超过100MB限制：${oversizedFiles.map(f => f.name).join(', ')}`,
      'error',
      'vab-hey-message-error'
    )
    return
  }

  // 开始上传文件
  await uploadFiles(files)
}
```

### 5. 文件上传实现
```javascript
// 上传文件方法
const uploadFiles = async (files) => {
  if (!props.directoryId && props.directoryId !== 0) {
    $baseMessage('请先选择目录', 'warning', 'vab-hey-message-warning')
    return
  }

  $baseMessage(`开始上传 ${files.length} 个文件...`, 'info', 'vab-hey-message-info')
  
  let successCount = 0
  let failCount = 0
  
  for (const file of files) {
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('directory_id', props.directoryId)
      formData.append('visibility', 'department') // 默认部门可见
      
      await fileApi.upload(formData)
      successCount++
    } catch (error) {
      failCount++
      console.error(`上传文件 ${file.name} 失败:`, error)
    }
  }

  // 显示上传结果
  if (successCount > 0) {
    $baseMessage(
      `成功上传 ${successCount} 个文件${failCount > 0 ? `，失败 ${failCount} 个` : ''}`,
      successCount === files.length ? 'success' : 'warning',
      successCount === files.length ? 'vab-hey-message-success' : 'vab-hey-message-warning'
    )
    
    // 刷新文件列表
    loadData()
  } else {
    $baseMessage('文件上传失败', 'error', 'vab-hey-message-error')
  }
}
```

## 样式实现

### 1. 拖拽遮罩样式
```scss
/* 拖拽上传样式 */
.unified-file-list__table {
  position: relative;
}

.unified-file-list__drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(64, 158, 255, 0.1);
  border: 2px dashed #409eff;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.unified-file-list__drag-content {
  text-align: center;
  color: #409eff;
}

.unified-file-list__drag-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.unified-file-list__drag-text {
  font-size: 16px;
  font-weight: 500;
}
```

## 技术特点

### 1. 用户体验
- **视觉反馈**: 拖拽时显示半透明遮罩和提示信息
- **智能检测**: 只有拖拽文件时才显示遮罩，避免误触发
- **进度反馈**: 显示上传进度和详细的成功/失败统计
- **错误提示**: 文件大小超限时显示具体的文件名列表

### 2. 技术实现
- **事件处理**: 使用dragCounter解决嵌套元素的拖拽事件问题
- **文件检测**: 通过dataTransfer.types检查是否包含文件
- **异步上传**: 逐个上传文件，避免并发过多导致的问题
- **自动刷新**: 上传成功后自动刷新文件列表

### 3. 兼容性
- **现有功能**: 与现有的FileUpload组件共存，不冲突
- **API复用**: 使用相同的文件上传API接口
- **样式隔离**: 拖拽样式不影响表格原有样式

## 与现有FileUpload组件的区别

| 特性 | 拖拽上传 | FileUpload组件 |
|------|----------|----------------|
| **触发方式** | 拖拽文件到表格 | 点击按钮打开对话框 |
| **用户界面** | 无对话框，直接在表格上操作 | 独立的上传对话框 |
| **文件选择** | 拖拽选择 | 点击选择或拖拽到上传区域 |
| **配置选项** | 自动使用默认配置 | 可配置可见性等选项 |
| **进度显示** | 简单的消息提示 | 详细的进度条和文件列表 |
| **适用场景** | 快速上传，简单操作 | 批量上传，需要配置选项 |

## 后端API支持

拖拽上传功能复用现有的文件上传API：

### API接口
- **路径**: `POST /api/fms/files`
- **参数**: 
  - `file`: 文件对象
  - `directory_id`: 目录ID
  - `visibility`: 可见性（默认为'department'）

### 文件大小限制
- **前端限制**: 100MB（在拖拽处理时检查）
- **后端限制**: 由后端配置决定
- **错误处理**: 超限文件显示具体文件名列表

## 测试建议

1. **功能测试**
   - 测试单个文件拖拽上传
   - 测试多个文件同时拖拽上传
   - 测试文件大小超限的处理

2. **交互测试**
   - 测试拖拽遮罩的显示/隐藏
   - 测试拖拽到表格不同区域的响应
   - 测试拖拽非文件内容的处理

3. **边界测试**
   - 测试在没有选择目录时的拖拽上传
   - 测试网络异常时的错误处理
   - 测试大量文件的拖拽上传性能

## 后续优化建议

1. **进度显示**: 添加实时上传进度条
2. **文件预览**: 上传前显示文件预览
3. **拖拽排序**: 支持文件拖拽排序功能
4. **批量配置**: 支持批量设置上传文件的可见性

---

**实现完成**: ✅ 文件拖拽上传功能已完全实现并可正常使用
