### FMS 前端组件开发计划（归档版）

作者：qinsx

本文件为 `/doc/fms/FMS-前端组件开发计划.md` 的归档副本，便于在项目根 `ai_summary` 下集中查看。请以 `doc` 版本为主进行编辑。

关键要点摘录：
- 目录结构：`src/views/fms` 下分目录、文件、标签、ACL、分享、回收站、日志、common、stores、types
- 规范约束：$baseMessage、$baseConfirm、firefly-dialog、VabQueryForm、BEM 命名与文件名关联、await 延时
- API 模块：`src/api/fms`（directories/files/tags/acl/shares/recycle/logs），Axios 统一封装与类型定义
- 权限：permissionStore + usePermission + v-permission + 路由守卫（位掩码：view/upload/delete/share）
- 时间线：6 周推进，先基建后模块，最后联调与验收

完整内容请见：`/doc/fms/FMS-前端组件开发计划.md`

© 2025 tchipbi | 作者：qinsx


