# TChip BI 项目架构分析报告

## 项目总体概述

TChip BI 是一个完整的企业级商业智能和管理平台，采用前后端分离架构：

- **前端**：Vue 3 + Element Plus + TypeScript
- **后端**：Hyperf 2.2 + PHP 7.4+ + MySQL + Redis
- **部署**：支持多环境部署（dev/pre/master）

## 1. 前端架构分析 (tchip_bi_frontend)

### 1.1 技术栈
- **框架**：Vue 3 + Composition API
- **构建工具**：Vue CLI 5 + Webpack 5
- **UI 框架**：Element Plus + Ant Design Vue + Arco Design
- **状态管理**：Pinia
- **路由**：Vue Router 4
- **类型检查**：TypeScript
- **图表库**：ECharts
- **编辑器**：WangEditor + Vditor (Markdown)

### 1.2 目录结构分析

#### 1.2.1 主要功能模块 (src/views)

**办公管理模块 (oa/)**
- **Wiki 知识库** (`oa/Wiki/`)
  - 文档管理、空间管理、目录管理
  - 权限控制、评论点赞、历史版本
  - 导入导出、全文搜索
- **审批流程** (`oa/approval/`)
- **资产管理** (`oa/assetsMgt/`)
- **借用管理** (`oa/borrow/`)
- **质检管理** (`oa/qc/`)
- **报表系统** (`oa/report/`)

**项目管理模块 (project/)**
- **事项管理** (`project/issue/`)
  - 事项详情、子事项、关联关系
  - 批量操作、复制功能、模板系统
- **项目总览** (`project/project/`)
- **测试用例** (`project/testCase/`)
- **版本管理** (`project/version/`)
- **工作流模板** (`project/flowtpl/`)

**产品管理模块 (product/)**
- **产品详情** (`product/details/`)
- **变更记录** (`product/changeRecord/`)
- **跟进管理** (`product/follow/`)

**生产管理模块 (production/)**
- **生产订单** (`production/production/`)
- **装配订单** (`production/assembleOrder/`)
- **异常记录** (`production/exception/`)
- **出货管理** (`production/shipment/`)

**营销管理模块 (marketing/)**
- **站点PC管理** (`marketing/stationpc/`)

**系统设置模块 (setting/)**
- **用户管理** (`setting/userManagement/`)
- **角色管理** (`setting/roleManagement/`)
- **菜单管理** (`setting/menuManagement/`)
- **部门管理** (`setting/departmentManagement/`)

#### 1.2.2 组件架构
- **公共组件** (`src/components/`)：可复用业务组件
- **布局组件** (`library/layouts/`)：多种布局模式支持
- **工具库** (`library/`)：自定义指令、插件、样式

### 1.3 前端特色功能
1. **多 UI 框架共存**：Element Plus、Ant Design Vue、Arco Design
2. **富文本编辑器**：WangEditor + Markdown 支持
3. **权限管理**：基于角色的访问控制 (RBAC)
4. **多种布局**：垂直、水平、综合等布局模式
5. **图表系统**：ECharts 集成
6. **代码生成**：Plop 模板生成

## 2. 后端架构分析 (tchip_bi_backend)

### 2.1 技术栈
- **框架**：Hyperf 2.2 (高性能 PHP 协程框架)
- **数据库**：MySQL 5.7+ (支持多数据库连接)
- **缓存**：Redis 5.0+ (模型缓存和数据缓存)
- **队列**：异步队列支持
- **认证**：JWT + hyperf-auth
- **文档生成**：支持 Markdown、PDF 生成

### 2.2 核心目录结构

#### 2.2.1 控制器层 (app/Controller)

**知识库相关控制器**
- `TchipWiki/TchipWikiController.php`：Wiki 核心功能
  - Wiki 文档管理 (CRUD、搜索、导出)
  - Wiki 空间管理
  - Wiki 目录管理
  - 权限和成员管理
  - 评论点赞系统

**项目管理控制器**
- `Project/IssueController.php`：事项管理
- `Project/ProjectController.php`：项目管理
- `Project/WikiController.php`：项目Wiki
- `Project/CheckList/CheckListController.php`：检查清单

**用户和权限控制器**
- `UserController.php`：用户管理
- `Setting/UserManagementController.php`：用户管理设置
- `Setting/RoleManagementController.php`：角色管理

#### 2.2.2 数据模型层 (app/Model)

**Wiki 相关模型**
- `TchipBi/WikiDocumentModel.php`：Wiki 文档
- `TchipBi/WikiSpaceModel.php`：Wiki 空间
- `TchipBi/WikiCatalogModel.php`：Wiki 目录
- `TchipBi/WikiCommentModel.php`：Wiki 评论
- `TchipBi/WikiLikeModel.php`：Wiki 点赞

**用户相关模型**
- `TchipBi/UserModel.php`：用户基础信息
- `Redmine/UserModel.php`：Redmine 用户同步
- `User/User.php`：用户核心模型

### 2.3 数据库设计分析

#### 2.3.1 Wiki 知识库相关表

**核心表结构**
1. **wiki_spaces** - Wiki 空间表
   - 空间名称、描述、公开性设置
   - 文档数量统计、创建人信息

2. **wiki_documents** - Wiki 文档表
   - 支持 HTML 和 Markdown 双格式
   - 层级关系、路径管理
   - 浏览数、点赞数统计

3. **wiki_catalogs** - Wiki 目录表
   - 树形结构支持
   - 排序功能

4. **wiki_user_groups** - 用户群组表
5. **wiki_space_members** - 空间成员表
6. **wiki_comments** - 评论表
7. **wiki_likes** - 点赞表

#### 2.3.2 用户相关表
- **users** - 用户基础信息
- **user_department** - 用户部门关系
- **auth_group** - 角色组
- **auth_menu** - 菜单权限

#### 2.3.3 项目管理相关表
- **issues** - 事项表
- **projects** - 项目表
- **issue_templates** - 事项模板
- **checklists** - 检查清单

### 2.4 多数据库支持
系统支持多个数据库连接：
- **default**：主数据库
- **tchip_redmine**：Redmine 系统集成
- **tchip_sale**：销售系统
- **tchip_oa**：OA 系统
- **tchip_bbs**：BBS 系统

## 3. 知识库功能架构分析

### 3.1 知识库前端组件结构

**主要组件** (tchip_bi_frontend/src/views/oa/Wiki/)
- `index.vue`：Wiki 主入口，路由切换
- `WikiDetail.vue`：文档详情页
- `components/WikiContent.vue`：文档内容显示
- `components/WikiMenu.vue`：导航菜单
- `components/WikiMySpace.vue`：我的空间
- `components/WikiAllDocument.vue`：所有文档

### 3.2 知识库后端服务架构

**控制器功能** (TchipWikiController.php)
1. **文档管理**
   - 文档 CRUD 操作
   - 批量编辑
   - 全局搜索
   - PDF 导出

2. **空间管理**
   - 空间创建、编辑、删除
   - 权限控制

3. **目录管理**
   - 树形结构维护
   - 拖拽排序

4. **权限管理**
   - 用户群组管理
   - 成员权限分配

5. **交互功能**
   - 评论回复系统
   - 点赞统计
   - 浏览统计

### 3.3 知识库数据流
1. **文档编辑流程**：前端编辑器 → API 提交 → 版本控制 → 数据库存储
2. **权限验证流程**：用户请求 → JWT 验证 → 权限检查 → 资源访问
3. **搜索流程**：关键词输入 → 全文检索 → 结果排序 → 权限过滤

## 4. 用户和权限架构

### 4.1 用户管理体系
- **用户基础信息**：存储在 users 表
- **部门关系**：user_department 表管理
- **角色权限**：基于 RBAC 模型

### 4.2 权限控制机制
1. **菜单权限**：auth_menu 表控制页面访问
2. **数据权限**：基于用户、角色、部门的数据过滤
3. **操作权限**：控制器级别的权限验证

## 5. 系统集成和扩展

### 5.1 第三方系统集成
- **Redmine 集成**：项目同步、问题跟踪
- **企业微信集成**：消息推送、审批流程
- **ERP 系统集成**：生产订单、库存管理

### 5.2 技术特色
1. **热重载开发**：后端支持 watch 模式
2. **队列系统**：异步任务处理
3. **多租户支持**：支持多系统集成
4. **缓存优化**：Redis 模型缓存

## 6. 部署和环境

### 6.1 环境配置
- **开发环境**：172.16.0.253:8057 (后端) / 172.16.0.253:2101 (前端)
- **预发布环境**：172.16.0.251:9057 (后端) / 172.16.0.251:3101 (前端)
- **生产环境**：bi.t-firefly.com:8057 (后端) / bi.t-firefly.com:2101 (前端)

### 6.2 Git 工作流
- **master**：生产分支
- **pre**：预发布分支
- **dev**：开发分支
- **feature-***：功能开发分支

## 7. 总结和建议

### 7.1 架构优势
1. **技术栈现代化**：Vue 3 + Hyperf 协程框架
2. **功能完整性**：覆盖项目管理、知识库、生产管理等核心业务
3. **扩展性良好**：支持多数据库、多系统集成
4. **权限体系完善**：基于 RBAC 的细粒度权限控制

### 7.2 架构特点
1. **知识库功能丰富**：支持多格式编辑、版本控制、权限管理
2. **用户体验优秀**：多种布局、富文本编辑、实时搜索
3. **开发效率高**：代码生成、热重载、组件化开发
4. **数据安全可靠**：多层权限验证、软删除机制

### 7.3 发展方向
1. **移动端支持**：已有移动端模块，可进一步完善
2. **AI 集成**：可考虑集成 AI 辅助功能
3. **性能优化**：继续优化缓存策略和数据库性能
4. **微服务架构**：随着业务增长可考虑服务拆分

---

*报告生成时间：2025-07-12*  
*项目版本：当前开发版本*  
*分析范围：前后端完整架构*