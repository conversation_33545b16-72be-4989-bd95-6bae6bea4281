# TChipBI 数据库结构分析报告 - 积分系统设计参考

## 分析目标
通过分析tchipbi后端现有的数据库迁移文件和模型结构，为积分系统设计提供详细的参考信息。

## 1. 用户表(users)结构分析

### 1.1 基础用户表字段
基于`/app/Model/User/User.php`模型分析，用户表包含以下核心字段：

- **主键**: `id` (integer)
- **企业微信字段**: `userid` (成员UserID), `name`, `mobile`, `department`, `position`
- **认证字段**: `password`, `salt`
- **个人信息**: `gender`, `email`, `avatar`, `telephone`, `address`
- **组织关系**: `department` (array), `is_leader_in_dept` (array), `direct_leader` (array)
- **状态字段**: `status` (integer)
- **时间戳**: `created_at`, `updated_at`, `deleted_at`

### 1.2 用户扩展字段
通过迁移文件分析发现的扩展字段：

- **个人偏好设置** (`2023_11_07_091907_commit_user_table.php`):
  - `product_detail_opening_mode` (tinyInteger): 产品详情打开模式偏好

- **用户设置** (`2024_02_21_055305_commit_user_settings_user_table.php`):
  - `user_settings` (json): 用户偏好设置JSON

- **系统状态** (`2024_04_26_144542_commit_bi_status_user_table.php`):
  - `bi_status` (tinyInteger): 系统状态，0时不可登录

## 2. Wiki相关表结构分析

### 2.1 Wiki文档表(wiki_documents)
基于`2025_04_02_071938_create_wiki_documents_table.php`：

```sql
-- 主键和关联
doc_id (bigIncrements) -- 文档ID
space_id (bigInteger) -- 所属空间ID
parent_id (bigInteger, nullable) -- 父文档ID
catalog_id (bigInteger) -- 目录ID

-- 内容字段
title (varchar 255) -- 文档标题
content_html (longText) -- HTML内容
content_markdown (longText) -- Markdown内容
current_editor_type (tinyInteger) -- 编辑器类型

-- 用户操作字段
created_by (bigInteger) -- 创建人ID
updated_by (bigInteger) -- 最后更新人ID
content_updated_by (bigInteger) -- 内容更新人ID

-- 统计字段（重要：已有积分相关基础）
view_count (integer, default 0) -- 浏览次数
like_count (integer, default 0) -- 点赞数量

-- 时间戳
created_at, updated_at, deleted_at, content_updated_at

-- 索引
INDEX(space_id, parent_id, title, created_by, updated_by)
```

### 2.2 Wiki点赞表(wiki_likes)
基于`2025_04_02_071954_create_wiki_likes_table.php`：

```sql
like_id (bigIncrements) -- 点赞ID
doc_id (bigInteger) -- 文档ID
user_id (bigInteger) -- 用户ID
created_at, updated_at, deleted_at

INDEX(doc_id, user_id)
```

### 2.3 Wiki评论表(wiki_comments)
基于`2025_04_02_071958_create_wiki_comments_table.php`：

```sql
comment_id (bigIncrements) -- 评论ID
doc_id (bigInteger) -- 文档ID
user_id (bigInteger) -- 评论用户ID
parent_id (bigInteger, nullable) -- 父评论ID
content (text) -- 评论内容
is_deleted (tinyInteger, default 0) -- 是否删除

INDEX(doc_id, user_id, parent_id)
```

### 2.4 Wiki统计表(wiki_document_statistics)
基于`2025_04_02_072005_create_wiki_document_statistics_table.php`：

```sql
stat_id (bigIncrements) -- 统计记录ID
doc_id (bigInteger) -- 文档ID
date (date) -- 统计日期
view_count (integer, default 0) -- 当日浏览量
like_count (integer, default 0) -- 当日点赞量
comment_count (integer, default 0) -- 当日评论量

INDEX(doc_id, date)
```

## 3. 字段命名规范分析

### 3.1 命名规范
- **主键**: 使用表意明确的字段名，如`doc_id`, `like_id`, `comment_id`
- **外键**: 使用`_id`后缀，如`user_id`, `doc_id`, `space_id`
- **状态字段**: 使用`status`, `is_deleted`等语义明确的字段名
- **计数字段**: 使用`_count`后缀，如`view_count`, `like_count`
- **时间字段**: 标准Laravel时间戳：`created_at`, `updated_at`, `deleted_at`

### 3.2 数据类型使用规范
- **主键**: `bigIncrements`（支持大数据量）
- **外键**: `bigInteger`（与主键类型保持一致）
- **状态字段**: `tinyInteger`（节省存储空间）
- **计数字段**: `integer`（支持大数值）
- **JSON数据**: `json`类型
- **文本内容**: `text`或`longText`

## 4. 索引设计分析

### 4.1 索引策略
- **单字段索引**: 常用查询字段如`user_id`, `doc_id`
- **复合索引**: 常用组合查询，但未发现复杂的复合索引
- **时间索引**: `date`字段用于统计查询

### 4.2 外键设计
- 使用软删除而非硬外键约束
- 依赖应用层维护数据一致性

## 5. 现有积分相关字段

### 5.1 已有统计字段
在Wiki系统中已经存在积分系统的基础：

- `wiki_documents.view_count`: 文档浏览次数
- `wiki_documents.like_count`: 文档点赞数
- `wiki_document_statistics`: 详细的日统计数据

### 5.2 用户行为跟踪
- `wiki_likes`: 用户点赞行为记录
- `wiki_comments`: 用户评论行为记录
- `user_comments`: 项目相关的用户评论（Redmine数据库）

## 6. 积分系统设计建议

### 6.1 基于现有结构的设计思路

根据分析的数据库结构和积分系统需求文档，建议如下：

#### 6.1.1 用户积分表设计
```sql
-- 用户积分表
CREATE TABLE user_points (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    current_points INT DEFAULT 0 COMMENT '当前积分（知识豆）',
    total_earned_points INT DEFAULT 0 COMMENT '累计获得积分',
    level TINYINT DEFAULT 1 COMMENT '等级(1青铜 2白银 3黄金 4钻石 5传奇)',
    level_name VARCHAR(20) DEFAULT '青铜智者' COMMENT '等级名称',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_points (current_points),
    INDEX idx_level (level)
);
```

#### 6.1.2 积分记录表设计
```sql
-- 积分变动记录表
CREATE TABLE point_records (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    action_type VARCHAR(50) NOT NULL COMMENT '行为类型',
    action_id BIGINT UNSIGNED COMMENT '关联的行为记录ID',
    points_change INT NOT NULL COMMENT '积分变化（正负数）',
    current_points INT NOT NULL COMMENT '变动后积分',
    description VARCHAR(255) COMMENT '变动描述',
    source_table VARCHAR(50) COMMENT '来源表名',
    source_id BIGINT UNSIGNED COMMENT '来源记录ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_action_type (action_type),
    INDEX idx_created_at (created_at),
    INDEX idx_source (source_table, source_id)
);
```

#### 6.1.3 成就系统表设计
```sql
-- 成就定义表
CREATE TABLE achievements (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '成就名称',
    description TEXT COMMENT '成就描述',
    icon VARCHAR(255) COMMENT '图标路径',
    condition_type VARCHAR(50) NOT NULL COMMENT '达成条件类型',
    condition_value INT NOT NULL COMMENT '达成条件数值',
    reward_points INT DEFAULT 0 COMMENT '奖励积分',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_condition_type (condition_type),
    INDEX idx_is_active (is_active)
);

-- 用户成就记录表
CREATE TABLE user_achievements (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    achievement_id BIGINT UNSIGNED NOT NULL COMMENT '成就ID',
    achieved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
    
    UNIQUE KEY uk_user_achievement (user_id, achievement_id),
    INDEX idx_user_id (user_id),
    INDEX idx_achievement_id (achievement_id)
);
```

### 6.2 积分规则映射

基于积分系统文档的规则：

| 行为 | 积分变化 | 对应数据源 |
|------|----------|------------|
| 发布知识库文档 | +10 | wiki_documents.created_by |
| 帖子被点赞 | +1 | wiki_likes表新增记录 |
| 帖子被加精华 | +50 | 需要新增精华标记字段 |
| 线下培训认证 | +300 | 需要新增培训记录表 |
| 帖子被删除 | -10 | wiki_documents.deleted_at |

### 6.3 等级体系映射

| 等级 | 积分范围 | 等级编号 |
|------|----------|----------|
| 青铜智者 | 0-99 | 1 |
| 白银智者 | 100-499 | 2 |
| 黄金智者 | 500-999 | 3 |
| 钻石智者 | 1000-4999 | 4 |
| 传奇智者 | 5000+ | 5 |

## 7. 技术实现建议

### 7.1 模型设计
- 继承现有的`\App\Model\Model`基类
- 使用软删除`SoftDeletes` trait
- 合理使用`$casts`进行类型转换
- 建立适当的关联关系

### 7.2 数据库连接
- 使用默认数据库连接（不需要指定`$connection`）
- Wiki相关表都在主数据库中

### 7.3 缓存策略
- 用户积分信息适合使用Redis缓存
- 排行榜数据可以使用缓存优化查询性能

## 8. 总结

TChipBI的数据库设计遵循了良好的规范：
1. **命名清晰**: 字段名语义明确，便于理解
2. **类型合理**: 数据类型选择恰当，考虑存储效率
3. **索引完善**: 基于查询需求建立了合适的索引
4. **扩展性好**: 使用JSON字段和软删除，便于功能扩展

现有的Wiki系统已经具备了积分系统的基础设施，包括用户行为记录、统计数据等，可以在此基础上快速构建完整的积分和成就系统。

## 9. 下一步行动建议

1. **创建积分相关数据表迁移文件**
2. **开发对应的Model类**
3. **实现积分计算和等级升级逻辑**
4. **开发成就系统的触发机制**
5. **创建积分和成就的管理界面**
6. **实现积分排行榜功能**