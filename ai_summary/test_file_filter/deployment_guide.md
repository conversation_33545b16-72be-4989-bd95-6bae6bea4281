# 测试文件筛选功能部署指南

## 一、功能概述
为老化测试和厂测文件添加结构化数据存储和精细化筛选功能，实现基于测试结果内容的高级筛选。

## 二、后端部署步骤

### 1. 执行数据库迁移
```bash
cd tchip_bi_backend
php bin/hyperf.php migrate
```

这将创建以下表和字段：
- `bi_aging_test_result` - 老化测试结果表
- `bi_factory_test_result` - 厂测结果表  
- `bi_file_sync` 表新增字段：file_type, is_parsed, parsed_at

### 2. 清理缓存（如有必要）
```bash
php bin/hyperf.php di:init-proxy
```

### 3. 重启服务
```bash
php bin/hyperf.php start
# 或使用你的服务管理方式
```

## 三、前端部署步骤

### 1. 安装依赖（如有新增）
```bash
cd tchip_bi_frontend
npm install
```

### 2. 编译打包
```bash
npm run build
```

### 3. 部署静态文件
将 `dist` 目录下的文件部署到Web服务器。

## 四、功能验证

### 1. 检查接口是否正常
测试以下接口：
- GET `/api/testfile/statistics/aging-test` - 老化测试统计
- GET `/api/testfile/statistics/factory-test` - 厂测统计
- GET `/api/testfile/files` - 文件列表（支持新的筛选参数）

### 2. 功能测试流程

#### 基础功能测试
1. 访问文件管理页面
2. 选择文件类型（老化测试/厂测）
3. 查看是否显示统计信息
4. 点击"高级筛选"按钮
5. 设置筛选条件并查询

#### 老化测试筛选测试
1. 选择文件类型为"老化测试"
2. 设置CPU温度范围（如：60-80°C）
3. 设置运行时长范围（如：10-24小时）
4. 点击查询，验证结果

#### 厂测筛选测试
1. 选择文件类型为"厂测"
2. 选择测试结果（成功/失败）
3. 输入设备名或固件版本
4. 选择测试项状态
5. 点击查询，验证结果

## 五、文件变更清单

### 后端文件
```
已创建：
- migrations/2025_08_29_100001_create_aging_test_result_table.php
- migrations/2025_08_29_100002_create_factory_test_result_table.php
- migrations/2025_08_29_100003_add_parse_fields_to_file_sync_table.php
- app/Model/TestFile/AgingTestResultModel.php
- app/Model/TestFile/FactoryTestResultModel.php

已修改：
- app/Core/Services/TestFile/ReorganizeService.php
- app/Core/Services/TestFile/FileManagerService.php
- app/Model/TestFile/FileSyncModel.php
- app/Controller/TestFile/TestFileController.php
```

### 前端文件
```
已修改：
- src/views/testFile/components/file-manager/FileManagerView.vue
- src/api/testFile.ts (已包含所需接口)
```

## 六、注意事项

### 1. 数据解析
- 首次部署后，新文件会在重排时自动解析
- 历史文件需要重新执行重排任务才能解析

### 2. 性能考虑
- 大量文件解析可能耗时较长
- 建议分批处理历史数据

### 3. 权限检查
- 确保PHP进程有读取测试文件的权限
- 确保有写入数据库的权限

### 4. 编码问题
- 系统自动处理GBK/UTF-8编码转换
- 如遇乱码，检查文件原始编码

## 七、故障排查

### 问题1：统计数据不显示
- 检查后端接口是否返回数据
- 查看浏览器控制台错误信息
- 确认数据库中有解析后的数据

### 问题2：筛选无效果
- 确认参数是否正确传递到后端
- 检查SQL查询日志
- 验证数据库索引是否创建

### 问题3：文件解析失败
- 查看日志文件中的错误信息
- 检查文件格式是否符合预期
- 验证正则表达式匹配

## 八、回滚方案

如需回滚，执行以下步骤：

### 1. 数据库回滚
```bash
php bin/hyperf.php migrate:rollback --step=3
```

### 2. 代码回滚
使用Git回滚到之前的版本：
```bash
git checkout <previous-commit>
```

### 3. 重新部署
按照常规部署流程重新部署旧版本。

## 九、后续优化建议

1. **添加缓存机制**：对统计数据进行缓存，提高响应速度
2. **异步解析**：使用队列处理大批量文件解析
3. **数据导出**：添加筛选结果导出功能
4. **图表展示**：添加可视化图表展示统计数据
5. **更多文件类型**：扩展支持更多测试文件类型

## 十、联系支持

如遇问题，请提供以下信息：
- 错误日志
- 操作步骤
- 环境信息（PHP版本、数据库版本等）
