# 测试文件筛选功能扩展实施计划

## 项目概述
根据设计方案，为老化测试和厂测文件添加结构化数据存储和筛选功能

## 实施阶段

### 第一阶段：数据库层实现
1. 创建老化测试结果表（bi_aging_test_result）
2. 创建厂测结果表（bi_factory_test_result）  
3. 扩展bi_file_sync表，添加file_type、is_parsed、parsed_at字段
4. 创建对应的Model文件

### 第二阶段：后端服务层实现
1. 修改ReorganizeService的processFile方法
2. 扩展parseAgingTestResult方法，增加数据解析和存储
3. 扩展parseFactoryTestResult方法，增加数据解析和存储
4. 添加getTestColumnName辅助方法

### 第三阶段：前端界面实现
1. 扩展文件管理筛选表单
2. 添加文件类型切换功能
3. 实现老化测试特有筛选条件
4. 实现厂测特有筛选条件

### 第四阶段：接口优化
1. 扩展查询接口，支持联合查询
2. 优化查询性能

## 技术要点
- PHP版本：7.4
- 框架：Hyperf
- 前端：Vue
- 数据库：MySQL

## 注意事项
1. 迁移文件需要兼容PHP 7.4语法
2. 使用Carbon::now()而非now()
3. Controller中不写业务逻辑
4. 前端样式命名需与文件名关联

## 时间计划
- 数据库层：2小时
- 后端服务：3小时
- 前端界面：3小时
- 测试调优：2小时

总计：约10小时完成全部功能
