# 知识豆积分系统前端实施方案

## 1. 项目实施概述

### 1.1 实施目标
基于 TChip BI 现有前端架构，开发完整的知识豆积分系统，包括：
- 用户积分概览和明细查看
- 积分排行榜和统计分析
- 管理员积分管理功能
- 积分规则配置和维护
- 与现有系统的深度集成

### 1.2 技术优势
- **成熟架构**: 基于 Vue 3 + TypeScript + Pinia 的现代化架构
- **丰富组件**: 60+ 现成组件可直接复用，减少 70% 开发工作量
- **完善基础设施**: 权限、通知、主题等企业级功能完备
- **开发规范**: 有明确的代码规范和文件组织方式

## 2. 详细实施方案

### 2.1 第一阶段：基础功能开发 (预计 2 周)

#### 2.1.1 文件结构创建
```
src/
├── api/
│   └── knowledgePoints.ts          # 积分系统 API 接口
├── store/modules/
│   └── knowledgePoints.ts          # 积分系统状态管理
├── views/knowledgePoints/          # 积分系统页面
│   ├── index.vue                   # 积分概览页面
│   ├── details.vue                 # 积分明细页面
│   ├── ranking.vue                 # 积分排行榜页面
│   ├── management.vue              # 积分管理页面 (管理员)
│   ├── rules.vue                   # 积分规则页面
│   └── components/                 # 积分系统专用组件
│       ├── PointsOverviewCard.vue  # 积分概览卡片
│       ├── PointsChart.vue         # 积分趋势图表
│       ├── PointsDetailTable.vue   # 积分明细表格
│       ├── PointsRankingList.vue   # 积分排行列表
│       └── PointsRuleDialog.vue    # 积分规则编辑对话框
└── styles/knowledgePoints/         # 积分系统样式
    ├── knowledge-points-overview.scss
    ├── knowledge-points-details.scss
    ├── knowledge-points-ranking.scss
    └── knowledge-points-management.scss
```

#### 2.1.2 API 接口开发
```typescript
// api/knowledgePoints.ts
import request from '@/utils/request'

/**
 * 积分概览接口
 */
export function getPointsOverview(params?: any) {
  return request({
    url: '/knowledgePoints/overview',
    method: 'get',
    params,
  })
}

/**
 * 积分明细列表
 */
export function getPointsList(data: any) {
  return request({
    url: '/knowledgePoints/list',
    method: 'post',
    data,
  })
}

/**
 * 积分排行榜
 */
export function getPointsRanking(params: any) {
  return request({
    url: '/knowledgePoints/ranking',
    method: 'get',
    params,
  })
}

/**
 * 添加积分记录 (管理员功能)
 */
export function addPointsRecord(data: any) {
  return request({
    url: '/knowledgePoints/add',
    method: 'post',
    data,
  })
}

/**
 * 扣减积分记录 (管理员功能)
 */
export function reducePointsRecord(data: any) {
  return request({
    url: '/knowledgePoints/reduce',
    method: 'post',
    data,
  })
}

/**
 * 批量积分操作 (管理员功能)
 */
export function batchPointsOperation(data: any) {
  return request({
    url: '/knowledgePoints/batch',
    method: 'post',
    data,
  })
}

/**
 * 获取积分规则
 */
export function getPointsRules(params?: any) {
  return request({
    url: '/knowledgePoints/rules',
    method: 'get',
    params,
  })
}

/**
 * 更新积分规则 (管理员功能)
 */
export function updatePointsRules(data: any) {
  return request({
    url: '/knowledgePoints/rules/update',
    method: 'post',
    data,
  })
}

/**
 * 积分统计数据
 */
export function getPointsStatistics(params: any) {
  return request({
    url: '/knowledgePoints/statistics',
    method: 'get',
    params,
  })
}

/**
 * 导出积分明细
 */
export function exportPointsDetail(data: any) {
  return request({
    url: '/knowledgePoints/export',
    method: 'post',
    data,
    responseType: 'blob',
  })
}
```

#### 2.1.3 状态管理开发
```typescript
// store/modules/knowledgePoints.ts
import { defineStore } from 'pinia'
import { 
  getPointsOverview,
  getPointsList,
  getPointsRanking,
  getPointsStatistics,
  getPointsRules
} from '@/api/knowledgePoints'

interface KnowledgePointsState {
  // 用户积分信息
  userPoints: {
    total: number              // 总积分
    available: number          // 可用积分
    frozen: number            // 冻结积分
    rank: number              // 排名
    todayEarned: number       // 今日获得
    weekEarned: number        // 本周获得
    monthEarned: number       // 本月获得
  }
  
  // 积分明细
  pointsList: any[]
  pointsTotal: number
  pointsLoading: boolean
  
  // 积分排行榜
  ranking: any[]
  rankingLoading: boolean
  
  // 积分统计
  statistics: {
    dailyTrend: any[]         // 每日趋势
    monthlyTrend: any[]       // 月度趋势
    typeDistribution: any[]   // 类型分布
    departmentRanking: any[]  // 部门排行
  }
  
  // 积分规则
  rules: {
    studyPoints: number       // 学习积分
    sharePoints: number       // 分享积分
    commentPoints: number     // 评论积分
    loginPoints: number       // 登录积分
    taskCompletePoints: number // 任务完成积分
  }
  
  // 加载状态
  loading: boolean
}

export const useKnowledgePointsStore = defineStore('knowledgePoints', {
  state: (): KnowledgePointsState => ({
    userPoints: {
      total: 0,
      available: 0,
      frozen: 0,
      rank: 0,
      todayEarned: 0,
      weekEarned: 0,
      monthEarned: 0,
    },
    pointsList: [],
    pointsTotal: 0,
    pointsLoading: false,
    ranking: [],
    rankingLoading: false,
    statistics: {
      dailyTrend: [],
      monthlyTrend: [],
      typeDistribution: [],
      departmentRanking: [],
    },
    rules: {
      studyPoints: 10,
      sharePoints: 20,
      commentPoints: 5,
      loginPoints: 2,
      taskCompletePoints: 15,
    },
    loading: false,
  }),

  getters: {
    // 获取用户总积分
    getTotalPoints: (state) => state.userPoints.total,
    
    // 获取用户排名
    getUserRank: (state) => state.userPoints.rank,
    
    // 获取今日积分
    getTodayPoints: (state) => state.userPoints.todayEarned,
    
    // 获取本周积分
    getWeekPoints: (state) => state.userPoints.weekEarned,
    
    // 获取本月积分
    getMonthPoints: (state) => state.userPoints.monthEarned,
    
    // 判断是否有积分数据
    hasPointsData: (state) => state.userPoints.total > 0,
  },

  actions: {
    /**
     * 加载用户积分概览
     */
    async loadUserPointsOverview() {
      this.loading = true
      try {
        const response = await getPointsOverview()
        this.userPoints = response.data
      } catch (error) {
        console.error('加载积分概览失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 加载积分明细列表
     */
    async loadPointsList(params: any) {
      this.pointsLoading = true
      try {
        const response = await getPointsList(params)
        this.pointsList = response.data.list
        this.pointsTotal = response.data.total
      } catch (error) {
        console.error('加载积分明细失败:', error)
        throw error
      } finally {
        this.pointsLoading = false
      }
    },

    /**
     * 加载积分排行榜
     */
    async loadRanking(params: any = {}) {
      this.rankingLoading = true
      try {
        const response = await getPointsRanking(params)
        this.ranking = response.data
      } catch (error) {
        console.error('加载排行榜失败:', error)
        throw error
      } finally {
        this.rankingLoading = false
      }
    },

    /**
     * 加载积分统计数据
     */
    async loadStatistics(params: any = {}) {
      try {
        const response = await getPointsStatistics(params)
        this.statistics = response.data
      } catch (error) {
        console.error('加载统计数据失败:', error)
        throw error
      }
    },

    /**
     * 加载积分规则
     */
    async loadRules() {
      try {
        const response = await getPointsRules()
        this.rules = response.data
      } catch (error) {
        console.error('加载积分规则失败:', error)
        throw error
      }
    },

    /**
     * 刷新所有积分数据
     */
    async refreshAllData() {
      await Promise.all([
        this.loadUserPointsOverview(),
        this.loadStatistics(),
        this.loadRules(),
      ])
    },

    /**
     * 清空积分数据
     */
    clearPointsData() {
      this.pointsList = []
      this.pointsTotal = 0
      this.ranking = []
      this.userPoints = {
        total: 0,
        available: 0,
        frozen: 0,
        rank: 0,
        todayEarned: 0,
        weekEarned: 0,
        monthEarned: 0,
      }
    },
  },
})
```

#### 2.1.4 核心页面开发

**积分概览页面 (views/knowledgePoints/index.vue)**
```vue
<template>
  <div class="knowledge-points-overview-container">
    <!-- 积分概览卡片 -->
    <div class="points-overview-cards">
      <vab-colorful-card 
        v-loading="knowledgePointsStore.loading"
        title="我的积分"
        class="points-summary-card"
      >
        <div class="points-summary">
          <div class="current-points">
            <span class="points-number">{{ knowledgePointsStore.getTotalPoints }}</span>
            <span class="points-label">总积分</span>
          </div>
          <div class="points-rank">
            <span class="rank-number"># {{ knowledgePointsStore.getUserRank }}</span>
            <span class="rank-label">当前排名</span>
          </div>
        </div>
      </vab-colorful-card>

      <vab-colorful-card title="近期收益" class="recent-earnings-card">
        <div class="earnings-grid">
          <div class="earning-item">
            <span class="earning-value">{{ knowledgePointsStore.getTodayPoints }}</span>
            <span class="earning-label">今日</span>
          </div>
          <div class="earning-item">
            <span class="earning-value">{{ knowledgePointsStore.getWeekPoints }}</span>
            <span class="earning-label">本周</span>
          </div>
          <div class="earning-item">
            <span class="earning-value">{{ knowledgePointsStore.getMonthPoints }}</span>
            <span class="earning-label">本月</span>
          </div>
        </div>
      </vab-colorful-card>
    </div>

    <!-- 积分趋势图表 -->
    <vab-card title="积分趋势" class="points-trend-card">
      <points-chart 
        :data="knowledgePointsStore.statistics.dailyTrend"
        type="line"
        height="300px"
      />
    </vab-card>

    <!-- 最近积分记录 -->
    <vab-card title="最近积分记录" class="recent-points-card">
      <points-detail-table 
        :data="recentPointsList"
        :show-pagination="false"
        :max-height="400"
      />
      <div class="more-link">
        <el-button type="text" @click="goToDetails">
          查看全部明细 <vab-icon icon="arrow-right-s-line" />
        </el-button>
      </div>
    </vab-card>

    <!-- 积分排行榜预览 -->
    <vab-card title="积分排行榜" class="ranking-preview-card">
      <points-ranking-list 
        :data="topRankingList"
        :show-more="false"
        :max-items="10"
      />
      <div class="more-link">
        <el-button type="text" @click="goToRanking">
          查看完整排行榜 <vab-icon icon="arrow-right-s-line" />
        </el-button>
      </div>
    </vab-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useKnowledgePointsStore } from '@/store/modules/knowledgePoints'
import PointsChart from './components/PointsChart.vue'
import PointsDetailTable from './components/PointsDetailTable.vue'
import PointsRankingList from './components/PointsRankingList.vue'

defineOptions({
  name: 'KnowledgePointsOverview',
})

const router = useRouter()
const knowledgePointsStore = useKnowledgePointsStore()

// 最近积分记录
const recentPointsList = computed(() => 
  knowledgePointsStore.pointsList.slice(0, 5)
)

// 排行榜前10名
const topRankingList = computed(() => 
  knowledgePointsStore.ranking.slice(0, 10)
)

// 跳转到积分明细页
const goToDetails = () => {
  router.push('/knowledgePoints/details')
}

// 跳转到排行榜页
const goToRanking = () => {
  router.push('/knowledgePoints/ranking')
}

// 页面初始化
onMounted(async () => {
  try {
    await knowledgePointsStore.refreshAllData()
    // 加载最近的积分记录
    await knowledgePointsStore.loadPointsList({ 
      pageSize: 5, 
      current: 1 
    })
    // 加载排行榜前10名
    await knowledgePointsStore.loadRanking({ 
      pageSize: 10, 
      current: 1 
    })
  } catch (error) {
    console.error('加载积分数据失败:', error)
  }
})
</script>

<style lang="scss" scoped>
.knowledge-points-overview-container {
  padding: 20px;

  .points-overview-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;

    .points-summary-card {
      .points-summary {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .current-points, .points-rank {
          display: flex;
          flex-direction: column;
          align-items: center;

          .points-number, .rank-number {
            font-size: 32px;
            font-weight: bold;
            color: var(--el-color-primary);
            line-height: 1;
          }

          .points-label, .rank-label {
            font-size: 14px;
            color: var(--el-text-color-secondary);
            margin-top: 4px;
          }
        }
      }
    }

    .recent-earnings-card {
      .earnings-grid {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 16px;

        .earning-item {
          display: flex;
          flex-direction: column;
          align-items: center;

          .earning-value {
            font-size: 24px;
            font-weight: bold;
            color: var(--el-color-success);
            line-height: 1;
          }

          .earning-label {
            font-size: 12px;
            color: var(--el-text-color-secondary);
            margin-top: 4px;
          }
        }
      }
    }
  }

  .points-trend-card,
  .recent-points-card,
  .ranking-preview-card {
    margin-bottom: 20px;

    .more-link {
      text-align: center;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid var(--el-border-color-light);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .knowledge-points-overview-container {
    padding: 16px;

    .points-overview-cards {
      grid-template-columns: 1fr;
    }
  }
}
</style>
```

#### 2.1.5 路由配置
```typescript
// 在 router/index.ts 的 asyncRoutes 中添加
{
  path: '/knowledgePoints',
  name: 'KnowledgePoints',
  component: Layout,
  meta: {
    title: '知识豆积分',
    icon: 'coin-line',
    guard: ['Admin', 'User'],
  },
  children: [
    {
      path: 'overview',
      name: 'KnowledgePointsOverview',
      component: () => import('@/views/knowledgePoints/index.vue'),
      meta: {
        title: '积分概览',
        icon: 'dashboard-2-line',
      },
    },
    {
      path: 'details',
      name: 'KnowledgePointsDetails',
      component: () => import('@/views/knowledgePoints/details.vue'),
      meta: {
        title: '积分明细',
        icon: 'file-list-2-line',
      },
    },
    {
      path: 'ranking',
      name: 'KnowledgePointsRanking',
      component: () => import('@/views/knowledgePoints/ranking.vue'),
      meta: {
        title: '积分排行',
        icon: 'trophy-line',
      },
    },
    {
      path: 'management',
      name: 'KnowledgePointsManagement',
      component: () => import('@/views/knowledgePoints/management.vue'),
      meta: {
        title: '积分管理',
        icon: 'settings-3-line',
        guard: ['Admin'], // 仅管理员可访问
      },
    },
    {
      path: 'rules',
      name: 'KnowledgePointsRules',
      component: () => import('@/views/knowledgePoints/rules.vue'),
      meta: {
        title: '积分规则',
        icon: 'book-2-line',
      },
    },
  ],
}
```

### 2.2 第二阶段：高级功能开发 (预计 1.5 周)

#### 2.2.1 积分明细页面开发
```vue
<!-- views/knowledgePoints/details.vue -->
<template>
  <div class="knowledge-points-details-container">
    <!-- 查询表单 -->
    <vab-query-form>
      <vab-query-form-left-panel :span="16">
        <el-form inline label-width="80px" :model="queryForm" @submit.prevent>
          <el-form-item label="时间范围">
            <el-date-picker 
              v-model="queryForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateChange"
            />
          </el-form-item>
          
          <el-form-item label="积分类型">
            <el-select 
              v-model="queryForm.pointsType" 
              placeholder="全部类型"
              clearable
              @change="handleQuery"
            >
              <el-option label="全部" value="" />
              <el-option label="学习积分" value="study" />
              <el-option label="分享积分" value="share" />
              <el-option label="评论积分" value="comment" />
              <el-option label="登录积分" value="login" />
              <el-option label="任务积分" value="task" />
              <el-option label="管理员操作" value="admin" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="积分方向">
            <el-select 
              v-model="queryForm.direction" 
              placeholder="全部"
              clearable
              @change="handleQuery"
            >
              <el-option label="全部" value="" />
              <el-option label="获得" value="add" />
              <el-option label="消费" value="consume" />
              <el-option label="扣减" value="reduce" />
            </el-select>
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      
      <vab-query-form-right-panel :span="8">
        <el-button 
          :icon="Download" 
          type="primary" 
          @click="exportDetails"
          :loading="exportLoading"
        >
          导出明细
        </el-button>
        <el-button :icon="Refresh" @click="refreshData">
          刷新
        </el-button>
      </vab-query-form-right-panel>
    </vab-query-form>

    <!-- 积分明细表格 -->
    <vab-table 
      v-loading="knowledgePointsStore.pointsLoading"
      :data="knowledgePointsStore.pointsList"
      :columns="tableColumns"
      :pagination="pagination"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { Download, Refresh } from '@element-plus/icons-vue'
import { useKnowledgePointsStore } from '@/store/modules/knowledgePoints'
import { exportPointsDetail } from '@/api/knowledgePoints'
import { formatTime } from '@/utils/time'
import { gp } from '@gp'

defineOptions({
  name: 'KnowledgePointsDetails',
})

const knowledgePointsStore = useKnowledgePointsStore()

// 查询表单
const queryForm = reactive({
  dateRange: [] as string[],
  pointsType: '',
  direction: '',
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: computed(() => knowledgePointsStore.pointsTotal),
})

// 导出加载状态
const exportLoading = ref(false)

// 表格列配置
const tableColumns = [
  {
    prop: 'created_at',
    label: '时间',
    width: 180,
    formatter: (row: any) => formatTime(row.created_at),
  },
  {
    prop: 'type_name',
    label: '类型',
    width: 120,
  },
  {
    prop: 'points',
    label: '积分变动',
    width: 120,
    formatter: (row: any) => {
      const points = row.points
      const prefix = points > 0 ? '+' : ''
      const className = points > 0 ? 'text-success' : 'text-danger'
      return `<span class="${className}">${prefix}${points}</span>`
    },
  },
  {
    prop: 'balance',
    label: '积分余额',
    width: 120,
  },
  {
    prop: 'description',
    label: '说明',
    minWidth: 200,
  },
  {
    prop: 'source',
    label: '来源',
    width: 120,
  },
]

// 处理查询
const handleQuery = () => {
  pagination.current = 1
  loadPointsList()
}

// 处理日期变化
const handleDateChange = () => {
  handleQuery()
}

// 处理分页变化
const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadPointsList()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.current = 1
  loadPointsList()
}

// 加载积分明细数据
const loadPointsList = async () => {
  try {
    const params = {
      current: pagination.current,
      pageSize: pagination.pageSize,
      dateRange: queryForm.dateRange,
      pointsType: queryForm.pointsType,
      direction: queryForm.direction,
    }
    await knowledgePointsStore.loadPointsList(params)
  } catch (error) {
    gp.$baseMessage('加载积分明细失败', 'error', 'vab-hey-message-error')
  }
}

// 刷新数据
const refreshData = () => {
  loadPointsList()
}

// 导出明细
const exportDetails = async () => {
  exportLoading.value = true
  try {
    const params = {
      dateRange: queryForm.dateRange,
      pointsType: queryForm.pointsType,
      direction: queryForm.direction,
    }
    
    const response = await exportPointsDetail(params)
    
    // 创建下载链接
    const blob = new Blob([response], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `积分明细_${new Date().getTime()}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    gp.$baseMessage('导出成功', 'success', 'vab-hey-message-success')
  } catch (error) {
    gp.$baseMessage('导出失败', 'error', 'vab-hey-message-error')
  } finally {
    exportLoading.value = false
  }
}

// 页面初始化
onMounted(() => {
  loadPointsList()
})
</script>

<style lang="scss" scoped>
.knowledge-points-details-container {
  .text-success {
    color: var(--el-color-success);
  }
  
  .text-danger {
    color: var(--el-color-danger);
  }
}
</style>
```

#### 2.2.2 积分管理页面开发 (管理员功能)
```vue
<!-- views/knowledgePoints/management.vue -->
<template>
  <div class="knowledge-points-management-container">
    <!-- 操作功能区 -->
    <vab-query-form>
      <vab-query-form-left-panel :span="16">
        <el-form inline :model="queryForm">
          <!-- 复用现有的人员选择组件 -->
          <el-form-item label="选择用户">
            <personnel-select 
              v-model="queryForm.selectedUsers"
              multiple
              placeholder="选择要操作的用户"
              style="width: 300px"
            />
          </el-form-item>
          
          <el-form-item label="操作类型">
            <el-select v-model="queryForm.operationType" placeholder="选择操作">
              <el-option label="添加积分" value="add" />
              <el-option label="扣减积分" value="reduce" />
              <el-option label="设置积分" value="set" />
            </el-select>
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      
      <vab-query-form-right-panel :span="8">
        <el-button 
          type="primary" 
          :disabled="!canOperate"
          @click="showOperationDialog"
        >
          执行操作
        </el-button>
        <el-button 
          type="success" 
          @click="showRulesDialog"
        >
          积分规则设置
        </el-button>
      </vab-query-form-right-panel>
    </vab-query-form>

    <!-- 用户积分列表 -->
    <vab-card title="用户积分管理">
      <vab-table 
        :data="userPointsList"
        :columns="userTableColumns"
        :pagination="userPagination"
        @current-change="handleUserCurrentChange"
        @size-change="handleUserSizeChange"
      />
    </vab-card>

    <!-- 积分操作对话框 -->
    <firefly-dialog
      v-model="operationDialogVisible"
      :title="operationDialogTitle"
      width="600px"
      @confirm="confirmOperation"
      @cancel="cancelOperation"
    >
      <el-form 
        ref="operationFormRef"
        :model="operationForm"
        :rules="operationRules"
        label-width="100px"
      >
        <el-form-item label="操作用户">
          <el-tag 
            v-for="user in selectedUserList" 
            :key="user.id"
            class="user-tag"
          >
            {{ user.name }}
          </el-tag>
        </el-form-item>
        
        <el-form-item label="积分数量" prop="points">
          <el-input-number 
            v-model="operationForm.points"
            :min="operationForm.operationType === 'set' ? 0 : 1"
            :max="999999"
            placeholder="请输入积分数量"
          />
        </el-form-item>
        
        <el-form-item label="操作原因" prop="reason">
          <el-input 
            v-model="operationForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入操作原因"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </firefly-dialog>

    <!-- 积分规则设置对话框 -->
    <points-rule-dialog 
      v-model="rulesDialogVisible"
      @refresh="loadRules"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useKnowledgePointsStore } from '@/store/modules/knowledgePoints'
import { addPointsRecord, reducePointsRecord, batchPointsOperation } from '@/api/knowledgePoints'
import PersonnelSelect from '@/components/PersonnelSelect.vue'
import PointsRuleDialog from './components/PointsRuleDialog.vue'
import { gp } from '@gp'

defineOptions({
  name: 'KnowledgePointsManagement',
})

const knowledgePointsStore = useKnowledgePointsStore()

// 查询表单
const queryForm = reactive({
  selectedUsers: [] as any[],
  operationType: 'add',
})

// 用户积分列表数据
const userPointsList = ref([])
const userPagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
})

// 操作对话框
const operationDialogVisible = ref(false)
const operationFormRef = ref()
const operationForm = reactive({
  operationType: 'add',
  points: 0,
  reason: '',
})

// 积分规则对话框
const rulesDialogVisible = ref(false)

// 用户表格列配置
const userTableColumns = [
  {
    prop: 'username',
    label: '用户名',
    width: 120,
  },
  {
    prop: 'department_name',
    label: '部门',
    width: 120,
  },
  {
    prop: 'total_points',
    label: '总积分',
    width: 100,
  },
  {
    prop: 'available_points',
    label: '可用积分',
    width: 100,
  },
  {
    prop: 'frozen_points',
    label: '冻结积分',
    width: 100,
  },
  {
    prop: 'rank',
    label: '排名',
    width: 80,
  },
  {
    prop: 'last_earned_at',
    label: '最后获得时间',
    width: 180,
  },
]

// 表单验证规则
const operationRules = {
  points: [
    { required: true, message: '请输入积分数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '积分数量必须大于0', trigger: 'blur' },
  ],
  reason: [
    { required: true, message: '请输入操作原因', trigger: 'blur' },
    { min: 5, message: '操作原因至少5个字符', trigger: 'blur' },
  ],
}

// 计算属性
const canOperate = computed(() => 
  queryForm.selectedUsers.length > 0 && queryForm.operationType
)

const selectedUserList = computed(() => queryForm.selectedUsers)

const operationDialogTitle = computed(() => {
  const typeMap = {
    add: '添加积分',
    reduce: '扣减积分',
    set: '设置积分',
  }
  return typeMap[queryForm.operationType] || '积分操作'
})

// 显示操作对话框
const showOperationDialog = () => {
  if (!canOperate.value) {
    gp.$baseMessage('请先选择用户和操作类型', 'warning', 'vab-hey-message-warning')
    return
  }
  
  operationForm.operationType = queryForm.operationType
  operationForm.points = 0
  operationForm.reason = ''
  operationDialogVisible.value = true
}

// 显示规则设置对话框
const showRulesDialog = () => {
  rulesDialogVisible.value = true
}

// 确认操作
const confirmOperation = async () => {
  try {
    await operationFormRef.value.validate()
    
    const operationData = {
      users: queryForm.selectedUsers.map(user => user.id),
      operationType: operationForm.operationType,
      points: operationForm.points,
      reason: operationForm.reason,
    }
    
    await batchPointsOperation(operationData)
    
    gp.$baseMessage('操作成功', 'success', 'vab-hey-message-success')
    operationDialogVisible.value = false
    
    // 刷新数据
    loadUserPointsList()
    
    // 清空选择
    queryForm.selectedUsers = []
  } catch (error) {
    console.error('积分操作失败:', error)
    gp.$baseMessage('操作失败', 'error', 'vab-hey-message-error')
  }
}

// 取消操作
const cancelOperation = () => {
  operationDialogVisible.value = false
}

// 加载用户积分列表
const loadUserPointsList = async () => {
  // 这里应该调用获取用户积分列表的API
  // 暂时使用模拟数据
}

// 加载积分规则
const loadRules = () => {
  knowledgePointsStore.loadRules()
}

// 分页处理
const handleUserCurrentChange = (current: number) => {
  userPagination.current = current
  loadUserPointsList()
}

const handleUserSizeChange = (size: number) => {
  userPagination.pageSize = size
  userPagination.current = 1
  loadUserPointsList()
}

// 页面初始化
onMounted(() => {
  loadUserPointsList()
  loadRules()
})
</script>

<style lang="scss" scoped>
.knowledge-points-management-container {
  .user-tag {
    margin-right: 8px;
    margin-bottom: 4px;
  }
}
</style>
```

### 2.3 第三阶段：完善和优化 (预计 1 周)

#### 2.3.1 积分排行榜页面
#### 2.3.2 积分规则配置页面
#### 2.3.3 移动端页面适配
#### 2.3.4 企业微信通知集成
#### 2.3.5 性能优化和测试

## 3. 实施时间表

### 3.1 详细时间安排

| 阶段 | 任务 | 预计时间 | 负责人 | 备注 |
|------|------|----------|--------|------|
| 第一阶段 | API接口设计和开发 | 3天 | 后端开发 | 包含所有CRUD接口 |
|  | 状态管理开发 | 2天 | 前端开发 | Pinia Store |
|  | 积分概览页面 | 3天 | 前端开发 | 核心展示页面 |
|  | 路由和权限配置 | 1天 | 前端开发 | 集成现有权限系统 |
|  | 基础组件开发 | 1天 | 前端开发 | 图表、表格组件 |
| 第二阶段 | 积分明细页面 | 2天 | 前端开发 | 查询、导出功能 |
|  | 积分管理页面 | 3天 | 前端开发 | 管理员功能 |
|  | 积分排行榜页面 | 2天 | 前端开发 | 排行和统计 |
|  | 积分规则配置 | 1.5天 | 前端开发 | 规则维护 |
| 第三阶段 | 移动端适配 | 2天 | 前端开发 | 响应式设计 |
|  | 企业微信集成 | 1天 | 前端开发 | 通知推送 |
|  | 性能优化 | 1天 | 前端开发 | 代码优化 |
|  | 测试和调试 | 3天 | 全栈开发 | 功能测试 |

### 3.2 里程碑计划

- **第1周结束**: 完成基础功能开发，积分概览页面可用
- **第2.5周结束**: 完成高级功能开发，管理功能可用
- **第4.5周结束**: 完成全部功能开发和测试，可正式上线

## 4. 质量保证

### 4.1 代码质量
- **代码规范**: 严格遵循项目ESLint和Prettier配置
- **TypeScript**: 全面使用TypeScript，确保类型安全
- **组件复用**: 最大化利用现有组件库，减少重复代码
- **性能优化**: 使用Vue 3 Composition API和响应式优化

### 4.2 用户体验
- **响应式设计**: 确保PC端和移动端良好体验
- **加载状态**: 所有异步操作提供Loading状态
- **错误处理**: 统一的错误提示和处理机制
- **操作反馈**: 及时的操作成功/失败反馈

### 4.3 测试覆盖
- **单元测试**: 核心业务逻辑单元测试
- **集成测试**: API接口和组件集成测试
- **用户测试**: 实际用户场景测试
- **性能测试**: 大数据量和高并发测试

## 5. 风险评估与应对

### 5.1 技术风险
- **组件兼容性**: 现有组件可能需要微调适配
  - 应对: 预留时间进行组件适配和定制
- **性能问题**: 大量积分数据可能影响页面性能
  - 应对: 实施分页、虚拟滚动等性能优化

### 5.2 业务风险
- **需求变更**: 积分规则可能需要调整
  - 应对: 设计灵活的规则配置系统
- **数据安全**: 积分数据的安全性要求高
  - 应对: 严格的权限控制和操作日志

## 6. 预期效果

### 6.1 开发效率提升
- **组件复用率**: 预计70%的UI组件可直接复用
- **开发时间节省**: 比从零开发节省60%的时间
- **维护成本降低**: 统一的技术栈和代码规范

### 6.2 用户体验优化
- **界面统一性**: 与现有系统保持一致的用户体验
- **功能完整性**: 提供完整的积分管理功能
- **易用性**: 简洁直观的操作界面

通过充分利用TChip BI现有的前端架构和组件库，知识豆积分系统可以快速、高质量地完成开发，为用户提供优秀的积分管理体验。