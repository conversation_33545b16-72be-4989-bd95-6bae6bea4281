# 测试文件管理系统项目总览

## 项目简介

本项目是一个完整的测试文件管理系统，用于管理从测试设备生成的大量测试文件。系统通过 Lsyncd 实时同步文件到远端服务器，并提供目录重排、数据库管理和 Web 界面展示等功能。

## 项目结构

```
test-file-manager/
├── ai_summary/                 # 项目规划文档
│   ├── 系统架构/
│   │   └── 系统架构总览.md
│   ├── 数据库设计/
│   │   └── 数据库设计文档.md
│   ├── 后端设计/
│   │   ├── API接口文档.md
│   │   └── 目录重排实现方案.md
│   ├── 前端设计/
│   │   └── 界面设计文档.md
│   ├── 部署运维/
│   │   └── 部署运维文档.md
│   └── 项目总览.md
├── backend/                    # 后端项目代码（Hyperf）
├── frontend/                   # 前端项目代码（Vue3）
├── database/                   # 数据库脚本
│   └── init.sql
└── docs/                      # 其他文档

```

## 技术栈

### 后端技术
- **语言**: PHP 7.4
- **框架**: Hyperf 2.2
- **数据库**: MySQL 5.7+
- **缓存**: Redis 6.0+
- **文件同步**: Lsyncd + Rsync

### 前端技术
- **框架**: Vue 3.2+
- **UI库**: Element Plus
- **构建工具**: Vite
- **图表**: ECharts
- **HTTP客户端**: Axios

### 运维技术
- **Web服务器**: Nginx
- **进程管理**: Systemd
- **监控**: Prometheus + Grafana
- **容器化**: Docker（可选）

## 核心功能

### 1. 文件同步
- 使用 Lsyncd 实时监控本地目录变化
- 通过 SSH + Rsync 同步到远端服务器
- 支持断点续传和自动重连
- 百万级文件处理能力

### 2. 目录重排
- 自动解析原始目录结构
- 按产品/SN/测试时间重新组织
- 批量处理和并发优化
- 幂等性设计防止重复处理

### 3. 数据管理
- 文件元信息自动入库
- 支持多维度查询和统计
- 测试记录关联管理
- 定期数据归档清理

### 4. Web界面
- 树形目录结构展示
- 文件列表查询和下载
- 实时统计图表
- 响应式设计支持移动端

## 数据流程

```
1. 测试设备生成文件
   ↓
2. Lsyncd 检测文件变化
   ↓
3. SSH/Rsync 同步到远端临时目录
   ↓
4. 后端服务扫描临时目录
   ↓
5. 解析路径并重排到目标目录
   ↓
6. 文件信息写入数据库
   ↓
7. 前端查询展示
```

## 部署要点

### 环境准备
1. 配置 SSH 免密登录
2. 安装必要的系统软件
3. 优化系统参数
4. 创建目录结构

### 服务部署顺序
1. MySQL 数据库
2. Redis 缓存
3. 后端 API 服务
4. 前端静态服务
5. Lsyncd 同步服务

### 监控告警
- 服务健康检查
- 资源使用监控
- 同步状态监控
- 错误日志告警

## 性能指标

- 文件同步延迟: < 5秒
- API 响应时间: < 200ms
- 并发处理能力: 1000+ 文件/分钟
- 系统可用性: > 99.9%

## 安全措施

1. SSH 密钥认证
2. API 接口鉴权（JWT）
3. 数据传输加密
4. 文件访问控制
5. 操作审计日志

## 扩展计划

### 短期计划
- 支持更多文件类型解析
- 增加批量操作功能
- 优化大文件传输
- 完善监控指标

### 长期计划
- 分布式部署支持
- 多租户隔离
- 智能分析功能
- API 开放平台

## 开发指南

### 后端开发
```bash
# 克隆代码
git clone <repository>

# 安装依赖
cd backend
composer install

# 配置环境
cp .env.example .env
vim .env

# 启动服务
php bin/hyperf.php start
```

### 前端开发
```bash
# 安装依赖
cd frontend
npm install

# 开发环境
npm run dev

# 构建生产
npm run build
```

## 故障排查

### 常见问题
1. **Lsyncd 同步失败**
   - 检查 SSH 连接
   - 验证目录权限
   - 查看日志文件

2. **API 服务异常**
   - 检查数据库连接
   - 查看错误日志
   - 验证配置文件

3. **前端无法访问**
   - 检查 Nginx 配置
   - 验证 API 地址
   - 查看浏览器控制台

## 维护建议

### 日常维护
- 监控服务状态
- 检查磁盘空间
- 备份重要数据
- 更新安全补丁

### 定期维护
- 清理过期日志
- 优化数据库索引
- 评估系统性能
- 更新依赖版本

## 联系方式

- 项目负责人: [姓名]
- 技术支持: [邮箱]
- 问题反馈: [Issue Tracker]

## 更新日志

### v1.0.0 (2025-08-15)
- 初始版本发布
- 完成基础功能
- 支持文件同步和管理
- Web 界面上线

---

本文档会随项目发展持续更新，请关注最新版本。
