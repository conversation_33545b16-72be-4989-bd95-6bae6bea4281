# 测试文件管理系统部署运维文档

## 系统环境要求

### 硬件要求
- CPU: 4核以上
- 内存: 8GB以上
- 磁盘: 500GB以上（根据文件数量调整）
- 网络: 千兆网卡

### 软件要求
- 操作系统: Ubuntu 20.04/22.04 LTS
- PHP: 7.4+
- MySQL: 5.7+ 或 8.0+
- Nginx: 1.18+
- Redis: 6.0+
- Node.js: 16.0+
- Docker: 20.10+（可选）

## 部署架构

```
┌─────────────────────────────────────────────────────────┐
│                    负载均衡器 (Nginx)                     │
└─────────────────────────┬───────────────────────────────┘
                          │
        ┌─────────────────┴─────────────────┐
        │                                   │
┌───────▼────────┐                 ┌────────▼────────┐
│   前端服务      │                 │   后端服务       │
│  (Nginx静态)    │                 │   (Hyperf)      │
└────────────────┘                 └─────────────────┘
                                            │
                    ┌───────────────────────┼───────────────────────┐
                    │                       │                       │
            ┌───────▼────────┐     ┌────────▼────────┐    ┌────────▼────────┐
            │     MySQL      │     │     Redis       │    │   文件存储      │
            │   (主从复制)    │     │    (缓存)       │    │   (NFS/本地)    │
            └────────────────┘     └─────────────────┘    └─────────────────┘
```

## 服务部署

### 1. Lsyncd 部署配置

#### 1.1 安装 Lsyncd
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y lsyncd

# 创建配置目录
sudo mkdir -p /etc/lsyncd
sudo mkdir -p /var/log/lsyncd
```

#### 1.2 配置文件
```lua
-- /etc/lsyncd/lsyncd.conf.lua
settings {
    logfile    = "/var/log/lsyncd/lsyncd.log",
    statusFile = "/var/log/lsyncd/lsyncd.status",
    statusInterval = 10,
    nodaemon   = false,
    maxProcesses = 8,
    maxDelays = 10
}

sync {
    default.rsyncssh,
    source    = "/home/<USER>/FFMassData/",
    host      = "*************",
    targetdir = "/tmp/test",
    delete    = false,
    delay     = 5,
    rsync     = {
        archive  = true,
        compress = true,
        verbose  = true,
        _extra   = {"--bwlimit=10000"},
        rsh = "/usr/bin/ssh -i /home/<USER>/.ssh/id_rsa_lsyncd -p 2222 -l qinsx -o StrictHostKeyChecking=no"
    }
}
```

#### 1.3 Systemd 服务配置
```ini
# /etc/systemd/system/lsyncd.service
[Unit]
Description=Live Syncing Daemon
After=network.target

[Service]
Type=simple
ExecStart=/usr/bin/lsyncd -nodaemon /etc/lsyncd/lsyncd.conf.lua
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
User=firefly
Group=firefly

# 日志配置
StandardOutput=journal
StandardError=journal

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
```

#### 1.4 启动服务
```bash
# 重载配置
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable lsyncd

# 启动服务
sudo systemctl start lsyncd

# 查看状态
sudo systemctl status lsyncd

# 查看日志
sudo journalctl -u lsyncd -f
```

### 2. 后端服务部署

#### 2.1 PHP 环境安装
```bash
# 安装 PHP 7.4
sudo apt-get install -y php7.4-fpm php7.4-cli php7.4-mysql \
    php7.4-redis php7.4-curl php7.4-mbstring php7.4-xml \
    php7.4-zip php7.4-bcmath php7.4-gd

# 安装 Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

#### 2.2 Hyperf 项目部署
```bash
# 克隆项目
cd /var/www
git clone https://github.com/your-repo/test-file-manager-backend.git
cd test-file-manager-backend

# 安装依赖
composer install --no-dev --optimize-autoloader

# 配置环境
cp .env.example .env
vim .env  # 编辑数据库等配置

# 生成配置
php bin/hyperf.php vendor:publish
```

#### 2.3 Hyperf 服务配置
```ini
# /etc/systemd/system/hyperf.service
[Unit]
Description=Hyperf HTTP Server
After=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/var/www/test-file-manager-backend
ExecStart=/usr/bin/php /var/www/test-file-manager-backend/bin/hyperf.php start
ExecReload=/bin/kill -USR1 $MAINPID
Restart=always
RestartSec=5

# 环境变量
Environment="APP_ENV=production"
Environment="SCAN_CACHEABLE=true"

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
```

#### 2.4 定时任务配置
```bash
# 编辑 crontab
sudo crontab -e

# 添加文件重排定时任务
0 * * * * /usr/bin/php /var/www/test-file-manager-backend/bin/hyperf.php reorganize:run >> /var/log/reorganize.log 2>&1
```

### 3. 前端部署

#### 3.1 构建前端项目
```bash
# 安装 Node.js
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs

# 构建项目
cd /var/www/test-file-manager-frontend
npm install
npm run build
```

#### 3.2 Nginx 配置
```nginx
# /etc/nginx/sites-available/test-file-manager
server {
    listen 80;
    server_name test-file-manager.example.com;
    
    # 前端静态文件
    root /var/www/test-file-manager-frontend/dist;
    index index.html;
    
    # 前端路由
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API 反向代理
    location /api {
        proxy_pass http://127.0.0.1:9501;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 文件下载
    location /download {
        alias /data/test-files/;
        autoindex off;
        
        # 安全限制
        if ($request_filename ~ "\.\.") {
            return 403;
        }
    }
    
    # 日志
    access_log /var/log/nginx/test-file-manager.access.log;
    error_log /var/log/nginx/test-file-manager.error.log;
}

# HTTPS 配置
server {
    listen 443 ssl http2;
    server_name test-file-manager.example.com;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    # 其他配置同上...
}
```

### 4. 数据库部署

#### 4.1 MySQL 安装配置
```bash
# 安装 MySQL
sudo apt-get install -y mysql-server

# 安全设置
sudo mysql_secure_installation

# 优化配置
sudo vim /etc/mysql/mysql.conf.d/mysqld.cnf
```

#### 4.2 MySQL 性能优化配置
```ini
# /etc/mysql/mysql.conf.d/mysqld.cnf
[mysqld]
# 基础设置
max_connections = 500
max_connect_errors = 100
wait_timeout = 600
interactive_timeout = 600

# 缓冲区设置
key_buffer_size = 256M
max_allowed_packet = 64M
table_open_cache = 2048
sort_buffer_size = 2M
read_buffer_size = 2M
read_rnd_buffer_size = 8M
join_buffer_size = 2M

# InnoDB 设置
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_log_buffer_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_file_per_table = 1
innodb_flush_method = O_DIRECT

# 查询缓存
query_cache_type = 1
query_cache_size = 64M
query_cache_limit = 2M

# 日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

#### 4.3 数据库备份策略
```bash
#!/bin/bash
# /usr/local/bin/mysql-backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/mysql"
DB_NAME="test_file_manager"
DB_USER="backup"
DB_PASS="backup_password"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
mysqldump -u$DB_USER -p$DB_PASS --single-transaction --quick --lock-tables=false $DB_NAME | gzip > $BACKUP_DIR/backup_$DATE.sql.gz

# 删除7天前的备份
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +7 -delete

# 添加到 crontab
# 0 2 * * * /usr/local/bin/mysql-backup.sh
```

### 5. Redis 部署

```bash
# 安装 Redis
sudo apt-get install -y redis-server

# 配置 Redis
sudo vim /etc/redis/redis.conf
```

```conf
# /etc/redis/redis.conf
bind 127.0.0.1
protected-mode yes
port 6379
maxmemory 1gb
maxmemory-policy allkeys-lru
appendonly yes
appendfsync everysec
```

## 监控方案

### 1. 系统监控

#### 1.1 安装 Prometheus + Grafana
```bash
# 使用 Docker Compose 部署
cat > docker-compose.monitoring.yml << EOF
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    restart: always

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    volumes:
      - grafana_data:/var/lib/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    restart: always

  node_exporter:
    image: prom/node-exporter:latest
    container_name: node_exporter
    ports:
      - "9100:9100"
    restart: always

volumes:
  prometheus_data:
  grafana_data:
EOF

docker-compose -f docker-compose.monitoring.yml up -d
```

#### 1.2 监控指标
- 系统资源：CPU、内存、磁盘、网络
- 服务状态：Lsyncd、Hyperf、MySQL、Redis
- 业务指标：文件同步数量、处理速度、错误率

### 2. 日志管理

#### 2.1 日志收集配置
```bash
# 安装 rsyslog
sudo apt-get install -y rsyslog

# 配置日志轮转
cat > /etc/logrotate.d/test-file-manager << EOF
/var/log/lsyncd/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 firefly firefly
    postrotate
        systemctl reload lsyncd > /dev/null 2>&1 || true
    endscript
}

/var/log/nginx/test-file-manager*.log {
    daily
    rotate 14
    compress
    delaycompress
    missingok
    notifempty
    create 644 www-data www-data
    sharedscripts
    postrotate
        nginx -s reload > /dev/null 2>&1 || true
    endscript
}
EOF
```

#### 2.2 日志分析脚本
```bash
#!/bin/bash
# /usr/local/bin/analyze-logs.sh

# 分析 Lsyncd 日志
echo "=== Lsyncd 同步统计 ==="
grep "synced" /var/log/lsyncd/lsyncd.log | wc -l
echo "同步成功文件数："
grep "failed" /var/log/lsyncd/lsyncd.log | wc -l
echo "同步失败文件数："

# 分析 API 访问日志
echo "=== API 访问统计 ==="
awk '{print $7}' /var/log/nginx/test-file-manager.access.log | sort | uniq -c | sort -rn | head -10
```

### 3. 告警配置

#### 3.1 告警规则
```yaml
# prometheus/alert.rules.yml
groups:
  - name: test-file-manager
    rules:
      - alert: ServiceDown
        expr: up{job="hyperf"} == 0
        for: 5m
        annotations:
          summary: "Hyperf service is down"
          
      - alert: HighCPUUsage
        expr: cpu_usage_percent > 80
        for: 10m
        annotations:
          summary: "CPU usage is above 80%"
          
      - alert: DiskSpaceLow
        expr: disk_free_percent < 20
        for: 5m
        annotations:
          summary: "Disk space is below 20%"
          
      - alert: FileSyncError
        expr: file_sync_error_rate > 0.1
        for: 5m
        annotations:
          summary: "File sync error rate is above 10%"
```

## 性能优化

### 1. 系统优化
```bash
# /etc/sysctl.conf
# 网络优化
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 8192
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_fin_timeout = 30

# 文件系统优化
fs.file-max = 1000000
fs.inotify.max_user_watches = 524288

# 应用配置
sudo sysctl -p
```

### 2. PHP-FPM 优化
```ini
# /etc/php/7.4/fpm/pool.d/www.conf
pm = dynamic
pm.max_children = 50
pm.start_servers = 20
pm.min_spare_servers = 10
pm.max_spare_servers = 30
pm.max_requests = 500
```

## 故障处理

### 1. 常见问题处理

#### Lsyncd 同步失败
```bash
# 检查服务状态
sudo systemctl status lsyncd

# 查看错误日志
sudo tail -f /var/log/lsyncd/lsyncd.log

# 手动测试 SSH 连接
ssh -i /home/<USER>/.ssh/id_rsa_lsyncd -p 2222 qinsx@*************

# 重启服务
sudo systemctl restart lsyncd
```

#### 后端服务异常
```bash
# 检查进程
ps aux | grep hyperf

# 查看错误日志
tail -f /var/www/test-file-manager-backend/runtime/logs/hyperf.log

# 重启服务
sudo systemctl restart hyperf
```

### 2. 应急恢复流程

1. **服务恢复优先级**
   - MySQL > Redis > 后端服务 > 前端服务 > Lsyncd

2. **数据恢复**
   ```bash
   # 恢复 MySQL 数据
   gunzip < /backup/mysql/backup_latest.sql.gz | mysql -u root -p test_file_manager
   
   # 重建文件索引
   php /var/www/test-file-manager-backend/bin/hyperf.php rebuild:index
   ```

3. **验证恢复**
   - 检查各服务状态
   - 验证数据完整性
   - 测试核心功能

## 维护计划

### 日常维护
- 检查服务状态（每天）
- 查看错误日志（每天）
- 监控磁盘空间（每天）
- 备份数据库（每天）

### 周期维护
- 清理过期日志（每周）
- 分析性能报告（每周）
- 更新系统补丁（每月）
- 容量规划评估（每季度）

### 维护脚本
```bash
#!/bin/bash
# /usr/local/bin/daily-maintenance.sh

echo "=== 日常维护检查 $(date) ==="

# 检查服务状态
for service in lsyncd hyperf mysql redis nginx; do
    echo -n "检查 $service 服务: "
    systemctl is-active $service
done

# 检查磁盘空间
echo "=== 磁盘空间 ==="
df -h | grep -E "^/dev|Filesystem"

# 检查数据库连接
echo "=== 数据库状态 ==="
mysql -e "SELECT COUNT(*) as file_count FROM test_file_manager.file_sync;"

# 发送报告
# mail -s "日常维护报告 $(date +%Y-%m-%d)" <EMAIL> < maintenance.log
```
