# 测试文件管理系统数据库设计

## 数据库概述

数据库名称：`test_file_manager`
字符集：`utf8mb4`
排序规则：`utf8mb4_unicode_ci`
存储引擎：`InnoDB`

## 表结构设计

### 1. 文件同步记录表 (file_sync)

存储所有同步文件的元信息。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 主键ID |
| product | VARCHAR(64) | NOT NULL, INDEX | 产品名称 |
| sn | VARCHAR(64) | NOT NULL, INDEX | 序列号 |
| date_folder | VARCHAR(32) | NOT NULL, INDEX | 日期文件夹 |
| test_datetime | VARCHAR(32) | NOT NULL | 测试日期时间 |
| test_type | VARCHAR(32) | | 测试类型 (AgingTest/FactoryTest等) |
| filename | VARCHAR(255) | NOT NULL | 文件名 |
| file_size | BIGINT | | 文件大小(字节) |
| file_mtime | DATETIME | | 文件修改时间 |
| src_path | VARCHAR(512) | | 源文件路径 |
| dst_path | VARCHAR(512) | UNIQUE | 目标文件路径 |
| file_md5 | CHAR(32) | | 文件MD5值 |
| sync_status | TINYINT | DEFAULT 1 | 同步状态: 1-已同步, 2-已重排, 3-已删除 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

索引：
- UNIQUE KEY `uniq_dst_path` (`dst_path`)
- INDEX `idx_product_sn_date` (`product`, `sn`, `date_folder`)
- INDEX `idx_sync_status` (`sync_status`)
- INDEX `idx_created_at` (`created_at`)

### 2. 产品信息表 (products)

存储产品的基本信息。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 主键ID |
| product_name | VARCHAR(64) | UNIQUE, NOT NULL | 产品名称 |
| product_code | VARCHAR(32) | UNIQUE | 产品代码 |
| description | TEXT | | 产品描述 |
| status | TINYINT | DEFAULT 1 | 状态: 1-启用, 0-禁用 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

### 3. 测试记录表 (test_records)

存储每次测试的汇总信息。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 主键ID |
| product_id | INT | FOREIGN KEY | 产品ID |
| sn | VARCHAR(64) | NOT NULL, INDEX | 序列号 |
| test_date | DATE | NOT NULL, INDEX | 测试日期 |
| test_datetime | VARCHAR(32) | NOT NULL | 测试日期时间 |
| test_types | JSON | | 测试类型列表 |
| file_count | INT | DEFAULT 0 | 文件数量 |
| total_size | BIGINT | DEFAULT 0 | 总文件大小 |
| result | VARCHAR(32) | | 测试结果 |
| remarks | TEXT | | 备注 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

索引：
- INDEX `idx_product_sn` (`product_id`, `sn`)
- INDEX `idx_test_date` (`test_date`)
- UNIQUE KEY `uniq_test` (`product_id`, `sn`, `test_datetime`)

### 4. 目录重排任务表 (reorganize_tasks)

记录目录重排任务的执行情况。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 主键ID |
| task_type | VARCHAR(32) | NOT NULL | 任务类型: manual/scheduled |
| source_dir | VARCHAR(512) | | 源目录 |
| target_dir | VARCHAR(512) | | 目标目录 |
| status | TINYINT | DEFAULT 0 | 状态: 0-待处理, 1-处理中, 2-完成, 3-失败 |
| total_files | INT | DEFAULT 0 | 总文件数 |
| processed_files | INT | DEFAULT 0 | 已处理文件数 |
| error_message | TEXT | | 错误信息 |
| started_at | DATETIME | | 开始时间 |
| completed_at | DATETIME | | 完成时间 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

### 5. 操作日志表 (operation_logs)

记录系统的重要操作日志。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 主键ID |
| user_id | INT | | 用户ID |
| operation_type | VARCHAR(32) | NOT NULL | 操作类型 |
| operation_desc | VARCHAR(255) | | 操作描述 |
| target_type | VARCHAR(32) | | 目标类型 |
| target_id | VARCHAR(64) | | 目标ID |
| request_data | JSON | | 请求数据 |
| response_data | JSON | | 响应数据 |
| ip_address | VARCHAR(45) | | IP地址 |
| user_agent | VARCHAR(255) | | 用户代理 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

索引：
- INDEX `idx_user_operation` (`user_id`, `operation_type`)
- INDEX `idx_created_at` (`created_at`)

## 数据关系图

```
products (1) ─────┐
                  │
                  ▼ (n)
            test_records ────────────┐
                  │                  │
                  │ (1)              │
                  ▼ (n)              │
             file_sync               │
                  │                  │
                  └──────────────────┘
                  
reorganize_tasks (独立表)
operation_logs (独立表)
```

## 性能优化建议

1. **索引优化**
   - 为常用查询字段建立复合索引
   - 定期分析查询日志，优化慢查询

2. **分区策略**
   - file_sync 表按 created_at 月份分区
   - operation_logs 表按 created_at 月份分区

3. **数据归档**
   - 超过6个月的日志数据归档到历史表
   - 定期清理已删除的文件记录

4. **查询优化**
   - 使用分页查询限制返回数据量
   - 大数据量统计使用汇总表缓存
