# 目录重排实现方案

## 概述

目录重排服务负责将 Lsyncd 同步到临时目录的文件，按照新的目录结构重新组织，并将文件信息写入数据库。

## 目录结构转换规则

### 原始目录结构
```
/tmp/test/
└── 20250815/                    # 日期文件夹
    └── AIO-3576-JD4/           # 产品名称
        └── demo1111/           # SN序列号
            └── 20250815-014408/# 具体测试时间
                ├── AgingTest/  # 测试类型
                ├── FactoryTest/
                └── cpuid.txt
```

### 目标目录结构
```
/data/test-files/
└── AIO-3576-JD4/               # 产品名称
    └── demo1111/               # SN序列号
        └── 20250815-014408/    # 具体测试时间
            ├── AgingTest/
            ├── FactoryTest/
            └── cpuid.txt
```

## 实现架构

### 1. 核心服务类

```php
<?php
declare(strict_types=1);

namespace App\Service;

use App\Model\FileSync;
use App\Model\ReorganizeTask;
use Carbon\Carbon;
use Hyperf\DbConnection\Db;
use Hyperf\Logger\LoggerFactory;
use Psr\Log\LoggerInterface;

class ReorganizeService
{
    /**
     * @var LoggerInterface
     */
    protected $logger;
    
    protected $sourceDir = '/tmp/test';
    protected $targetDir = '/data/test-files';
    
    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('reorganize');
    }
    
    /**
     * 执行目录重排任务
     */
    public function execute(array $options = []): array
    {
        $task = $this->createTask($options);
        
        try {
            $this->updateTaskStatus($task->id, 1); // 处理中
            
            $sourceDir = $options['source_dir'] ?? $this->sourceDir;
            $targetDir = $options['target_dir'] ?? $this->targetDir;
            
            $files = $this->scanDirectory($sourceDir);
            $processedCount = 0;
            
            foreach ($files as $file) {
                if ($this->processFile($file, $sourceDir, $targetDir)) {
                    $processedCount++;
                }
            }
            
            $this->updateTaskStatus($task->id, 2, $processedCount); // 完成
            
            return [
                'task_id' => $task->id,
                'processed' => $processedCount,
                'total' => count($files)
            ];
            
        } catch (\Exception $e) {
            $this->logger->error('重排任务失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);
            
            $this->updateTaskStatus($task->id, 3, 0, $e->getMessage()); // 失败
            throw $e;
        }
    }
    
    /**
     * 扫描目录获取文件列表
     */
    protected function scanDirectory(string $dir): array
    {
        $files = [];
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dir, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $files[] = $file->getPathname();
            }
        }
        
        return $files;
    }
    
    /**
     * 处理单个文件
     */
    protected function processFile(string $filePath, string $sourceBase, string $targetBase): bool
    {
        // 解析文件路径
        $pathInfo = $this->parseFilePath($filePath, $sourceBase);
        if (!$pathInfo) {
            $this->logger->warning('无法解析文件路径', ['file' => $filePath]);
            return false;
        }
        
        // 检查是否已处理
        $existing = FileSync::where('src_path', $filePath)
            ->where('sync_status', '!=', 3)
            ->first();
            
        if ($existing) {
            $this->logger->debug('文件已存在', ['file' => $filePath]);
            return false;
        }
        
        // 构建目标路径
        $targetPath = $this->buildTargetPath($pathInfo, $targetBase);
        
        // 创建目标目录
        $targetDir = dirname($targetPath);
        if (!is_dir($targetDir)) {
            mkdir($targetDir, 0755, true);
        }
        
        // 复制文件
        if (!copy($filePath, $targetPath)) {
            $this->logger->error('文件复制失败', [
                'source' => $filePath,
                'target' => $targetPath
            ]);
            return false;
        }
        
        // 计算文件信息
        $fileInfo = $this->getFileInfo($filePath);
        
        // 写入数据库
        $record = new FileSync();
        $record->product = $pathInfo['product'];
        $record->sn = $pathInfo['sn'];
        $record->date_folder = $pathInfo['date_folder'];
        $record->test_datetime = $pathInfo['test_datetime'];
        $record->test_type = $pathInfo['test_type'];
        $record->filename = $pathInfo['filename'];
        $record->file_size = $fileInfo['size'];
        $record->file_mtime = Carbon::createFromTimestamp($fileInfo['mtime']);
        $record->src_path = $filePath;
        $record->dst_path = $targetPath;
        $record->file_md5 = $fileInfo['md5'];
        $record->sync_status = 2; // 已重排
        $record->save();
        
        $this->logger->info('文件处理成功', [
            'file' => $pathInfo['filename'],
            'product' => $pathInfo['product'],
            'sn' => $pathInfo['sn']
        ]);
        
        return true;
    }
    
    /**
     * 解析文件路径
     */
    protected function parseFilePath(string $filePath, string $baseDir): ?array
    {
        $relativePath = str_replace($baseDir . '/', '', $filePath);
        $parts = explode('/', $relativePath);
        
        // 期望格式: 日期/产品/SN/测试时间/[测试类型/]文件名
        if (count($parts) < 5) {
            return null;
        }
        
        $dateFolder = $parts[0];
        $product = $parts[1];
        $sn = $parts[2];
        $testDatetime = $parts[3];
        
        // 判断是否有测试类型目录
        if (count($parts) == 6) {
            $testType = $parts[4];
            $filename = $parts[5];
        } else {
            $testType = null;
            $filename = $parts[4];
        }
        
        return [
            'date_folder' => $dateFolder,
            'product' => $product,
            'sn' => $sn,
            'test_datetime' => $testDatetime,
            'test_type' => $testType,
            'filename' => $filename,
            'relative_path' => $relativePath
        ];
    }
    
    /**
     * 构建目标路径
     */
    protected function buildTargetPath(array $pathInfo, string $targetBase): string
    {
        $path = $targetBase . '/' . $pathInfo['product'] . '/' . 
                $pathInfo['sn'] . '/' . $pathInfo['test_datetime'];
                
        if ($pathInfo['test_type']) {
            $path .= '/' . $pathInfo['test_type'];
        }
        
        $path .= '/' . $pathInfo['filename'];
        
        return $path;
    }
    
    /**
     * 获取文件信息
     */
    protected function getFileInfo(string $filePath): array
    {
        return [
            'size' => filesize($filePath),
            'mtime' => filemtime($filePath),
            'md5' => md5_file($filePath)
        ];
    }
    
    /**
     * 创建任务记录
     */
    protected function createTask(array $options): ReorganizeTask
    {
        $task = new ReorganizeTask();
        $task->task_type = $options['task_type'] ?? 'manual';
        $task->source_dir = $options['source_dir'] ?? $this->sourceDir;
        $task->target_dir = $options['target_dir'] ?? $this->targetDir;
        $task->status = 0;
        $task->save();
        
        return $task;
    }
    
    /**
     * 更新任务状态
     */
    protected function updateTaskStatus(int $taskId, int $status, int $processed = 0, ?string $error = null): void
    {
        $task = ReorganizeTask::find($taskId);
        if (!$task) {
            return;
        }
        
        $task->status = $status;
        
        if ($status === 1) {
            $task->started_at = Carbon::now();
        } elseif ($status === 2) {
            $task->completed_at = Carbon::now();
            $task->processed_files = $processed;
        } elseif ($status === 3) {
            $task->completed_at = Carbon::now();
            $task->error_message = $error;
        }
        
        $task->save();
    }
}
```

### 2. 定时任务实现

```php
<?php
declare(strict_types=1);

namespace App\Command;

use App\Service\ReorganizeService;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Hyperf\Di\Annotation\Inject;

/**
 * @Command
 */
class ReorganizeCommand extends HyperfCommand
{
    /**
     * @Inject
     * @var ReorganizeService
     */
    protected $reorganizeService;
    
    protected ?string $signature = 'reorganize:run {--source=} {--target=}';
    
    protected string $description = '执行文件重排任务';
    
    public function handle()
    {
        $this->info('开始执行文件重排任务...');
        
        $options = [
            'task_type' => 'scheduled',
            'source_dir' => $this->option('source') ?: '/tmp/test',
            'target_dir' => $this->option('target') ?: '/data/test-files'
        ];
        
        try {
            $result = $this->reorganizeService->execute($options);
            
            $this->info(sprintf(
                '任务完成! 任务ID: %d, 处理文件数: %d/%d',
                $result['task_id'],
                $result['processed'],
                $result['total']
            ));
        } catch (\Exception $e) {
            $this->error('任务执行失败: ' . $e->getMessage());
        }
    }
}
```

### 3. 定时任务配置

```php
<?php
// config/autoload/crontab.php

use Hyperf\Crontab\Crontab;

return [
    'enable' => true,
    'crontab' => [
        // 每小时执行一次文件重排
        (new Crontab())
            ->setName('reorganize-files')
            ->setRule('0 * * * *')
            ->setCallback([App\Command\ReorganizeCommand::class, 'handle'])
            ->setMemo('定时执行文件重排任务'),
    ],
];
```

## 性能优化策略

### 1. 批量处理
- 使用事务批量插入数据库记录
- 批量创建目录结构
- 使用队列异步处理大量文件

### 2. 并发处理
```php
use Hyperf\Utils\Coroutine;
use Hyperf\Utils\Parallel;

protected function processFilesInParallel(array $files, string $sourceBase, string $targetBase): int
{
    $parallel = new Parallel();
    $processedCount = 0;
    
    foreach (array_chunk($files, 100) as $chunk) {
        $parallel->add(function () use ($chunk, $sourceBase, $targetBase) {
            $count = 0;
            foreach ($chunk as $file) {
                if ($this->processFile($file, $sourceBase, $targetBase)) {
                    $count++;
                }
            }
            return $count;
        });
    }
    
    $results = $parallel->wait();
    foreach ($results as $result) {
        $processedCount += $result;
    }
    
    return $processedCount;
}
```

### 3. 增量处理
- 记录上次处理时间
- 只处理新增或修改的文件
- 使用文件监控事件触发处理

## 错误处理和恢复

### 1. 断点续传
- 记录处理进度
- 支持从失败点恢复
- 避免重复处理

### 2. 日志记录
- 详细记录每个文件的处理状态
- 错误日志包含完整上下文
- 支持日志分析和统计

### 3. 监控告警
- 任务执行时间监控
- 失败率统计
- 磁盘空间预警

## 部署建议

1. **资源配置**
   - 根据文件数量调整内存限制
   - 配置合适的执行超时时间
   - 使用 SSD 提升 IO 性能

2. **并发控制**
   - 限制同时执行的任务数量
   - 使用分布式锁防止重复执行
   - 合理设置协程数量

3. **数据清理**
   - 定期清理已删除的文件记录
   - 归档历史数据
   - 清理临时文件
