# 测试文件管理系统后端 API 设计文档

## API 概述

基于 Hyperf 框架，遵循 RESTful 设计规范，提供文件管理、目录重排、数据查询等功能接口。

### 基本信息
- **基础路径**: `/api/v1`
- **数据格式**: JSON
- **认证方式**: JWT Token
- **PHP版本**: 7.4
- **框架版本**: Hyperf 2.2

### 响应格式
```json
{
    "code": 0,
    "message": "success",
    "data": {},
    "timestamp": 1629456789
}
```

## API 接口列表

### 1. 认证相关

#### 1.1 用户登录
```
POST /api/v1/auth/login
```

**请求参数**:
```json
{
    "username": "admin",
    "password": "password"
}
```

**响应示例**:
```json
{
    "code": 0,
    "message": "登录成功",
    "data": {
        "token": "eyJ0eXAiOiJKV1QiLCJhbGc...",
        "expire_time": 1629460389,
        "user": {
            "id": 1,
            "username": "admin",
            "role": "admin"
        }
    }
}
```

### 2. 文件管理接口

#### 2.1 获取文件列表
```
GET /api/v1/files
```

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认20 |
| product | string | 否 | 产品名称 |
| sn | string | 否 | 序列号 |
| date_folder | string | 否 | 日期文件夹 |
| test_type | string | 否 | 测试类型 |
| sync_status | int | 否 | 同步状态 |

**响应示例**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "list": [
            {
                "id": 1,
                "product": "AIO-3576-JD4",
                "sn": "demo1111",
                "date_folder": "20250815",
                "test_datetime": "20250815-014408",
                "test_type": "FactoryTest",
                "filename": "factoryTest_result.txt",
                "file_size": 1024,
                "sync_status": 2,
                "created_at": "2025-08-15 14:44:08"
            }
        ],
        "total": 100,
        "page": 1,
        "page_size": 20
    }
}
```

#### 2.2 获取文件详情
```
GET /api/v1/files/{id}
```

**响应示例**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "id": 1,
        "product": "AIO-3576-JD4",
        "sn": "demo1111",
        "date_folder": "20250815",
        "test_datetime": "20250815-014408",
        "test_type": "FactoryTest",
        "filename": "factoryTest_result.txt",
        "file_size": 1024,
        "file_mtime": "2025-08-15 14:44:00",
        "src_path": "/tmp/test/20250815/AIO-3576-JD4/demo1111/20250815-014408/FactoryTest/factoryTest_result.txt",
        "dst_path": "/data/test-files/AIO-3576-JD4/demo1111/20250815-014408/FactoryTest/factoryTest_result.txt",
        "file_md5": "d41d8cd98f00b204e9800998ecf8427e",
        "sync_status": 2,
        "created_at": "2025-08-15 14:44:08",
        "updated_at": "2025-08-15 14:45:00"
    }
}
```

#### 2.3 下载文件
```
GET /api/v1/files/{id}/download
```

**响应**: 文件流

#### 2.4 删除文件
```
DELETE /api/v1/files/{id}
```

**响应示例**:
```json
{
    "code": 0,
    "message": "文件删除成功",
    "data": {
        "id": 1,
        "deleted": true
    }
}
```

### 3. 目录重排接口

#### 3.1 手动触发重排
```
POST /api/v1/reorganize
```

**请求参数**:
```json
{
    "source_dir": "/tmp/test/20250815",
    "target_dir": "/data/test-files",
    "date_range": {
        "start": "2025-08-15",
        "end": "2025-08-15"
    }
}
```

**响应示例**:
```json
{
    "code": 0,
    "message": "重排任务已创建",
    "data": {
        "task_id": 123,
        "status": "processing",
        "estimated_files": 150
    }
}
```

#### 3.2 获取重排任务状态
```
GET /api/v1/reorganize/tasks/{task_id}
```

**响应示例**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "id": 123,
        "task_type": "manual",
        "source_dir": "/tmp/test/20250815",
        "target_dir": "/data/test-files",
        "status": 2,
        "status_text": "完成",
        "total_files": 150,
        "processed_files": 150,
        "started_at": "2025-08-15 15:00:00",
        "completed_at": "2025-08-15 15:05:00"
    }
}
```

#### 3.3 获取重排任务列表
```
GET /api/v1/reorganize/tasks
```

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码 |
| page_size | int | 否 | 每页数量 |
| status | int | 否 | 任务状态 |

### 4. 产品管理接口

#### 4.1 获取产品列表
```
GET /api/v1/products
```

**响应示例**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "list": [
            {
                "id": 1,
                "product_name": "AIO-3576-JD4",
                "product_code": "AIO3576JD4",
                "description": "All-in-One 3576 JD4 型号",
                "status": 1,
                "created_at": "2025-08-15 10:00:00"
            }
        ],
        "total": 3
    }
}
```

#### 4.2 添加产品
```
POST /api/v1/products
```

**请求参数**:
```json
{
    "product_name": "NEW-PRODUCT",
    "product_code": "NEWPROD001",
    "description": "新产品描述"
}
```

### 5. 统计分析接口

#### 5.1 获取文件统计信息
```
GET /api/v1/statistics/files
```

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| group_by | string | 否 | 分组方式: product/date/test_type |
| date_range | object | 否 | 日期范围 |

**响应示例**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "summary": {
            "total_files": 1500,
            "total_size": 15728640,
            "total_products": 3,
            "total_sns": 45
        },
        "details": [
            {
                "product": "AIO-3576-JD4",
                "file_count": 500,
                "total_size": 5242880,
                "sn_count": 15
            }
        ]
    }
}
```

#### 5.2 获取测试记录统计
```
GET /api/v1/statistics/tests
```

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| product_id | int | 否 | 产品ID |
| date_range | object | 否 | 日期范围 |

### 6. 树形目录接口

#### 6.1 获取树形目录结构
```
GET /api/v1/tree
```

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| node | string | 否 | 节点路径，空为根节点 |
| depth | int | 否 | 展开深度，默认1 |

**响应示例**:
```json
{
    "code": 0,
    "message": "success",
    "data": [
        {
            "id": "AIO-3576-JD4",
            "label": "AIO-3576-JD4",
            "type": "product",
            "children_count": 2,
            "children": [
                {
                    "id": "AIO-3576-JD4/demo1111",
                    "label": "demo1111",
                    "type": "sn",
                    "children_count": 2,
                    "children": []
                }
            ]
        }
    ]
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1000 | 参数错误 |
| 1001 | 参数验证失败 |
| 2000 | 认证失败 |
| 2001 | Token过期 |
| 2002 | 无权限 |
| 3000 | 资源不存在 |
| 3001 | 文件不存在 |
| 4000 | 业务错误 |
| 4001 | 重排任务失败 |
| 5000 | 服务器错误 |

## 接口实现示例（Hyperf）

### Controller 示例
```php
<?php
declare(strict_types=1);

namespace App\Controller;

use App\Service\FileService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Contract\RequestInterface;

/**
 * @Controller(prefix="/api/v1/files")
 */
class FileController extends AbstractController
{
    /**
     * @Inject
     * @var FileService
     */
    protected $fileService;

    /**
     * @GetMapping(path="")
     */
    public function index(RequestInterface $request)
    {
        $params = $request->all();
        $result = $this->fileService->getList($params);
        
        return $this->success($result);
    }
    
    /**
     * @GetMapping(path="{id:\d+}")
     */
    public function show(int $id)
    {
        $file = $this->fileService->getById($id);
        
        if (!$file) {
            return $this->error('文件不存在', 3001);
        }
        
        return $this->success($file);
    }
}
```

### Service 示例
```php
<?php
declare(strict_types=1);

namespace App\Service;

use App\Model\FileSync;
use Hyperf\DbConnection\Db;

class FileService
{
    public function getList(array $params): array
    {
        $page = (int) ($params['page'] ?? 1);
        $pageSize = (int) ($params['page_size'] ?? 20);
        
        $query = FileSync::query();
        
        if (!empty($params['product'])) {
            $query->where('product', $params['product']);
        }
        
        if (!empty($params['sn'])) {
            $query->where('sn', $params['sn']);
        }
        
        $total = $query->count();
        $list = $query->forPage($page, $pageSize)->get();
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize
        ];
    }
    
    public function getById(int $id): ?array
    {
        $file = FileSync::find($id);
        return $file ? $file->toArray() : null;
    }
}
```

## 部署注意事项

1. **路由配置**: 确保路由正确配置，避免路径变量冲突
2. **中间件**: 配置认证中间件、跨域中间件、日志中间件
3. **异常处理**: 统一异常处理，返回标准错误格式
4. **性能优化**: 使用连接池、协程、缓存等提升性能
5. **安全防护**: SQL注入防护、XSS防护、CSRF防护
