# 数据库迁移文件详细规划

## 迁移文件列表

1. 2025_08_15_000001_create_file_sync_table.php - 文件同步记录表
2. 2025_08_15_000002_create_products_table.php - 产品信息表
3. 2025_08_15_000003_create_test_records_table.php - 测试记录表
4. 2025_08_15_000004_create_aging_test_details_table.php - 老化测试详情表
5. 2025_08_15_000005_create_factory_test_items_table.php - 厂测项目详情表
6. 2025_08_15_000006_create_factory_test_summary_table.php - 厂测汇总表
7. 2025_08_15_000007_create_reorganize_tasks_table.php - 重排任务表
8. 2025_08_15_000008_create_operation_logs_table.php - 操作日志表

## 迁移文件详细实现

### 1. 文件同步记录表 (2025_08_15_000001_create_file_sync_table.php)

```php
<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFileSyncTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bi_file_sync', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('product', 64)->index()->comment('产品名称');
            $table->string('sn', 64)->index()->comment('序列号');
            $table->string('date_folder', 32)->index()->comment('日期文件夹');
            $table->string('test_datetime', 32)->comment('测试日期时间');
            $table->string('test_type', 32)->nullable()->comment('测试类型');
            $table->string('filename', 255)->comment('文件名');
            $table->bigInteger('file_size')->nullable()->comment('文件大小(字节)');
            $table->dateTime('file_mtime')->nullable()->comment('文件修改时间');
            $table->string('src_path', 512)->nullable()->comment('源文件路径');
            $table->string('dst_path', 512)->unique()->comment('目标文件路径');
            $table->char('file_md5', 32)->nullable()->comment('文件MD5值');
            $table->tinyInteger('sync_status')->default(1)->comment('同步状态: 1-已同步, 2-已重排, 3-已删除');
            $table->timestamps();
            
            // 复合索引
            $table->index(['product', 'sn', 'date_folder']);
            $table->index('sync_status');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bi_file_sync');
    }
}
```

### 2. 产品信息表 (2025_08_15_000002_create_products_table.php)

```php
<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bi_file_products', function (Blueprint $table) {
            $table->increments('id');
            $table->string('product_name', 64)->unique()->comment('产品名称');
            $table->string('product_code', 32)->nullable()->unique()->comment('产品代码');
            $table->text('description')->nullable()->comment('产品描述');
            $table->tinyInteger('status')->default(1)->comment('状态: 1-启用, 0-禁用');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bi_file_products');
    }
}
```

### 3. 测试记录表 (2025_08_15_000003_create_test_records_table.php)

```php
<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateTestRecordsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bi_file_test_records', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedInteger('product_id')->comment('产品ID');
            $table->string('sn', 64)->index()->comment('序列号');
            $table->string('cpuid', 64)->nullable()->index()->comment('CPU ID');
            $table->date('test_date')->index()->comment('测试日期');
            $table->string('test_datetime', 32)->comment('测试日期时间');
            $table->json('test_types')->nullable()->comment('测试类型列表');
            $table->integer('file_count')->default(0)->comment('文件数量');
            $table->bigInteger('total_size')->default(0)->comment('总文件大小');
            $table->string('factory_test_result', 32)->nullable()->comment('厂测结果');
            $table->string('aging_test_result', 32)->nullable()->comment('老化测试结果');
            $table->string('firmware_version', 255)->nullable()->comment('固件版本');
            $table->string('factory_test_version', 64)->nullable()->comment('厂测版本');
            $table->string('ddr_size', 32)->nullable()->comment('DDR大小');
            $table->string('flash_size', 32)->nullable()->comment('Flash大小');
            $table->text('remarks')->nullable()->comment('备注');
            $table->timestamps();
            
            // 索引
            $table->index(['product_id', 'sn']);
            $table->unique(['product_id', 'sn', 'test_datetime']);
            
            // 外键
            $table->foreign('product_id')->references('id')->on('bi_file_products');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bi_file_test_records');
    }
}
```

### 4. 老化测试详情表 (2025_08_15_000004_create_aging_test_details_table.php)

```php
<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateAgingTestDetailsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bi_file_aging_test_details', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('test_record_id')->index()->comment('测试记录ID');
            $table->string('soc_model', 32)->nullable()->comment('SOC型号');
            $table->integer('cpu_cores')->nullable()->comment('CPU核心数');
            $table->string('cpu_freq', 128)->nullable()->comment('CPU频率信息');
            $table->integer('gpu_freq')->nullable()->comment('GPU频率');
            $table->integer('npu_freq')->nullable()->comment('NPU频率');
            $table->integer('ddr_freq')->nullable()->comment('DDR频率');
            $table->integer('cpu_temp')->nullable()->comment('CPU温度');
            $table->integer('gpu_temp')->nullable()->comment('GPU温度');
            $table->string('npu_load', 128)->nullable()->comment('NPU负载信息');
            $table->integer('total_memory')->nullable()->comment('总内存(MB)');
            $table->integer('available_memory')->nullable()->comment('可用内存(MB)');
            $table->string('run_time', 32)->nullable()->comment('运行时间');
            $table->integer('abnormal_restart_count')->nullable()->comment('异常重启次数');
            $table->integer('aging_test_count')->nullable()->comment('老化测试次数');
            $table->timestamp('created_at')->nullable();
            
            // 外键
            $table->foreign('test_record_id')->references('id')->on('bi_file_test_records');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bi_file_aging_test_details');
    }
}
```

### 5. 厂测项目详情表 (2025_08_15_000005_create_factory_test_items_table.php)

```php
<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFactoryTestItemsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bi_file_factory_test_items', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('test_record_id')->index()->comment('测试记录ID');
            $table->string('item_name', 64)->comment('测试项目名称');
            $table->string('test_status', 32)->nullable()->comment('测试状态');
            $table->string('test_result', 32)->nullable()->comment('测试结果');
            $table->text('test_details')->nullable()->comment('测试细节');
            $table->timestamp('created_at')->nullable();
            
            // 索引
            $table->index(['item_name', 'test_result']);
            
            // 外键
            $table->foreign('test_record_id')->references('id')->on('bi_file_test_records');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bi_file_factory_test_items');
    }
}
```

### 6. 厂测汇总表 (2025_08_15_000006_create_factory_test_summary_table.php)

```php
<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFactoryTestSummaryTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bi_file_factory_test_summary', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('test_record_id')->unique()->comment('测试记录ID');
            $table->string('device_name', 64)->nullable()->comment('设备名称');
            $table->string('cpuid', 64)->nullable()->index()->comment('CPU ID');
            $table->string('factory_version', 64)->nullable()->comment('厂测版本');
            $table->string('firmware_version', 255)->nullable()->comment('固件版本');
            $table->string('ddr_size', 32)->nullable()->comment('DDR大小');
            $table->string('flash_size', 32)->nullable()->comment('Flash大小');
            $table->json('success_items')->nullable()->comment('成功项目列表');
            $table->json('failed_items')->nullable()->comment('失败项目列表');
            $table->integer('success_count')->default(0)->comment('成功项目数');
            $table->integer('failed_count')->default(0)->comment('失败项目数');
            $table->string('final_result', 32)->nullable()->comment('最终结果');
            $table->timestamp('created_at')->nullable();
            
            // 索引
            $table->index('final_result');
            
            // 外键
            $table->foreign('test_record_id')->references('id')->on('bi_file_test_records');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bi_file_factory_test_summary');
    }
}
```

### 7. 重排任务表 (2025_08_15_000007_create_reorganize_tasks_table.php)

```php
<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateReorganizeTasksTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bi_file_reorganize_tasks', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('task_type', 32)->comment('任务类型: manual/scheduled');
            $table->string('source_dir', 512)->nullable()->comment('源目录');
            $table->string('target_dir', 512)->nullable()->comment('目标目录');
            $table->tinyInteger('status')->default(0)->comment('状态: 0-待处理, 1-处理中, 2-完成, 3-失败');
            $table->integer('total_files')->default(0)->comment('总文件数');
            $table->integer('processed_files')->default(0)->comment('已处理文件数');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->dateTime('started_at')->nullable()->comment('开始时间');
            $table->dateTime('completed_at')->nullable()->comment('完成时间');
            $table->timestamp('created_at')->nullable();
            
            // 索引
            $table->index('status');
            $table->index('task_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bi_file_reorganize_tasks');
    }
}
```

### 8. 操作日志表 (2025_08_15_000008_create_operation_logs_table.php)

```php
<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateOperationLogsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bi_file_operation_logs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedInteger('user_id')->nullable()->comment('用户ID');
            $table->string('operation_type', 32)->comment('操作类型');
            $table->string('operation_desc', 255)->nullable()->comment('操作描述');
            $table->string('target_type', 32)->nullable()->comment('目标类型');
            $table->string('target_id', 64)->nullable()->comment('目标ID');
            $table->json('request_data')->nullable()->comment('请求数据');
            $table->json('response_data')->nullable()->comment('响应数据');
            $table->string('ip_address', 45)->nullable()->comment('IP地址');
            $table->string('user_agent', 255)->nullable()->comment('用户代理');
            $table->timestamp('created_at')->nullable();
            
            // 索引
            $table->index(['user_id', 'operation_type']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bi_file_operation_logs');
    }
}
```

## 执行迁移命令

在 Hyperf 项目中执行迁移：

```bash
# 执行所有迁移
php bin/hyperf.php migrate

# 回滚迁移
php bin/hyperf.php migrate:rollback

# 重新执行所有迁移
php bin/hyperf.php migrate:refresh

# 查看迁移状态
php bin/hyperf.php migrate:status
```

## 模型文件示例

### FileSync 模型 (app/Model/FileSync.php)

```php
<?php

declare(strict_types=1);

namespace App\Model;

use Hyperf\DbConnection\Model\Model;

/**
 * @property int $id
 * @property string $product
 * @property string $sn
 * @property string $date_folder
 * @property string $test_datetime
 * @property string $test_type
 * @property string $filename
 * @property int $file_size
 * @property string $file_mtime
 * @property string $src_path
 * @property string $dst_path
 * @property string $file_md5
 * @property int $sync_status
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class FileSync extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'bi_file_sync';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'product', 'sn', 'date_folder', 'test_datetime', 'test_type',
        'filename', 'file_size', 'file_mtime', 'src_path', 'dst_path',
        'file_md5', 'sync_status'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'file_size' => 'integer',
        'sync_status' => 'integer',
        'file_mtime' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
}
```

## 注意事项

1. **字符集设置**: 所有表使用 `utf8mb4` 字符集和 `utf8mb4_unicode_ci` 排序规则
2. **索引优化**: 根据查询需求建立合适的索引
3. **外键约束**: 在生产环境可根据需要决定是否使用外键约束
4. **数据类型**: 注意 PHP 7.4 的兼容性，避免使用 PHP 8.0+ 特性
5. **迁移顺序**: 确保表的创建顺序正确，有外键关联的表后创建

## 数据库配置 (config/autoload/databases.php)

```php
<?php

declare(strict_types=1);

return [
    'default' => [
        'driver' => env('DB_DRIVER', 'mysql'),
        'host' => env('DB_HOST', 'localhost'),
        'database' => env('DB_DATABASE', 'test_file_manager'),
        'port' => env('DB_PORT', 3306),
        'username' => env('DB_USERNAME', 'root'),
        'password' => env('DB_PASSWORD', ''),
        'charset' => env('DB_CHARSET', 'utf8mb4'),
        'collation' => env('DB_COLLATION', 'utf8mb4_unicode_ci'),
        'prefix' => env('DB_PREFIX', ''),
        'pool' => [
            'min_connections' => 1,
            'max_connections' => 10,
            'connect_timeout' => 10.0,
            'wait_timeout' => 3.0,
            'heartbeat' => -1,
            'max_idle_time' => (float) env('DB_MAX_IDLE_TIME', 60),
        ],
        'commands' => [
            'gen:model' => [
                'path' => 'app/Model',
                'force_casts' => true,
                'inheritance' => 'Model',
            ],
        ],
    ],
];
```
