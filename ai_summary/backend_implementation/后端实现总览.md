# 测试文件管理系统后端实现规划（简化版）

## 项目基本信息

- **项目名称**: tchip_bi_backend
- **技术栈**: PHP 7.4 + Hyperf 2.2
- **项目位置**: /home/<USER>/Project/tchipbi/tchip_bi_backend
- **数据库**: MySQL 5.7+
- **缓存**: Redis 6.0+

## 简化后的目录结构

```
tchip_bi_backend/
├── app/
│   ├── Controller/            # 控制器（简化为2个）
│   │   ├── TestFileController.php  # 统一文件管理控制器
│   │   └── AdminController.php     # 管理功能控制器
│   ├── Service/               # 服务层（简化为2个）
│   │   ├── FileManagerService.php  # 文件管理服务
│   │   └── ReorganizeService.php   # 目录重排服务
│   ├── Model/                 # 模型层
│   │   ├── FileSync.php
│   │   ├── Product.php
│   │   ├── TestRecord.php
│   │   ├── AgingTestDetail.php
│   │   ├── FactoryTestItem.php
│   │   ├── FactoryTestSummary.php
│   │   ├── ReorganizeTask.php
│   │   └── OperationLog.php
│   ├── Command/               # 命令行任务
│   │   └── ReorganizeCommand.php
│   ├── Middleware/            # 中间件
│   │   ├── AuthMiddleware.php
│   │   └── CorsMiddleware.php
│   ├── Exception/             # 异常处理
│   │   └── Handler/
│   │       └── AppExceptionHandler.php
│   └── Constants/             # 常量定义
│       ├── ErrorCode.php
│       └── StatusCode.php
├── config/                    # 配置文件
│   ├── autoload/
│   │   ├── databases.php
│   │   ├── redis.php
│   │   ├── middlewares.php
│   │   └── crontab.php
│   └── routes.php            # 路由配置
├── migrations/                # 数据库迁移文件
│   ├── 2025_08_15_000001_create_file_sync_table.php
│   ├── 2025_08_15_000002_create_products_table.php
│   ├── 2025_08_15_000003_create_test_records_table.php
│   ├── 2025_08_15_000004_create_aging_test_details_table.php
│   ├── 2025_08_15_000005_create_factory_test_items_table.php
│   ├── 2025_08_15_000006_create_factory_test_summary_table.php
│   ├── 2025_08_15_000007_create_reorganize_tasks_table.php
│   └── 2025_08_15_000008_create_operation_logs_table.php
├── runtime/                   # 运行时文件
├── test/                      # 测试文件
└── composer.json             # 依赖配置
```

## 核心功能模块

### 1. 文件管理模块
- 文件列表查询（分页、筛选）
- 文件详情获取
- 文件删除
- 文件下载
- 树形目录结构

### 2. 目录重排模块
- 手动触发重排
- 定时任务执行
- 任务状态管理
- 文件路径解析
- 测试文件内容解析

### 3. 产品管理模块
- 产品列表管理
- 产品信息维护
- 产品统计分析

### 4. 统计分析模块
- 文件统计
- 测试记录统计
- 产品分布分析
- 数据导出

### 5. 认证授权模块
- JWT Token 认证
- 用户登录/登出
- 权限控制

## 数据库表设计

### 主要表结构
1. **bi_file_sync** - 文件同步记录表
2. **bi_file_products** - 产品信息表
3. **bi_file_test_records** - 测试记录表
4. **bi_file_aging_test_details** - 老化测试详情表
5. **bi_file_factory_test_items** - 厂测项目详情表
6. **bi_file_factory_test_summary** - 厂测汇总表
7. **bi_file_reorganize_tasks** - 重排任务表
8. **bi_file_operation_logs** - 操作日志表

## API 路由设计

- 使用注解路由

## 开发规范

### PHP 编码规范
- 遵循 PSR-12 编码规范
- 使用 PHP 7.4 语法特性
- 避免使用 PHP 8.0+ 的特性（如 #[] 注解）
- 使用 @Annotation 而非 #[Attribute]

### Hyperf 规范
- Controller 只负责接收请求和返回响应
- 业务逻辑放在 Service 层
- 数据库操作通过 Model 层
- 使用依赖注入而非直接实例化

### 命名规范
- 类名：PascalCase
- 方法名：camelCase
- 变量名：camelCase
- 数据库表名：snake_case
- 模型属性：snake_case

## 异常处理

```php
// 统一错误码
class ErrorCode
{
    const SUCCESS = 0;
    const PARAM_ERROR = 1000;
    const AUTH_FAILED = 2000;
    const NOT_FOUND = 3000;
    const BUSINESS_ERROR = 4000;
    const SERVER_ERROR = 5000;
}
```

## 性能优化

1. **数据库优化**
   - 合理使用索引
   - 批量操作使用事务
   - 大数据量使用分页

2. **协程优化**
   - 使用协程进行并发处理
   - 合理控制协程数量
   - 避免协程内阻塞操作

3. **文件处理优化**
   - 批量处理文件
   - 使用队列异步处理


## 部署配置

### 环境要求
- PHP >= 7.4
- Swoole >= 4.5
- MySQL >= 5.7
- Redis >= 6.0

### 配置文件
```env
# .env
APP_NAME=test_file_manager
APP_ENV=production

DB_DRIVER=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=test_file_manager
DB_USERNAME=root
DB_PASSWORD=password

REDIS_HOST=localhost
REDIS_AUTH=
REDIS_PORT=6379
REDIS_DB=0

JWT_SECRET=your-secret-key
JWT_TTL=7200
```

## 定时任务

```php
// config/autoload/crontab.php
use Hyperf\Crontab\Crontab;

return [
    'enable' => true,
    'crontab' => [
        // 每小时执行文件重排
        (new Crontab())
            ->setName('reorganize-files')
            ->setRule('0 * * * *')
            ->setCallback([App\Command\ReorganizeCommand::class, 'handle'])
            ->setMemo('定时执行文件重排任务'),
    ],
];
```

## 测试计划

1. **单元测试**
   - Service 层业务逻辑测试
   - Model 层数据操作测试

2. **功能测试**
   - API 接口测试
   - 文件处理流程测试

3. **性能测试**
   - 并发请求测试
   - 大文件处理测试

4. **集成测试**
   - 完整业务流程测试
