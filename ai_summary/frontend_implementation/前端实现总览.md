# 测试文件管理系统前端实现规划（简化版）

## 项目基本信息

- **项目名称**: tchip_bi_frontend  
- **技术栈**: Vue 3 + Element Plus + Vite
- **项目位置**: /home/<USER>/Project/tchipbi/tchip_bi_frontend
- **开发语言**: JavaScript (非TypeScript)

## 简化后的目录结构

```
tchip_bi_frontend/
├── src/
│   ├── api/                    # API 接口定义
│   │   └── index.js            # 统一接口文件
│   ├── assets/                 # 静态资源
│   ├── components/             # 公共组件（精简）
│   │   ├── FileTree.vue       # 文件树组件
│   │   └── FileList.vue       # 文件列表组件
│   ├── router/                # 路由配置
│   │   └── index.js          # 单一路由定义
│   ├── stores/                # Pinia 状态管理
│   │   └── index.js          # 统一状态管理
│   ├── utils/                 # 工具函数
│   │   ├── request.js        # axios 封装
│   │   └── format.js         # 格式化工具
│   ├── views/                 # 页面组件（仅一个主页面）
│   │   └── MainView.vue      # 主页面（包含所有tabs）
│   ├── App.vue                # 根组件
│   └── main.js                # 入口文件
├── public/                     # 公共资源
├── index.html                  # HTML 模板
├── vite.config.js             # Vite 配置
└── package.json               # 项目配置
```

## 核心功能模块

### 1. 文件管理模块
- 树形目录结构展示
- 文件列表查询和筛选
- 文件下载和删除
- 批量操作支持

### 2. 目录重排模块
- 手动触发重排任务
- 任务状态监控
- 任务历史记录
- 任务详情查看

### 3. 统计分析模块
- 文件统计图表
- 产品分布分析
- 测试结果汇总
- 数据导出功能

### 4. 系统管理模块
- 用户认证
- 产品管理
- 系统配置
- 操作日志

## 特殊要求注意事项

### 样式命名规范
- 所有样式类名必须包含文件名前缀，如 `test-file-manager-main`
- 避免使用过于简短的类名
- 使用 BEM 命名规范

### 组件使用规范
- 使用 `firefly-dialog` 替代 `el-dialog`
- 使用 `$baseMessage` 替代 `ElMessage`
- 使用 `$baseConfirm` 替代 `ElMessageBox`
- 表格查询项使用 `VabQueryForm` 组件包裹

### 代码风格规范
- 使用 `async/await` 替代 `setTimeout`
- 使用 Composition API
- 避免使用 TypeScript 类型定义

## 路由设计

```javascript
const routes = [
  {
    path: '/',
    component: MainLayout,
    redirect: '/file-manager',
    children: [
      {
        path: 'file-manager',
        name: 'FileManager',
        component: () => import('@/views/FileManager/index.vue'),
        meta: { title: '文件管理' }
      },
      {
        path: 'reorganize',
        name: 'Reorganize',
        component: () => import('@/views/Reorganize/index.vue'),
        meta: { title: '目录重排' }
      },
      {
        path: 'statistics',
        name: 'Statistics',
        component: () => import('@/views/Statistics/index.vue'),
        meta: { title: '统计分析' }
      },
      {
        path: 'products',
        name: 'Products',
        component: () => import('@/views/Products/index.vue'),
        meta: { title: '产品管理' }
      }
    ]
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login/index.vue')
  }
]
```

## API 接口设计

### 基础配置
```javascript
// utils/request.js
const baseURL = process.env.VITE_API_BASE_URL || 'http://*************:9501/api/v1'
```

### 接口列表
1. 文件管理接口
   - GET /files - 获取文件列表
   - GET /files/:id - 获取文件详情
   - DELETE /files/:id - 删除文件
   - GET /files/:id/download - 下载文件

2. 目录重排接口
   - POST /reorganize - 创建重排任务
   - GET /reorganize/tasks - 获取任务列表
   - GET /reorganize/tasks/:id - 获取任务详情

3. 统计分析接口
   - GET /statistics/files - 文件统计
   - GET /statistics/tests - 测试统计
   - GET /statistics/products - 产品统计

## 开发规范

### 文件命名
- 组件文件：PascalCase (如 FileTree.vue)
- 普通 JS 文件：camelCase (如 request.js)
- 样式文件：kebab-case (如 file-manager.scss)

### 注释规范
- 所有组件必须有功能说明注释
- 复杂逻辑必须添加注释
- API 接口必须注明参数和返回值

### Git 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式
- refactor: 重构
- test: 测试
- chore: 构建或辅助工具

## 部署配置

### 环境变量
```
# .env.development
VITE_API_BASE_URL=http://localhost:9501/api/v1

# .env.production
VITE_API_BASE_URL=http://*************:9501/api/v1
```

### 构建配置
```javascript
// vite.config.js
export default {
  base: '/test-file-manager/',
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false
  }
}
```

## 性能优化

1. 路由懒加载
2. 组件按需引入
3. 图片懒加载
4. 虚拟滚动（大数据表格）
5. 请求缓存和防抖

## 测试计划

1. 单元测试：核心工具函数
2. 组件测试：关键组件功能
3. E2E 测试：主要业务流程
4. 性能测试：大数据量场景
