# 前端组件详细实现规划（简化版）

## 核心实现

### 1. 主页面组件 (views/MainView.vue) - 使用el-tabs切换

```vue
<template>
  <div class="test-file-manager-container">
    <!-- 搜索区域 -->
    <VabQueryForm>
      <vab-query-form-left-panel :span="18">
        <el-form :inline="true" :model="queryForm" class="test-file-manager-search-form">
          <el-form-item label="产品名称">
            <el-select v-model="queryForm.product" placeholder="请选择产品" clearable>
              <el-option 
                v-for="item in products" 
                :key="item.id"
                :label="item.product_name"
                :value="item.product_name"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="序列号">
            <el-input v-model="queryForm.sn" placeholder="请输入序列号" clearable />
          </el-form-item>
          <el-form-item label="测试类型">
            <el-select v-model="queryForm.test_type" placeholder="请选择测试类型" clearable>
              <el-option label="老化测试" value="AgingTest" />
              <el-option label="厂测" value="FactoryTest" />
            </el-select>
          </el-form-item>
          <el-form-item label="日期范围">
            <el-date-picker
              v-model="queryForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="6">
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button type="success" @click="handleReorganize">手动重排</el-button>
      </vab-query-form-right-panel>
    </VabQueryForm>

    <!-- 主体内容区 -->
    <el-row :gutter="20" class="test-file-manager-content">
      <!-- 左侧树形目录 -->
      <el-col :span="6">
        <FileTree 
          :data="treeData"
          @node-click="handleNodeClick"
          @load-node="handleLoadNode"
        />
      </el-col>
      
      <!-- 右侧文件列表 -->
      <el-col :span="18">
        <FileList 
          :data="fileList"
          :loading="loading"
          :pagination="pagination"
          @page-change="handlePageChange"
          @download="handleDownload"
          @delete="handleDelete"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, inject } from 'vue'
import { useFilesStore } from '@/stores/files'
import { useProductsStore } from '@/stores/products'
import FileTree from '@/components/FileTree/index.vue'
import FileList from '@/components/FileList/index.vue'
import { getFileList, deleteFile, downloadFile } from '@/api/files'
import { triggerReorganize } from '@/api/reorganize'

const $baseMessage = inject('$baseMessage')
const $baseConfirm = inject('$baseConfirm')

const filesStore = useFilesStore()
const productsStore = useProductsStore()

// 数据定义
const queryForm = reactive({
  product: '',
  sn: '',
  test_type: '',
  dateRange: []
})

const fileList = ref([])
const loading = ref(false)
const treeData = ref([])
const products = ref([])

const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 方法定义
const handleQuery = async () => {
  loading.value = true
  try {
    const params = {
      ...queryForm,
      page: pagination.page,
      page_size: pagination.pageSize
    }
    
    if (queryForm.dateRange && queryForm.dateRange.length === 2) {
      params.start_date = queryForm.dateRange[0]
      params.end_date = queryForm.dateRange[1]
    }
    
    const { data } = await getFileList(params)
    fileList.value = data.list
    pagination.total = data.total
  } catch (error) {
    $baseMessage('查询失败: ' + error.message, 'error')
  } finally {
    loading.value = false
  }
}

const handleReset = () => {
  queryForm.product = ''
  queryForm.sn = ''
  queryForm.test_type = ''
  queryForm.dateRange = []
  pagination.page = 1
  handleQuery()
}

const handleReorganize = () => {
  $baseConfirm(
    '确认手动触发目录重排任务？',
    '提示',
    async () => {
      try {
        await triggerReorganize()
        $baseMessage('重排任务已创建', 'success', 'vab-hey-message-success')
      } catch (error) {
        $baseMessage('创建任务失败: ' + error.message, 'error')
      }
    }
  )
}

const handleNodeClick = (node) => {
  // 根据节点类型构建查询条件
  if (node.type === 'product') {
    queryForm.product = node.label
  } else if (node.type === 'sn') {
    queryForm.sn = node.label
  }
  handleQuery()
}

const handleLoadNode = async (node, resolve) => {
  // 动态加载树节点
  // 实现延迟加载逻辑
}

const handlePageChange = (page, pageSize) => {
  pagination.page = page
  pagination.pageSize = pageSize
  handleQuery()
}

const handleDownload = async (file) => {
  try {
    await downloadFile(file.id)
    $baseMessage('下载成功', 'success', 'vab-hey-message-success')
  } catch (error) {
    $baseMessage('下载失败: ' + error.message, 'error')
  }
}

const handleDelete = (file) => {
  $baseConfirm(
    `确认删除文件 ${file.filename}？`,
    '警告',
    async () => {
      try {
        await deleteFile(file.id)
        $baseMessage('删除成功', 'success', 'vab-hey-message-success')
        handleQuery()
      } catch (error) {
        $baseMessage('删除失败: ' + error.message, 'error')
      }
    }
  )
}

onMounted(async () => {
  // 加载产品列表
  await productsStore.fetchProducts()
  products.value = productsStore.productList
  
  // 加载文件列表
  handleQuery()
})
</script>

<style scoped lang="scss">
.test-file-manager-container {
  padding: 20px;
  
  .test-file-manager-search-form {
    width: 100%;
  }
  
  .test-file-manager-content {
    margin-top: 20px;
    min-height: calc(100vh - 200px);
  }
}
</style>
```

### 2. 文件树组件 (components/FileTree/index.vue)

```vue
<template>
  <el-card class="file-tree-card">
    <template #header>
      <span class="file-tree-title">目录结构</span>
      <el-button 
        type="text" 
        size="small" 
        @click="refreshTree"
        class="file-tree-refresh"
      >
        刷新
      </el-button>
    </template>
    
    <el-tree
      ref="treeRef"
      :data="treeData"
      :props="treeProps"
      :load="loadNode"
      lazy
      node-key="id"
      highlight-current
      :expand-on-click-node="false"
      @node-click="handleNodeClick"
      class="file-tree-component"
    >
      <template #default="{ node, data }">
        <span class="file-tree-node">
          <el-icon class="file-tree-icon">
            <Folder v-if="data.type === 'folder' || data.type === 'product' || data.type === 'sn'" />
            <Document v-else />
          </el-icon>
          <span class="file-tree-label">{{ node.label }}</span>
          <span class="file-tree-count" v-if="data.children_count">
            ({{ data.children_count }})
          </span>
        </span>
      </template>
    </el-tree>
  </el-card>
</template>

<script setup>
import { ref } from 'vue'
import { Folder, Document } from '@element-plus/icons-vue'
import { getTreeData } from '@/api/files'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['node-click', 'load-node'])

const treeRef = ref()
const treeData = ref(props.data)

const treeProps = {
  label: 'label',
  children: 'children',
  isLeaf: 'isLeaf'
}

const loadNode = async (node, resolve) => {
  if (node.level === 0) {
    // 加载根节点
    const { data } = await getTreeData({ node: '', depth: 1 })
    resolve(data)
  } else {
    // 加载子节点
    const { data } = await getTreeData({ 
      node: node.data.id, 
      depth: 1 
    })
    resolve(data)
  }
}

const handleNodeClick = (data, node) => {
  emit('node-click', data, node)
}

const refreshTree = () => {
  treeRef.value.store.root.childNodes = []
  loadNode({ level: 0 }, (data) => {
    treeData.value = data
  })
}
</script>

<style scoped lang="scss">
.file-tree-card {
  height: calc(100vh - 240px);
  
  :deep(.el-card__header) {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  :deep(.el-card__body) {
    overflow-y: auto;
    padding: 10px;
  }
  
  .file-tree-title {
    font-weight: bold;
    font-size: 14px;
  }
  
  .file-tree-refresh {
    padding: 0;
  }
  
  .file-tree-component {
    .file-tree-node {
      display: flex;
      align-items: center;
      font-size: 14px;
      
      .file-tree-icon {
        margin-right: 5px;
        color: #409eff;
      }
      
      .file-tree-label {
        flex: 1;
      }
      
      .file-tree-count {
        margin-left: 5px;
        color: #909399;
        font-size: 12px;
      }
    }
  }
}
</style>
```

### 3. 文件列表组件 (components/FileList/index.vue)

```vue
<template>
  <el-card class="file-list-card">
    <el-table 
      :data="data" 
      :loading="loading"
      stripe
      style="width: 100%"
      class="file-list-table"
    >
      <el-table-column type="selection" width="55" />
      
      <el-table-column prop="filename" label="文件名" min-width="200">
        <template #default="{ row }">
          <div class="file-list-filename">
            <el-icon><Document /></el-icon>
            <span>{{ row.filename }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="product" label="产品" width="120" />
      <el-table-column prop="sn" label="序列号" width="120" />
      <el-table-column prop="test_type" label="测试类型" width="100">
        <template #default="{ row }">
          <el-tag v-if="row.test_type === 'AgingTest'" type="primary">老化测试</el-tag>
          <el-tag v-else-if="row.test_type === 'FactoryTest'" type="success">厂测</el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="file_size" label="文件大小" width="100">
        <template #default="{ row }">
          {{ formatFileSize(row.file_size) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="created_at" label="同步时间" width="160" />
      
      <el-table-column prop="sync_status" label="状态" width="80">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.sync_status)">
            {{ getStatusText(row.sync_status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button 
            type="primary" 
            size="small" 
            @click="handleDownload(row)"
          >
            下载
          </el-button>
          <el-button 
            type="danger" 
            size="small" 
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="pagination.total"
      :page-sizes="[20, 50, 100, 200]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      class="file-list-pagination"
    />
  </el-card>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Document } from '@element-plus/icons-vue'
import { formatFileSize } from '@/utils/format'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  pagination: {
    type: Object,
    default: () => ({
      page: 1,
      pageSize: 20,
      total: 0
    })
  }
})

const emit = defineEmits(['page-change', 'download', 'delete'])

const currentPage = computed({
  get: () => props.pagination.page,
  set: (val) => emit('page-change', val, props.pagination.pageSize)
})

const pageSize = computed({
  get: () => props.pagination.pageSize,
  set: (val) => emit('page-change', props.pagination.page, val)
})

const getStatusType = (status) => {
  const statusMap = {
    1: 'info',
    2: 'success',
    3: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    1: '已同步',
    2: '已重排',
    3: '已删除'
  }
  return statusMap[status] || '未知'
}

const handleDownload = (row) => {
  emit('download', row)
}

const handleDelete = (row) => {
  emit('delete', row)
}

const handleSizeChange = (val) => {
  emit('page-change', 1, val)
}

const handleCurrentChange = (val) => {
  emit('page-change', val, props.pagination.pageSize)
}
</script>

<style scoped lang="scss">
.file-list-card {
  .file-list-table {
    .file-list-filename {
      display: flex;
      align-items: center;
      
      .el-icon {
        margin-right: 5px;
        color: #409eff;
      }
    }
  }
  
  .file-list-pagination {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
```

## API 接口实现

### 文件管理接口 (api/files.js)

```javascript
import request from '@/utils/request'

// 获取文件列表
export function getFileList(params) {
  return request({
    url: '/files',
    method: 'get',
    params
  })
}

// 获取文件详情
export function getFileDetail(id) {
  return request({
    url: `/files/${id}`,
    method: 'get'
  })
}

// 删除文件
export function deleteFile(id) {
  return request({
    url: `/files/${id}`,
    method: 'delete'
  })
}

// 下载文件
export function downloadFile(id) {
  return request({
    url: `/files/${id}/download`,
    method: 'get',
    responseType: 'blob'
  })
}

// 获取树形目录
export function getTreeData(params) {
  return request({
    url: '/tree',
    method: 'get',
    params
  })
}
```

### 目录重排接口 (api/reorganize.js)

```javascript
import request from '@/utils/request'

// 手动触发重排
export function triggerReorganize(data) {
  return request({
    url: '/reorganize',
    method: 'post',
    data
  })
}

// 获取任务列表
export function getTaskList(params) {
  return request({
    url: '/reorganize/tasks',
    method: 'get',
    params
  })
}

// 获取任务详情
export function getTaskDetail(taskId) {
  return request({
    url: `/reorganize/tasks/${taskId}`,
    method: 'get'
  })
}
```

## 状态管理 (stores/files.js)

```javascript
import { defineStore } from 'pinia'
import { getFileList, getTreeData } from '@/api/files'

export const useFilesStore = defineStore('files', {
  state: () => ({
    fileList: [],
    treeData: [],
    selectedNode: null,
    loading: false,
    pagination: {
      page: 1,
      pageSize: 20,
      total: 0
    }
  }),
  
  actions: {
    async fetchFiles(params) {
      this.loading = true
      try {
        const { data } = await getFileList({
          ...params,
          page: this.pagination.page,
          page_size: this.pagination.pageSize
        })
        this.fileList = data.list
        this.pagination.total = data.total
      } finally {
        this.loading = false
      }
    },
    
    async fetchTreeData(node = '') {
      try {
        const { data } = await getTreeData({ node, depth: 1 })
        if (!node) {
          this.treeData = data
        }
        return data
      } catch (error) {
        console.error('Failed to fetch tree data:', error)
        return []
      }
    },
    
    setSelectedNode(node) {
      this.selectedNode = node
    },
    
    setPagination(page, pageSize) {
      this.pagination.page = page
      this.pagination.pageSize = pageSize
    }
  }
})
```

## 工具函数 (utils/format.js)

```javascript
/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
  if (!bytes) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化日期时间
 * @param {string|Date} date - 日期
 * @param {string} format - 格式
 * @returns {string} 格式化后的日期
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  // 实现日期格式化逻辑
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}
```
