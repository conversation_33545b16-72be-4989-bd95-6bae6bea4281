# 测试文件管理系统架构总览

## 系统概述

本系统旨在管理从 `/home/<USER>/FFMassData/` 目录同步到远端服务器的测试文件，实现文件的自动同步、目录重排、数据库记录和前端展示功能。

## 系统架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        本地测试设备                               │
│  /home/<USER>/FFMassData/                                      │
│  └── 20250815/                                                  │
│      └── AIO-3576-JD4/                                         │
│          └── demo1111/                                         │
│              └── 20250815-014408/                             │
│                  ├── AgingTest/                               │
│                  ├── FactoryTest/                             │
│                  └── cpuid.txt                                │
└─────────────────────────────────────────────────────────────────┘
                                  │
                                  │ Lsyncd 实时同步
                                  ▼
┌─────────────────────────────────────────────────────────────────┐
│                        远端服务器 (172.16.10.104)                │
│  临时目录: /tmp/test/                                            │
│  目标目录: /data/test-files/                                    │
│                                                                 │
│  ┌──────────────┐    ┌──────────────┐    ┌─────────────────┐  │
│  │   Lsyncd     │────▶│  后端服务     │────▶│   MySQL DB    │  │
│  │   守护进程    │    │  (Hyperf)    │    │               │  │
│  └──────────────┘    └──────────────┘    └─────────────────┘  │
│                              │                                  │
│                              │ RESTful API                      │
│                              ▼                                  │
│                     ┌──────────────┐                           │
│                     │   前端应用    │                           │
│                     │  (Vue3+EP)   │                           │
│                     └──────────────┘                           │
└─────────────────────────────────────────────────────────────────┘
```

## 核心组件说明

### 1. Lsyncd 同步服务
- **功能**: 实时监控本地目录变化，自动同步到远端服务器
- **源目录**: `/home/<USER>/FFMassData/`
- **目标目录**: `172.16.10.104:/tmp/test/`
- **同步方式**: rsyncssh，使用 SSH 密钥认证
- **特点**: 保持原始目录结构，不做重排

### 2. 后端服务 (Hyperf)
- **功能**: 
  - 目录重排：将临时目录文件重新组织到目标目录
  - 数据库操作：记录文件元信息
  - API 接口：提供前端调用的 RESTful API
- **技术栈**: PHP 7.4 + Hyperf 框架
- **部署位置**: 172.16.10.104

### 3. MySQL 数据库
- **功能**: 存储文件元信息、产品信息、测试记录等
- **主要表**: 
  - file_sync: 文件同步记录表
  - products: 产品信息表
  - test_records: 测试记录表

### 4. 前端应用
- **功能**: 
  - 树形目录展示
  - 文件列表查看
  - 手动触发重排
  - 文件下载/删除
- **技术栈**: Vue3 + Element Plus
- **部署方式**: Nginx 静态服务

## 数据流程

### 1. 文件同步流程
```
本地生成文件 → Lsyncd 检测 → SSH 同步 → 远端临时目录
```

### 2. 目录重排流程
```
临时目录 → 后端扫描 → 解析路径 → 重排到目标目录 → 写入数据库
```

原始路径：`/tmp/test/20250815/AIO-3576-JD4/demo1111/20250815-014408/`
目标路径：`/data/test-files/AIO-3576-JD4/demo1111/20250815-014408/`

### 3. 前端查询流程
```
前端请求 → API 接口 → 查询数据库 → 返回 JSON → 前端渲染
```

## 关键特性

### 1. 性能优化
- Lsyncd 仅负责同步，不做复杂处理
- 批量处理文件重排和数据库写入
- 数据库索引优化查询性能

### 2. 容错机制
- Lsyncd 自动重连机制
- 后端服务幂等性设计，防止重复处理
- 数据库唯一索引防止重复记录

### 3. 扩展性
- 支持百万级文件管理
- 模块化设计，易于扩展新功能
- API 接口标准化，支持多种前端接入

## 部署架构

### 1. 服务部署
- Lsyncd: systemd 管理，开机自启
- 后端服务: Docker 容器化部署
- 前端应用: Nginx 静态服务
- MySQL: Docker 容器或独立部署

### 2. 日志管理
- Lsyncd 日志: `/var/log/lsyncd.log`
- 后端服务日志: `/var/log/test-file-manager/`
- Nginx 访问日志: `/var/log/nginx/`

### 3. 监控告警
- 服务健康检查
- 磁盘空间监控
- 同步延迟监控
- 错误日志告警

## 安全考虑

1. SSH 密钥认证，禁用密码登录
2. API 接口鉴权，使用 JWT Token
3. 数据库访问控制，最小权限原则
4. 文件访问权限控制
5. 敏感信息加密存储
