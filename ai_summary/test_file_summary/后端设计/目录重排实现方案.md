# 目录重排实现方案

## 概述

目录重排服务负责将 Lsyncd 同步到临时目录的文件，按照新的目录结构重新组织，并将文件信息写入数据库。

## 目录结构转换规则

### 原始目录结构
```
/tmp/test/
└── 20250815/                    # 日期文件夹
    └── AIO-3576-JD4/           # 产品名称
        └── demo1111/           # SN序列号
            └── 20250815-014408/# 具体测试时间
                ├── AgingTest/  # 测试类型
                ├── FactoryTest/
                └── cpuid.txt
```

### 目标目录结构
```
/data/test-files/
└── AIO-3576-JD4/               # 产品名称
    └── demo1111/               # SN序列号
        └── 20250815-014408/    # 具体测试时间
            ├── AgingTest/
            ├── FactoryTest/
            └── cpuid.txt
```

## 实现架构

### 1. 核心服务类

```php
<?php
declare(strict_types=1);

namespace App\Service;

use App\Model\FileSync;
use App\Model\ReorganizeTask;
use Carbon\Carbon;
use Hyperf\DbConnection\Db;
use Hyperf\Logger\LoggerFactory;
use Psr\Log\LoggerInterface;

class ReorganizeService
{
    /**
     * @var LoggerInterface
     */
    protected $logger;
    
    protected $sourceDir = '/tmp/test';
    protected $targetDir = '/data/test-files';
    
    public function __construct(LoggerFactory $loggerFactory)
    {
        $this->logger = $loggerFactory->get('reorganize');
    }
    
    /**
     * 执行目录重排任务
     */
    public function execute(array $options = []): array
    {
        $task = $this->createTask($options);
        
        try {
            $this->updateTaskStatus($task->id, 1); // 处理中
            
            $sourceDir = $options['source_dir'] ?? $this->sourceDir;
            $targetDir = $options['target_dir'] ?? $this->targetDir;
            
            $files = $this->scanDirectory($sourceDir);
            $processedCount = 0;
            
            foreach ($files as $file) {
                if ($this->processFile($file, $sourceDir, $targetDir)) {
                    $processedCount++;
                }
            }
            
            $this->updateTaskStatus($task->id, 2, $processedCount); // 完成
            
            return [
                'task_id' => $task->id,
                'processed' => $processedCount,
                'total' => count($files)
            ];
            
        } catch (\Exception $e) {
            $this->logger->error('重排任务失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);
            
            $this->updateTaskStatus($task->id, 3, 0, $e->getMessage()); // 失败
            throw $e;
        }
    }
    
    /**
     * 扫描目录获取文件列表
     */
    protected function scanDirectory(string $dir): array
    {
        $files = [];
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dir, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $files[] = $file->getPathname();
            }
        }
        
        return $files;
    }
    
    /**
     * 处理单个文件
     */
    protected function processFile(string $filePath, string $sourceBase, string $targetBase): bool
    {
        // 解析文件路径
        $pathInfo = $this->parseFilePath($filePath, $sourceBase);
        if (!$pathInfo) {
            $this->logger->warning('无法解析文件路径', ['file' => $filePath]);
            return false;
        }
        
        // 检查是否已处理
        $existing = FileSync::where('src_path', $filePath)
            ->where('sync_status', '!=', 3)
            ->first();
            
        if ($existing) {
            $this->logger->debug('文件已存在', ['file' => $filePath]);
            return false;
        }
        
        // 构建目标路径
        $targetPath = $this->buildTargetPath($pathInfo, $targetBase);
        
        // 创建目标目录
        $targetDir = dirname($targetPath);
        if (!is_dir($targetDir)) {
            mkdir($targetDir, 0755, true);
        }
        
        // 复制文件
        if (!copy($filePath, $targetPath)) {
            $this->logger->error('文件复制失败', [
                'source' => $filePath,
                'target' => $targetPath
            ]);
            return false;
        }
        
        // 计算文件信息
        $fileInfo = $this->getFileInfo($filePath);
        
        // 写入数据库
        $record = new FileSync();
        $record->product = $pathInfo['product'];
        $record->sn = $pathInfo['sn'];
        $record->date_folder = $pathInfo['date_folder'];
        $record->test_datetime = $pathInfo['test_datetime'];
        $record->test_type = $pathInfo['test_type'];
        $record->filename = $pathInfo['filename'];
        $record->file_size = $fileInfo['size'];
        $record->file_mtime = Carbon::createFromTimestamp($fileInfo['mtime']);
        $record->src_path = $filePath;
        $record->dst_path = $targetPath;
        $record->file_md5 = $fileInfo['md5'];
        $record->sync_status = 2; // 已重排
        $record->save();
        
        // 解析测试文件内容
        $this->parseTestFile($targetPath, $pathInfo);
        
        $this->logger->info('文件处理成功', [
            'file' => $pathInfo['filename'],
            'product' => $pathInfo['product'],
            'sn' => $pathInfo['sn']
        ]);
        
        return true;
    }
    
    /**
     * 解析文件路径
     */
    protected function parseFilePath(string $filePath, string $baseDir): ?array
    {
        $relativePath = str_replace($baseDir . '/', '', $filePath);
        $parts = explode('/', $relativePath);
        
        // 期望格式: 日期/产品/SN/测试时间/[测试类型/]文件名
        if (count($parts) < 5) {
            return null;
        }
        
        $dateFolder = $parts[0];
        $product = $parts[1];
        $sn = $parts[2];
        $testDatetime = $parts[3];
        
        // 判断是否有测试类型目录
        if (count($parts) == 6) {
            $testType = $parts[4];
            $filename = $parts[5];
        } else {
            $testType = null;
            $filename = $parts[4];
        }
        
        return [
            'date_folder' => $dateFolder,
            'product' => $product,
            'sn' => $sn,
            'test_datetime' => $testDatetime,
            'test_type' => $testType,
            'filename' => $filename,
            'relative_path' => $relativePath
        ];
    }
    
    /**
     * 构建目标路径
     */
    protected function buildTargetPath(array $pathInfo, string $targetBase): string
    {
        $path = $targetBase . '/' . $pathInfo['product'] . '/' . 
                $pathInfo['sn'] . '/' . $pathInfo['test_datetime'];
                
        if ($pathInfo['test_type']) {
            $path .= '/' . $pathInfo['test_type'];
        }
        
        $path .= '/' . $pathInfo['filename'];
        
        return $path;
    }
    
    /**
     * 获取文件信息
     */
    protected function getFileInfo(string $filePath): array
    {
        return [
            'size' => filesize($filePath),
            'mtime' => filemtime($filePath),
            'md5' => md5_file($filePath)
        ];
    }
    
    /**
     * 创建任务记录
     */
    protected function createTask(array $options): ReorganizeTask
    {
        $task = new ReorganizeTask();
        $task->task_type = $options['task_type'] ?? 'manual';
        $task->source_dir = $options['source_dir'] ?? $this->sourceDir;
        $task->target_dir = $options['target_dir'] ?? $this->targetDir;
        $task->status = 0;
        $task->save();
        
        return $task;
    }
    
    /**
     * 更新任务状态
     */
    protected function updateTaskStatus(int $taskId, int $status, int $processed = 0, ?string $error = null): void
    {
        $task = ReorganizeTask::find($taskId);
        if (!$task) {
            return;
        }
        
        $task->status = $status;
        
        if ($status === 1) {
            $task->started_at = Carbon::now();
        } elseif ($status === 2) {
            $task->completed_at = Carbon::now();
            $task->processed_files = $processed;
        } elseif ($status === 3) {
            $task->completed_at = Carbon::now();
            $task->error_message = $error;
        }
        
        $task->save();
    }
    
    /**
     * 解析测试文件并保存详细信息
     */
    protected function parseTestFile(string $filePath, array $pathInfo): void
    {
        $filename = basename($filePath);
        
        // 获取或创建测试记录
        $testRecord = $this->getOrCreateTestRecord($pathInfo);
        
        switch ($filename) {
            case 'agingTest_result.txt':
                $this->parseAgingTestResult($filePath, $testRecord->id);
                break;
                
            case 'factoryTest_details.txt':
                $this->parseFactoryTestDetails($filePath, $testRecord->id);
                break;
                
            case 'factoryTest_result.txt':
                $this->parseFactoryTestResult($filePath, $testRecord->id);
                break;
                
            case 'cpuid.txt':
                $this->updateCpuId($filePath, $testRecord);
                break;
        }
    }
    
    /**
     * 获取或创建测试记录
     */
    protected function getOrCreateTestRecord(array $pathInfo): TestRecord
    {
        $product = Product::where('product_name', $pathInfo['product'])->first();
        if (!$product) {
            $product = new Product();
            $product->product_name = $pathInfo['product'];
            $product->save();
        }
        
        $testRecord = TestRecord::where('product_id', $product->id)
            ->where('sn', $pathInfo['sn'])
            ->where('test_datetime', $pathInfo['test_datetime'])
            ->first();
            
        if (!$testRecord) {
            $testRecord = new TestRecord();
            $testRecord->product_id = $product->id;
            $testRecord->sn = $pathInfo['sn'];
            $testRecord->test_date = Carbon::createFromFormat('Ymd', $pathInfo['date_folder'])->toDateString();
            $testRecord->test_datetime = $pathInfo['test_datetime'];
            $testRecord->save();
        }
        
        return $testRecord;
    }
    
    /**
     * 解析老化测试结果
     */
    protected function parseAgingTestResult(string $filePath, int $testRecordId): void
    {
        $content = file_get_contents($filePath);
        $lines = explode("\n", $content);
        
        $agingDetail = AgingTestDetails::where('test_record_id', $testRecordId)->first();
        if (!$agingDetail) {
            $agingDetail = new AgingTestDetails();
            $agingDetail->test_record_id = $testRecordId;
        }
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            if (preg_match('/SOC:(\w+),核心数:(\d+)/', $line, $matches)) {
                $agingDetail->soc_model = $matches[1];
                $agingDetail->cpu_cores = (int)$matches[2];
            }
            
            if (preg_match('/CPU\d+:(\d+)/', $line, $matches)) {
                $cpuFreq = $agingDetail->cpu_freq ? json_decode($agingDetail->cpu_freq, true) : [];
                $cpuFreq[] = $matches[1];
                $agingDetail->cpu_freq = json_encode($cpuFreq);
            }
            
            if (preg_match('/GPU:(\d+),NPU:(\d+),DDR:(\d+)/', $line, $matches)) {
                $agingDetail->gpu_freq = (int)$matches[1];
                $agingDetail->npu_freq = (int)$matches[2];
                $agingDetail->ddr_freq = (int)$matches[3];
            }
            
            if (preg_match('/CPU温度:\s*\*(\d+)\s*°C\*.*GPU温度:\s*\*(\d+)\s*°C\*/', $line, $matches)) {
                $agingDetail->cpu_temp = (int)$matches[1];
                $agingDetail->gpu_temp = (int)$matches[2];
            }
            
            if (preg_match('/NPU负载:(.+)/', $line, $matches)) {
                $agingDetail->npu_load = trim($matches[1]);
            }
            
            if (preg_match('/运行内存:(\d+)\s*MB,可用内存：(\d+)\s*MB/', $line, $matches)) {
                $agingDetail->total_memory = (int)$matches[1];
                $agingDetail->available_memory = (int)$matches[2];
            }
            
            if (preg_match('/运行时间:(.+)/', $line, $matches)) {
                $agingDetail->run_time = trim($matches[1]);
            }
            
            if (preg_match('/异常重启次数:(\d+)/', $line, $matches)) {
                $agingDetail->abnormal_restart_count = (int)$matches[1];
            }
            
            if (preg_match('/老化测试次数:(\d+)/', $line, $matches)) {
                $agingDetail->aging_test_count = (int)$matches[1];
            }
        }
        
        $agingDetail->save();
        
        // 更新测试记录
        TestRecord::where('id', $testRecordId)->update([
            'aging_test_result' => '完成'
        ]);
    }
    
    /**
     * 解析厂测详情
     */
    protected function parseFactoryTestDetails(string $filePath, int $testRecordId): void
    {
        $content = file_get_contents($filePath);
        $sections = preg_split('/\[测试项目\d+\]/', $content);
        
        foreach ($sections as $section) {
            if (empty(trim($section))) {
                continue;
            }
            
            $item = new FactoryTestItems();
            $item->test_record_id = $testRecordId;
            
            if (preg_match('/项目名:(.+)/', $section, $matches)) {
                $item->item_name = trim($matches[1]);
            }
            
            if (preg_match('/测试状态:(.+)/', $section, $matches)) {
                $item->test_status = trim($matches[1]);
            }
            
            if (preg_match('/测试结果:(.+)/', $section, $matches)) {
                $item->test_result = trim($matches[1]);
            }
            
            if (preg_match('/测试细节:(.+?)(?=\n\n|$)/s', $section, $matches)) {
                $item->test_details = trim($matches[1]);
            }
            
            if ($item->item_name) {
                $item->save();
            }
        }
    }
    
    /**
     * 解析厂测结果
     */
    protected function parseFactoryTestResult(string $filePath, int $testRecordId): void
    {
        $content = file_get_contents($filePath);
        
        $summary = FactoryTestSummary::where('test_record_id', $testRecordId)->first();
        if (!$summary) {
            $summary = new FactoryTestSummary();
            $summary->test_record_id = $testRecordId;
        }
        
        if (preg_match('/设备名:(.+)/', $content, $matches)) {
            $summary->device_name = trim($matches[1]);
        }
        
        if (preg_match('/CPUID:(.+)/', $content, $matches)) {
            $summary->cpuid = trim($matches[1]);
        }
        
        if (preg_match('/厂测版本:(.+)/', $content, $matches)) {
            $summary->factory_version = trim($matches[1]);
        }
        
        if (preg_match('/固件版本:(.+)/', $content, $matches)) {
            $summary->firmware_version = trim($matches[1]);
        }
        
        if (preg_match('/DDR:(.+)/', $content, $matches)) {
            $summary->ddr_size = trim($matches[1]);
        }
        
        if (preg_match('/Flash:(.+)/', $content, $matches)) {
            $summary->flash_size = trim($matches[1]);
        }
        
        if (preg_match('/成功\s*项目:(\d+)\s*\[(.+)\]/', $content, $matches)) {
            $summary->success_count = (int)$matches[1];
            $items = array_map('trim', explode(' ', trim($matches[2])));
            $summary->success_items = json_encode($items);
        }
        
        if (preg_match('/失败项目:(\d+)\s*\[(.+)\]/', $content, $matches)) {
            $summary->failed_count = (int)$matches[1];
            $items = array_map('trim', explode(' ', trim($matches[2])));
            $summary->failed_items = json_encode($items);
        }
        
        if (preg_match('/厂测结果:(.+)/', $content, $matches)) {
            $summary->final_result = trim($matches[1]);
        }
        
        $summary->save();
        
        // 更新测试记录
        TestRecord::where('id', $testRecordId)->update([
            'factory_test_result' => $summary->final_result,
            'cpuid' => $summary->cpuid,
            'firmware_version' => $summary->firmware_version,
            'factory_test_version' => $summary->factory_version,
            'ddr_size' => $summary->ddr_size,
            'flash_size' => $summary->flash_size
        ]);
    }
    
    /**
     * 更新 CPU ID
     */
    protected function updateCpuId(string $filePath, TestRecord $testRecord): void
    {
        $cpuid = trim(file_get_contents($filePath));
        if ($cpuid) {
            $testRecord->cpuid = $cpuid;
            $testRecord->save();
        }
    }
}
```

### 2. 定时任务实现

```php
<?php
declare(strict_types=1);

namespace App\Command;

use App\Service\ReorganizeService;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Hyperf\Di\Annotation\Inject;

/**
 * @Command
 */
class ReorganizeCommand extends HyperfCommand
{
    /**
     * @Inject
     * @var ReorganizeService
     */
    protected $reorganizeService;
    
    protected ?string $signature = 'reorganize:run {--source=} {--target=}';
    
    protected string $description = '执行文件重排任务';
    
    public function handle()
    {
        $this->info('开始执行文件重排任务...');
        
        $options = [
            'task_type' => 'scheduled',
            'source_dir' => $this->option('source') ?: '/tmp/test',
            'target_dir' => $this->option('target') ?: '/data/test-files'
        ];
        
        try {
            $result = $this->reorganizeService->execute($options);
            
            $this->info(sprintf(
                '任务完成! 任务ID: %d, 处理文件数: %d/%d',
                $result['task_id'],
                $result['processed'],
                $result['total']
            ));
        } catch (\Exception $e) {
            $this->error('任务执行失败: ' . $e->getMessage());
        }
    }
}
```

### 3. 定时任务配置

```php
<?php
// config/autoload/crontab.php

use Hyperf\Crontab\Crontab;

return [
    'enable' => true,
    'crontab' => [
        // 每小时执行一次文件重排
        (new Crontab())
            ->setName('reorganize-files')
            ->setRule('0 * * * *')
            ->setCallback([App\Command\ReorganizeCommand::class, 'handle'])
            ->setMemo('定时执行文件重排任务'),
    ],
];
```

## 性能优化策略

### 1. 批量处理
- 使用事务批量插入数据库记录
- 批量创建目录结构
- 使用队列异步处理大量文件

### 2. 并发处理
```php
use Hyperf\Utils\Coroutine;
use Hyperf\Utils\Parallel;

protected function processFilesInParallel(array $files, string $sourceBase, string $targetBase): int
{
    $parallel = new Parallel();
    $processedCount = 0;
    
    foreach (array_chunk($files, 100) as $chunk) {
        $parallel->add(function () use ($chunk, $sourceBase, $targetBase) {
            $count = 0;
            foreach ($chunk as $file) {
                if ($this->processFile($file, $sourceBase, $targetBase)) {
                    $count++;
                }
            }
            return $count;
        });
    }
    
    $results = $parallel->wait();
    foreach ($results as $result) {
        $processedCount += $result;
    }
    
    return $processedCount;
}
```

### 3. 增量处理
- 记录上次处理时间
- 只处理新增或修改的文件
- 使用文件监控事件触发处理

## 错误处理和恢复

### 1. 断点续传
- 记录处理进度
- 支持从失败点恢复
- 避免重复处理

### 2. 日志记录
- 详细记录每个文件的处理状态
- 错误日志包含完整上下文
- 支持日志分析和统计

### 3. 监控告警
- 任务执行时间监控
- 失败率统计
- 磁盘空间预警

## 部署建议

1. **资源配置**
   - 根据文件数量调整内存限制
   - 配置合适的执行超时时间
   - 使用 SSD 提升 IO 性能

2. **并发控制**
   - 限制同时执行的任务数量
   - 使用分布式锁防止重复执行
   - 合理设置协程数量

3. **数据清理**
   - 定期清理已删除的文件记录
   - 归档历史数据
   - 清理临时文件
