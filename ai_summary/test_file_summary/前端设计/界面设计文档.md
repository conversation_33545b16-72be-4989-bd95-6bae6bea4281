# 测试文件管理系统前端界面设计文档

## 技术栈

- **框架**: Vue 3.2+ (Composition API)
- **UI组件库**: Element Plus 2.3+
- **路由**: Vue Router 4
- **状态管理**: Pinia
- **HTTP请求**: Axios
- **图表**: ECharts 5
- **构建工具**: Vite

## 项目结构

```
frontend/
├── src/
│   ├── api/              # API 接口定义
│   ├── assets/           # 静态资源
│   ├── components/       # 公共组件
│   ├── layouts/          # 布局组件
│   ├── router/           # 路由配置
│   ├── stores/           # Pinia 状态管理
│   ├── utils/            # 工具函数
│   ├── views/            # 页面组件
│   ├── App.vue           # 根组件
│   └── main.ts           # 入口文件
├── public/               # 公共资源
├── index.html           # HTML 模板
├── vite.config.ts       # Vite 配置
└── package.json         # 项目配置
```

## 页面设计

### 1. 整体布局

```vue
<template>
  <el-container class="test-file-manager-layout">
    <el-header>
      <AppHeader />
    </el-header>
    <el-container>
      <el-aside width="250px">
        <SideMenu />
      </el-aside>
      <el-main>
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>
```

### 2. 文件管理主页面

#### 2.1 页面结构
```vue
<template>
  <div class="test-file-manager-main">
    <!-- 搜索栏 -->
    <VabQueryForm>
      <vab-query-form-left-panel :span="18">
        <el-form :inline="true" :model="queryForm">
          <el-form-item label="产品名称">
            <el-select v-model="queryForm.product" placeholder="请选择产品">
              <el-option 
                v-for="item in products" 
                :key="item.id"
                :label="item.product_name"
                :value="item.product_name"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="序列号">
            <el-input v-model="queryForm.sn" placeholder="请输入序列号" />
          </el-form-item>
          <el-form-item label="日期范围">
            <el-date-picker
              v-model="queryForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="6">
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button type="success" @click="handleReorganize">手动重排</el-button>
      </vab-query-form-right-panel>
    </VabQueryForm>

    <!-- 文件树和列表 -->
    <el-row :gutter="20" class="test-file-manager-content">
      <!-- 左侧树形目录 -->
      <el-col :span="6">
        <el-card class="test-file-manager-tree-card">
          <template #header>
            <span>目录结构</span>
          </template>
          <el-tree
            :data="treeData"
            :props="treeProps"
            :load="loadNode"
            lazy
            @node-click="handleNodeClick"
            highlight-current
            :expand-on-click-node="false"
          >
            <template #default="{ node, data }">
              <span class="test-file-manager-tree-node">
                <el-icon>
                  <Folder v-if="data.type === 'folder'" />
                  <Document v-else />
                </el-icon>
                <span>{{ node.label }}</span>
                <span class="test-file-manager-tree-count" v-if="data.children_count">
                  ({{ data.children_count }})
                </span>
              </span>
            </template>
          </el-tree>
        </el-card>
      </el-col>

      <!-- 右侧文件列表 -->
      <el-col :span="18">
        <el-card>
          <el-table 
            :data="fileList" 
            v-loading="loading"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="filename" label="文件名" min-width="200">
              <template #default="{ row }">
                <div class="test-file-manager-filename">
                  <el-icon><Document /></el-icon>
                  <span>{{ row.filename }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="test_type" label="测试类型" width="120" />
            <el-table-column prop="file_size" label="文件大小" width="100">
              <template #default="{ row }">
                {{ formatFileSize(row.file_size) }}
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="同步时间" width="160" />
            <el-table-column prop="sync_status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.sync_status)">
                  {{ getStatusText(row.sync_status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="handleDownload(row)"
                >
                  下载
                </el-button>
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="handleDelete(row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :page-sizes="[20, 50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Folder } from '@element-plus/icons-vue'
import { getFileList, getTreeData, deleteFile } from '@/api/files'
import { formatFileSize } from '@/utils/format'

const $baseMessage = inject('$baseMessage')
const $baseConfirm = inject('$baseConfirm')

// 查询表单
const queryForm = reactive({
  product: '',
  sn: '',
  dateRange: []
})

// 文件列表
const fileList = ref([])
const loading = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 树形数据
const treeData = ref([])
const treeProps = {
  label: 'label',
  children: 'children',
  isLeaf: 'isLeaf'
}

// 加载文件列表
const loadFileList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      ...queryForm
    }
    const { data } = await getFileList(params)
    fileList.value = data.list
    pagination.total = data.total
  } catch (error) {
    $baseMessage('加载文件列表失败', 'error')
  } finally {
    loading.value = false
  }
}

// 删除文件
const handleDelete = (row) => {
  $baseConfirm(
    `确认删除文件 ${row.filename}？`,
    '提示',
    async () => {
      try {
        await deleteFile(row.id)
        $baseMessage('删除成功', 'success')
        loadFileList()
      } catch (error) {
        $baseMessage('删除失败', 'error')
      }
    }
  )
}

onMounted(() => {
  loadFileList()
})
</script>

<style scoped lang="scss">
.test-file-manager {
  &-main {
    padding: 20px;
  }

  &-content {
    margin-top: 20px;
  }

  &-tree-card {
    height: calc(100vh - 240px);
    
    :deep(.el-card__body) {
      overflow-y: auto;
    }
  }

  &-tree-node {
    display: flex;
    align-items: center;
    font-size: 14px;
    
    .el-icon {
      margin-right: 4px;
    }
  }

  &-tree-count {
    margin-left: 4px;
    color: #909399;
    font-size: 12px;
  }

  &-filename {
    display: flex;
    align-items: center;
    
    .el-icon {
      margin-right: 4px;
      color: #409eff;
    }
  }
}
</style>
```

#### 2.2 统计面板组件
```vue
<template>
  <el-row :gutter="20" class="test-file-manager-stats">
    <el-col :span="6" v-for="item in stats" :key="item.title">
      <el-card>
        <div class="test-file-manager-stat-item">
          <div class="test-file-manager-stat-icon" :style="{ backgroundColor: item.color }">
            <el-icon :size="24">
              <component :is="item.icon" />
            </el-icon>
          </div>
          <div class="test-file-manager-stat-content">
            <div class="test-file-manager-stat-value">{{ item.value }}</div>
            <div class="test-file-manager-stat-title">{{ item.title }}</div>
          </div>
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Files, Folder, Timer, HardDisk } from '@element-plus/icons-vue'

const props = defineProps<{
  summary: {
    total_files: number
    total_size: number
    total_products: number
    total_sns: number
  }
}>()

const stats = computed(() => [
  {
    title: '文件总数',
    value: props.summary.total_files.toLocaleString(),
    icon: Files,
    color: '#409eff'
  },
  {
    title: '产品数量',
    value: props.summary.total_products,
    icon: Folder,
    color: '#67c23a'
  },
  {
    title: '设备数量',
    value: props.summary.total_sns,
    icon: Timer,
    color: '#e6a23c'
  },
  {
    title: '总大小',
    value: formatFileSize(props.summary.total_size),
    icon: HardDisk,
    color: '#f56c6c'
  }
])
</script>
```

### 3. 目录重排管理页面

```vue
<template>
  <div class="test-file-manager-reorganize">
    <el-card>
      <template #header>
        <div class="test-file-manager-reorganize-header">
          <span>目录重排任务</span>
          <el-button type="primary" @click="showCreateDialog = true">
            创建任务
          </el-button>
        </div>
      </template>

      <el-table :data="taskList" stripe>
        <el-table-column prop="id" label="任务ID" width="80" />
        <el-table-column prop="task_type" label="任务类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.task_type === 'manual' ? 'primary' : 'info'">
              {{ row.task_type === 'manual' ? '手动' : '定时' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="source_dir" label="源目录" min-width="200" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getTaskStatusType(row.status)">
              {{ getTaskStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="processed_files" label="处理进度" width="120">
          <template #default="{ row }">
            {{ row.processed_files }}/{{ row.total_files }}
          </template>
        </el-table-column>
        <el-table-column prop="started_at" label="开始时间" width="160" />
        <el-table-column prop="completed_at" label="完成时间" width="160" />
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button 
              type="text" 
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建任务对话框 -->
    <firefly-dialog
      v-model="showCreateDialog"
      title="创建重排任务"
      width="500px"
    >
      <el-form :model="taskForm" label-width="100px">
        <el-form-item label="源目录">
          <el-input v-model="taskForm.source_dir" />
        </el-form-item>
        <el-form-item label="目标目录">
          <el-input v-model="taskForm.target_dir" />
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="taskForm.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleCreateTask">确定</el-button>
      </template>
    </firefly-dialog>
  </div>
</template>
```

### 4. 统计分析页面

```vue
<template>
  <div class="test-file-manager-statistics">
    <!-- 统计卡片 -->
    <StatsSummary :summary="summary" />

    <!-- 图表区域 -->
    <el-row :gutter="20" class="test-file-manager-charts">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>文件数量趋势</span>
          </template>
          <div ref="fileCountChart" style="height: 300px"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>产品文件分布</span>
          </template>
          <div ref="productChart" style="height: 300px"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细统计表格 -->
    <el-card class="test-file-manager-stats-table">
      <template #header>
        <span>产品统计详情</span>
      </template>
      <el-table :data="productStats" stripe>
        <el-table-column prop="product" label="产品名称" />
        <el-table-column prop="file_count" label="文件数量" />
        <el-table-column prop="total_size" label="总大小">
          <template #default="{ row }">
            {{ formatFileSize(row.total_size) }}
          </template>
        </el-table-column>
        <el-table-column prop="sn_count" label="设备数量" />
        <el-table-column prop="last_update" label="最后更新" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'
import { getFileStatistics } from '@/api/statistics'

const fileCountChart = ref<HTMLDivElement>()
const productChart = ref<HTMLDivElement>()

const initCharts = () => {
  // 文件数量趋势图
  const countChart = echarts.init(fileCountChart.value)
  countChart.setOption({
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: [120, 200, 150, 80, 70, 110, 130],
      type: 'line',
      smooth: true
    }]
  })

  // 产品文件分布饼图
  const pieChart = echarts.init(productChart.value)
  pieChart.setOption({
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [{
      type: 'pie',
      radius: '50%',
      data: [
        { value: 335, name: 'AIO-3576-JD4' },
        { value: 310, name: 'AIO-3568-JD8' },
        { value: 234, name: 'ROC-3588S-PC' }
      ]
    }]
  })
}

onMounted(() => {
  initCharts()
})
</script>
```

## 组件规范

### 1. 命名规范
- 组件文件名：PascalCase (如 `FileTree.vue`)
- 组件名称：PascalCase (如 `FileTree`)
- 样式类名：BEM 命名，前缀 `test-file-manager-`

### 2. 样式规范
- 使用 SCSS 预处理器
- 组件样式使用 scoped
- 全局样式变量定义在 `@/styles/variables.scss`

### 3. API 调用规范
```typescript
// api/files.ts
import request from '@/utils/request'

export function getFileList(params: any) {
  return request({
    url: '/api/v1/files',
    method: 'get',
    params
  })
}

export function deleteFile(id: number) {
  return request({
    url: `/api/v1/files/${id}`,
    method: 'delete'
  })
}
```

### 4. 状态管理规范
```typescript
// stores/files.ts
import { defineStore } from 'pinia'

export const useFilesStore = defineStore('files', {
  state: () => ({
    fileList: [],
    selectedNode: null,
    loading: false
  }),
  
  actions: {
    async fetchFiles(params: any) {
      this.loading = true
      try {
        const { data } = await getFileList(params)
        this.fileList = data.list
      } finally {
        this.loading = false
      }
    }
  }
})
```

## 响应式设计

- 使用 Element Plus 的栅格系统
- 小屏设备下树形目录收起
- 表格支持横向滚动
- 移动端适配操作按钮

## 性能优化

1. **路由懒加载**
2. **组件按需加载**
3. **虚拟滚动**（大数据量表格）
4. **图片懒加载**
5. **请求缓存和防抖**

## 部署配置

```typescript
// vite.config.ts
export default defineConfig({
  base: '/test-file-manager/',
  build: {
    outDir: 'dist',
    rollupOptions: {
      output: {
        manualChunks: {
          'element-plus': ['element-plus'],
          'echarts': ['echarts']
        }
      }
    }
  }
})
```
