# 测试文件管理系统数据库设计

## 数据库概述

数据库名称：`test_file_manager`
字符集：`utf8mb4`
排序规则：`utf8mb4_unicode_ci`
存储引擎：`InnoDB`

## 表结构设计

### 1. 文件同步记录表 (bi_file_sync)

存储所有同步文件的元信息。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 主键ID |
| product | VARCHAR(64) | NOT NULL, INDEX | 产品名称 |
| sn | VARCHAR(64) | NOT NULL, INDEX | 序列号 |
| date_folder | VARCHAR(32) | NOT NULL, INDEX | 日期文件夹 |
| test_datetime | VARCHAR(32) | NOT NULL | 测试日期时间 |
| test_type | VARCHAR(32) | | 测试类型 (AgingTest/FactoryTest等) |
| filename | VARCHAR(255) | NOT NULL | 文件名 |
| file_size | BIGINT | | 文件大小(字节) |
| file_mtime | DATETIME | | 文件修改时间 |
| src_path | VARCHAR(512) | | 源文件路径 |
| dst_path | VARCHAR(512) | UNIQUE | 目标文件路径 |
| file_md5 | CHAR(32) | | 文件MD5值 |
| sync_status | TINYINT | DEFAULT 1 | 同步状态: 1-已同步, 2-已重排, 3-已删除 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

索引：
- UNIQUE KEY `uniq_dst_path` (`dst_path`)
- INDEX `idx_product_sn_date` (`product`, `sn`, `date_folder`)
- INDEX `idx_sync_status` (`sync_status`)
- INDEX `idx_created_at` (`created_at`)

### 2. 产品信息表 (bi_file_products)

存储产品的基本信息。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 主键ID |
| product_name | VARCHAR(64) | UNIQUE, NOT NULL | 产品名称 |
| product_code | VARCHAR(32) | UNIQUE | 产品代码 |
| description | TEXT | | 产品描述 |
| status | TINYINT | DEFAULT 1 | 状态: 1-启用, 0-禁用 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

### 3. 测试记录表 (bi_file_test_records)

存储每次测试的汇总信息。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 主键ID |
| product_id | INT | FOREIGN KEY | 产品ID |
| sn | VARCHAR(64) | NOT NULL, INDEX | 序列号 |
| cpuid | VARCHAR(64) | INDEX | CPU ID |
| test_date | DATE | NOT NULL, INDEX | 测试日期 |
| test_datetime | VARCHAR(32) | NOT NULL | 测试日期时间 |
| test_types | JSON | | 测试类型列表 |
| file_count | INT | DEFAULT 0 | 文件数量 |
| total_size | BIGINT | DEFAULT 0 | 总文件大小 |
| factory_test_result | VARCHAR(32) | | 厂测结果 (成功/失败) |
| aging_test_result | VARCHAR(32) | | 老化测试结果 |
| firmware_version | VARCHAR(255) | | 固件版本 |
| factory_test_version | VARCHAR(64) | | 厂测版本 |
| ddr_size | VARCHAR(32) | | DDR大小 |
| flash_size | VARCHAR(32) | | Flash大小 |
| remarks | TEXT | | 备注 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

索引：
- INDEX `idx_product_sn` (`product_id`, `sn`)
- INDEX `idx_cpuid` (`cpuid`)
- INDEX `idx_test_date` (`test_date`)
- UNIQUE KEY `uniq_test` (`product_id`, `sn`, `test_datetime`)

### 4. 老化测试详情表 (bi_file_aging_test_details)

存储老化测试的详细信息。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 主键ID |
| test_record_id | BIGINT | FOREIGN KEY | 测试记录ID |
| soc_model | VARCHAR(32) | | SOC型号 |
| cpu_cores | INT | | CPU核心数 |
| cpu_freq | VARCHAR(128) | | CPU频率信息 |
| gpu_freq | INT | | GPU频率 |
| npu_freq | INT | | NPU频率 |
| ddr_freq | INT | | DDR频率 |
| cpu_temp | INT | | CPU温度 |
| gpu_temp | INT | | GPU温度 |
| npu_load | VARCHAR(128) | | NPU负载信息 |
| total_memory | INT | | 总内存(MB) |
| available_memory | INT | | 可用内存(MB) |
| run_time | VARCHAR(32) | | 运行时间 |
| abnormal_restart_count | INT | | 异常重启次数 |
| aging_test_count | INT | | 老化测试次数 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

索引：
- INDEX `idx_test_record` (`test_record_id`)

### 5. 厂测项目详情表 (bi_file_factory_test_items)

存储厂测各项目的详细结果。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 主键ID |
| test_record_id | BIGINT | FOREIGN KEY | 测试记录ID |
| item_name | VARCHAR(64) | NOT NULL | 测试项目名称 |
| test_status | VARCHAR(32) | | 测试状态 |
| test_result | VARCHAR(32) | | 测试结果 (成功/失败) |
| test_details | TEXT | | 测试细节 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

索引：
- INDEX `idx_test_record` (`test_record_id`)
- INDEX `idx_item_result` (`item_name`, `test_result`)

### 6. 厂测汇总表 (bi_file_factory_test_summary)

存储厂测的汇总信息。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 主键ID |
| test_record_id | BIGINT | FOREIGN KEY, UNIQUE | 测试记录ID |
| device_name | VARCHAR(64) | | 设备名称 |
| cpuid | VARCHAR(64) | INDEX | CPU ID |
| factory_version | VARCHAR(64) | | 厂测版本 |
| firmware_version | VARCHAR(255) | | 固件版本 |
| ddr_size | VARCHAR(32) | | DDR大小 |
| flash_size | VARCHAR(32) | | Flash大小 |
| success_items | JSON | | 成功项目列表 |
| failed_items | JSON | | 失败项目列表 |
| success_count | INT | DEFAULT 0 | 成功项目数 |
| failed_count | INT | DEFAULT 0 | 失败项目数 |
| final_result | VARCHAR(32) | | 最终结果 (成功/失败) |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

索引：
- UNIQUE KEY `uniq_test_record` (`test_record_id`)
- INDEX `idx_cpuid` (`cpuid`)
- INDEX `idx_final_result` (`final_result`)

### 7. 目录重排任务表 (bi_file_reorganize_tasks)

记录目录重排任务的执行情况。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 主键ID |
| task_type | VARCHAR(32) | NOT NULL | 任务类型: manual/scheduled |
| source_dir | VARCHAR(512) | | 源目录 |
| target_dir | VARCHAR(512) | | 目标目录 |
| status | TINYINT | DEFAULT 0 | 状态: 0-待处理, 1-处理中, 2-完成, 3-失败 |
| total_files | INT | DEFAULT 0 | 总文件数 |
| processed_files | INT | DEFAULT 0 | 已处理文件数 |
| error_message | TEXT | | 错误信息 |
| started_at | DATETIME | | 开始时间 |
| completed_at | DATETIME | | 完成时间 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

### 8. 操作日志表 (bi_file_operation_logs)

记录系统的重要操作日志。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 主键ID |
| user_id | INT | | 用户ID |
| operation_type | VARCHAR(32) | NOT NULL | 操作类型 |
| operation_desc | VARCHAR(255) | | 操作描述 |
| target_type | VARCHAR(32) | | 目标类型 |
| target_id | VARCHAR(64) | | 目标ID |
| request_data | JSON | | 请求数据 |
| response_data | JSON | | 响应数据 |
| ip_address | VARCHAR(45) | | IP地址 |
| user_agent | VARCHAR(255) | | 用户代理 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

索引：
- INDEX `idx_user_operation` (`user_id`, `operation_type`)
- INDEX `idx_created_at` (`created_at`)

## 数据关系图

```
bi_file_products (1) ─────┐
                          │
                          ▼ (n)
              bi_file_test_records ─────────────┬─────────────────────┐
                          │                     │                     │
                          │ (1)                 │ (1)                 │ (1)
                          ▼ (n)                 ▼ (1)                 ▼ (n)
                 bi_file_sync      bi_file_aging_test_details   bi_file_factory_test_items
                                                │                     │
                                                │                     │
                                                └─────────────────────┘
                                                         │
                                                         ▼ (1)
                                              bi_file_factory_test_summary
                  
bi_file_reorganize_tasks (独立表)
bi_file_operation_logs (独立表)
```

## 性能优化建议

1. **索引优化**
   - 为常用查询字段建立复合索引
   - 定期分析查询日志，优化慢查询

2. **分区策略**
   - bi_file_sync 表按 created_at 月份分区
   - bi_file_operation_logs 表按 created_at 月份分区

3. **数据归档**
   - 超过6个月的日志数据归档到历史表
   - 定期清理已删除的文件记录

4. **查询优化**
   - 使用分页查询限制返回数据量
   - 大数据量统计使用汇总表缓存
