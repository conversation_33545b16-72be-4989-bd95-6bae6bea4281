# 测试文件管理系统数据库更新说明

## 更新概述

根据实际的测试文件内容，对数据库设计进行了以下更新：

## 新增表结构

### 1. 老化测试详情表 (aging_test_details)
用于存储老化测试的详细信息，包括：
- SOC型号、CPU核心数、各组件频率
- CPU/GPU温度、NPU负载
- 内存使用情况、运行时间
- 异常重启次数、老化测试次数

### 2. 厂测项目详情表 (factory_test_items)
记录每个厂测项目的详细测试结果：
- 测试项目名称（如 MEMORY、SERIAL_PORT、USB 等）
- 测试状态和结果
- 测试细节信息

### 3. 厂测汇总表 (factory_test_summary)
存储厂测的整体信息：
- 设备信息（设备名、CPUID、版本信息）
- 硬件配置（DDR、Flash大小）
- 成功/失败项目列表和统计
- 最终测试结果

## 更新的表结构

### 测试记录表 (test_records)
新增字段：
- cpuid: CPU ID
- factory_test_result: 厂测结果
- aging_test_result: 老化测试结果
- firmware_version: 固件版本
- factory_test_version: 厂测版本
- ddr_size: DDR大小
- flash_size: Flash大小

## 文件解析功能

在目录重排服务中新增了测试文件解析功能：

### 1. 老化测试结果解析 (agingTest_result.txt)
```
[老化信息]
SOC:rk3576,核心数:8
CPU0:1800,CPU4:0,CPU6:0 
GPU:300,NPU:950,DDR:2112
CPU温度: *64 °C*    / GPU温度: *65 °C* 
NPU负载:  Core0:  4%, Core1:  0%,
运行内存:3897 MB,可用内存：1372 MB
运行时间:87:33:56
异常重启次数:13
老化测试次数:24
```

### 2. 厂测详情解析 (factoryTest_details.txt)
解析每个测试项目的详细信息，包括：
- 项目名称
- 测试状态
- 测试结果（成功/失败）
- 测试细节

### 3. 厂测结果解析 (factoryTest_result.txt)
提取厂测汇总信息：
- 设备信息
- 成功/失败项目统计
- 最终测试结果

### 4. CPU ID 解析 (cpuid.txt)
直接读取并保存 CPU ID 信息

## 数据关系

```
test_records (测试记录)
    ├── aging_test_details (老化测试详情) - 1:1关系
    ├── factory_test_items (厂测项目详情) - 1:n关系
    └── factory_test_summary (厂测汇总) - 1:1关系
```

## 使用说明

1. **数据库初始化**
   ```bash
   mysql -u root -p < /home/<USER>/Project/tchipbi/ai_summary/test_file_summary/database/init.sql
   ```

2. **文件处理流程**
   - Lsyncd 同步文件到临时目录
   - 后端服务扫描并重排文件
   - 自动解析测试文件内容
   - 将解析结果保存到对应数据表

3. **查询示例**
   ```sql
   -- 查询某个设备的测试结果
   SELECT tr.*, ats.final_result, atd.cpu_temp, atd.gpu_temp
   FROM test_records tr
   LEFT JOIN factory_test_summary ats ON tr.id = ats.test_record_id
   LEFT JOIN aging_test_details atd ON tr.id = atd.test_record_id
   WHERE tr.sn = 'demo1111';
   
   -- 查询失败的测试项目
   SELECT fti.*, tr.sn, tr.test_datetime
   FROM factory_test_items fti
   JOIN test_records tr ON fti.test_record_id = tr.id
   WHERE fti.test_result = '失败';
   ```

## 注意事项

1. 文件解析使用正则表达式，需要确保测试文件格式一致
2. 数据库使用 UTF8MB4 字符集，支持中文存储
3. 建议定期清理历史数据，保持数据库性能
4. 重要数据建议定期备份
