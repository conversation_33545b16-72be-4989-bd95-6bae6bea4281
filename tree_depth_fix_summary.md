# 树形数据加载深度限制问题修复总结

## 问题描述
在触发筛选后，左侧目录树的数据刷新时传入了 `depth` 参数，导致树形数据返回不完整。虽然后端实际上没有使用这个参数，但为了保持代码的清晰性和避免未来可能的问题，需要移除这个不必要的参数。

## 问题原因
前端在调用 `getTreeData` API 时，统一传入了 `depth: 1` 参数，这个参数原本可能是用来限制返回的树形层级深度的，但在当前的实现中：
1. 后端服务层虽然接收了这个参数，但实际没有使用它
2. 树形组件采用了懒加载模式，每次只加载当前节点的直接子节点
3. 传入固定的 `depth: 1` 可能会给维护者造成误解

## 解决方案

### 前端修改 (FileManagerView.vue)
移除了所有 `getTreeData` 调用中的 `depth` 参数：

1. **initTreeData 方法**
```javascript
// 修改前
const params = {
  node: '',
  depth: 1,
  for_tree: true,
}

// 修改后
const params = {
  node: '',
  for_tree: true,
}
```

2. **handleLoadNode 方法**
```javascript
// 修改前
const params = {
  node: treeNode.dataRef.id,
  depth: 1,
  for_tree: true,
}

// 修改后
const params = {
  node: treeNode.dataRef.id,
  for_tree: true,
}
```

3. **loadEntryList 方法中的表格数据加载**
同样移除了所有表格数据加载时的 `depth` 参数。

## 实现效果

1. **代码清晰度提升**：移除了未使用的参数，减少了代码的复杂性
2. **避免误解**：不会让维护者误以为树形数据的加载深度被限制为1层
3. **功能正常**：树形组件的懒加载功能继续正常工作，筛选功能也能正确应用

## 技术说明

### 树形组件的懒加载机制
1. 初始加载时只获取根节点（产品列表）
2. 用户点击展开某个节点时，触发 `handleLoadNode` 方法
3. 根据节点类型（product/sn/date）和筛选条件加载对应的子节点
4. 加载完成后更新节点的 `children` 属性

### 筛选条件的应用
1. 基础筛选（产品、SN）通过 `buildAllFilters()` 统一构建
2. 节点特定筛选（如日期范围）根据节点类型动态添加
3. 筛选条件在每次数据加载时都会被正确应用

## 注意事项

1. **后端兼容性**：虽然移除了前端的 `depth` 参数，但后端仍然接收这个参数（只是不使用），保证了向后兼容
2. **性能考虑**：懒加载机制确保了大数据量时的性能，每次只加载用户需要看到的数据
3. **筛选联动**：确保筛选条件变化后，已展开的节点会重新加载以反映新的筛选结果

## 后续建议

1. 考虑在后端完全移除 `depth` 参数的定义，使接口更加简洁
2. 如果未来需要支持一次性加载多层数据，可以考虑添加新的参数如 `load_children` 或 `recursive`
3. 优化树形数据的缓存机制，避免重复加载相同的节点数据
