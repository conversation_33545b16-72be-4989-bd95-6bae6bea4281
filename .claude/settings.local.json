{"permissions": {"allow": ["Bash(grep:*)", "Bash(rg:*)", "Bash(find:*)", "Bash(php:*)", "<PERSON><PERSON>(chmod:*)", "Bash(bash:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(true)", "Bash(rm:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(composer check-platform-reqs:*)", "Bash(for file in app/Core/Services/Points/LevelService.php app/Core/Services/Points/PointEventService.php app/Core/Services/Points/PointShopService.php app/Model/Points/*.php migrations/*.php)", "Bash(do)", "<PERSON><PERSON>(echo:*)", "Bash(done)", "<PERSON><PERSON>(sed:*)", "Bash(for file in app/Model/Points/*.php)", "Bash(npm run lint)", "Bash(ls:*)", "Bash(npm install:*)", "WebFetch(domain:github.com)"], "deny": []}}