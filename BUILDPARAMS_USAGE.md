# BuildParams 使用风格指南

## 核心概念

`buildparams` 是 `BusinessService` 基类提供的标准方法，用于构建数据库查询条件，简化参数处理流程。

## 基本用法

### Service层继承 BusinessService

```php
use App\Core\Services\BusinessService;

class FileManagerService extends BusinessService
{
    // ...
}
```

### 在方法中使用 buildparams

```php
public function getTreeData(
    string $node = '',
    int $depth = 1,
    array $filter = [],
    array $op = [],
    string $sort = '',
    string $order = 'ASC',
    int $limit = 50,
    int $page = 1
): array {
    // 1. 先构建基础查询
    $query = Db::table('table_name')
        ->where('status', '!=', 3)
        ->groupBy('field');
    
    // 2. 应用其他条件（如搜索）
    if (!empty($search)) {
        $query->where('name', 'LIKE', "%{$search}%");
    }
    
    // 3. 使用 buildparams 处理 filter、op、sort、order
    $sort = !empty($sort) ? $sort : 'default_field';
    [$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit, $query);
    
    // 4. 应用排序和分页
    $total = $query->count();
    $list = $query->orderBy($sort, $order)
                  ->forPage($page, $limit)
                  ->get();
    
    return compact('list', 'total');
}
```

## buildparams 方法签名

```php
protected function buildparams(
    array $filter,     // 过滤条件
    array $op,         // 操作符
    string $sort,      // 排序字段
    string $order,     // 排序方式
    int $limit,        // 限制数量
    $query = null      // 查询对象（可选）
): array
```

### 返回值
```php
[$query, $limit, $sort, $order]
```

## 使用步骤

1. **构建基础查询** - 创建初始查询对象，添加固定条件
2. **应用特殊条件** - 如搜索、日期范围等不在 filter 中的条件
3. **调用 buildparams** - 传入参数和查询对象
4. **应用排序分页** - 使用 `paginate` 方法进行分页

## 实际示例

### 使用 paginate 方法（推荐）

```php
// 获取产品列表
$query = Db::table('file_sync')
    ->select('product')
    ->where('sync_status', '!=', 3)
    ->groupBy('product');

// 应用搜索
if (!empty($search)) {
    $query->where('product', 'LIKE', "%{$search}%");
}

// 使用 buildparams
$sort = !empty($sort) ? $sort : 'product';
[$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit, $query);

// 分页处理 - 使用 paginate
if ($forTree) {
    // 树形组件不需要分页
    $products = $query->orderBy($sort, $order)->get();
    $total = count($products);
} else {
    // 使用 paginate 方法
    $result = $query->orderBy($sort, $order)->paginate($limit, ['*'], 'page', $page);
    $products = $result->items();
    $total = $result->total();
}
```

### Model 直接使用 paginate

```php
public function getFileList(array $params): array
{
    $page = (int) ($params['page'] ?? 1);
    $pageSize = (int) ($params['page_size'] ?? 20);
    
    $query = FileSyncModel::query()
        ->where('sync_status', '!=', 3);
    
    // 添加筛选条件...
    
    // 直接使用 paginate
    $result = $query->orderBy('id', 'desc')->paginate($pageSize, ['*'], 'page', $page);
    
    return [
        'list' => $result->items(),
        'total' => $result->total(),
        'page' => $result->currentPage(),
        'page_size' => $result->perPage(),
        'total_page' => $result->lastPage()
    ];
}
```

## 优势

1. **代码简洁** - 一行代码处理所有过滤、操作符逻辑
2. **标准化** - 统一的查询构建方式
3. **可维护** - 过滤逻辑集中在 BusinessService 基类
4. **灵活性** - 支持复杂的查询操作符
5. **便捷分页** - 使用 `paginate` 方法自动处理分页逻辑

## 注意事项

- 必须继承 `BusinessService` 类
- `$query` 参数传入前应完成基础条件设置
- 默认排序字段应在调用前设置
- 返回的 `$query` 已应用过滤条件但未排序和分页
- 推荐使用 `paginate` 方法进行分页，它会自动处理总数、当前页等信息

## Paginate 方法说明

```php
$result = $query->paginate($perPage, $columns, $pageName, $page);
```

### 参数
- `$perPage` - 每页显示数量
- `$columns` - 要查询的列，默认 `['*']`
- `$pageName` - 页码参数名，默认 `'page'`
- `$page` - 当前页码

### 返回值方法
- `items()` - 获取当前页数据
- `total()` - 获取总数
- `currentPage()` - 获取当前页
- `perPage()` - 获取每页数量
- `lastPage()` - 获取总页数
