# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

数字天启（TChip BI）是一个完整的企业级商业智能和管理平台，由前端（Vue 3）和后端（Hyperf PHP）组成。

### 项目结构
```
tchipbi/
├── tchip_bi_backend/    # 后端服务 (Hyperf 2.2 + PHP 7.4+)
└── tchip_bi_frontend/   # 前端应用 (Vue 3 + Element Plus)
```

## 常用开发命令

### 后端 (tchip_bi_backend)

#### 开发环境启动
```bash
cd tchip_bi_backend
php bin/hyperf.php server:watch  # 热重载开发模式
# 或
php watch                        # 自定义热重载脚本
```

#### 生产环境启动
```bash
bash start.sh                    # 自动检测环境并启动
# 或
php bin/hyperf.php start         # 直接启动
```

#### 数据库操作
```bash
php bin/hyperf.php migrate                    # 运行数据库迁移
php bin/hyperf.php gen:migration table_name   # 创建迁移文件
```

#### 代码生成
```bash
# 生成模型（支持多数据库）
php bin/hyperf.php gen:model user_department --path=app/Model/Redmine --pool=tchip_redmine

# 查看所有路由
php bin/hyperf.php describe:routes
```

#### 代码质量检查
```bash
composer test        # 运行 PHPUnit 测试
composer analyse     # PHPStan 静态分析
composer cs-fix      # PHP CS Fixer 格式化
```

### 前端 (tchip_bi_frontend)

#### 开发环境
```bash
cd tchip_bi_frontend
npm i --registry=http://mirrors.cloud.tencent.com/npm/
npm i
npm run serve        # 启动开发服务器
```

#### 构建部署
```bash
npm run build:dev   # 开发环境构建
npm run build:pre   # 预发布环境构建
npm run build       # 生产环境构建
npm run push        # 推送到服务器
```

#### 代码质量检查
```bash
npm run lint              # ESLint 检查
npm run lint:prettier     # Prettier 格式化
npm run test:unit         # Jest 单元测试
```

#### 代码生成
```bash
npm run template     # 使用 Plop 生成代码模板
```

## 项目架构

### 后端架构 (Hyperf)
- **基础框架**: Hyperf 2.2 (高性能 PHP 协程框架)
- **数据库**: MySQL (支持多数据库连接)
- **缓存**: Redis (模型缓存和数据缓存)
- **队列**: 异步队列支持
- **认证**: JWT + hyperf-auth
- **文档**: 支持 Markdown、PDF 生成

#### 核心目录结构
```
app/
├── Controller/    # 控制器层 (MVC)
├── Core/         # 核心业务逻辑
│   └── Services/ # 服务层
├── Model/        # 数据模型层
├── Constants/    # 常量定义
├── Middleware/   # 中间件
├── Request/      # 请求验证
├── Command/      # 命令行工具
└── Job/         # 队列任务
```

#### 多数据库支持
```
- default (主数据库)
- tchip_redmine (Redmine 系统)
- tchip_sale (销售系统)
- tchip_oa (OA 系统)
- tchip_bbs (BBS 系统)
```

### 前端架构 (Vue 3)
- **框架**: Vue 3 + Composition API
- **构建工具**: Vue CLI 5 + Webpack 5
- **UI 框架**: Element Plus + Ant Design Vue + Arco Design
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **类型检查**: TypeScript
- **图表**: ECharts
- **编辑器**: Wangeditor + Vditor

#### 核心目录结构
```
src/
├── views/        # 页面视图
├── components/   # 公共组件
├── api/         # API 接口
├── store/       # 状态管理 (Pinia)
├── router/      # 路由配置
├── utils/       # 工具函数
└── assets/      # 静态资源
```

#### 主要功能模块
- 项目管理 (Project): 项目、事项、工作流、Wiki
- 产品管理 (Product): 产品信息、进度跟踪、变更记录
- 生产管理 (Production): 生产订单、装配订单、质检管理
- 办公管理 (OA): 资产管理、借用管理、报表系统
- 营销管理 (Marketing): 品牌管理、平台管理、推广报告
- 系统管理 (System): 用户、角色、权限、菜单

## 开发环境配置

### 后端环境要求
- PHP >= 7.4
- Swoole >= 4.5
- MySQL 5.7+
- Redis 5.0+
- Composer

### 前端环境要求
- Node.js >= 16
- npm >= 8

### 数据库配置
后端支持多个数据库连接，配置在 `config/autoload/databases.php` 中：
- 主数据库: default
- Redmine 系统: tchip_redmine
- 销售系统: tchip_sale
- OA 系统: tchip_oa

### 环境变量
前端支持多环境配置：
- 本地开发: `.env.local`
- 开发环境: `.env.development`
- 预发布: `.env.pre`
- 生产环境: `.env.production`

## 部署环境

### 环境对应关系
| 环境 | 后端地址 | 前端地址 | 数据库 |
|------|----------|----------|--------|
| dev | http://************:8057 | http://************:2101 | 253 |
| pre | http://************:9057 | http://************:3101 | 生产 |
| master | http://bi.t-firefly.com:8057 | http://bi.t-firefly.com:2101 | 生产 |

### Git Flow 工作流
- **master**: 生产分支，稳定版本
- **pre**: 预发布分支，最终测试
- **dev**: 开发分支，最新开发功能
- **feature-\***: 功能开发分支
- **hotfix-\***: 紧急修复分支

## 特殊说明

### 后端特殊功能
- 企业微信集成: 支持消息推送、审批流程、通讯录同步
- Redmine 集成: 项目同步、问题跟踪
- 工作流引擎: 支持自定义审批流程
- 队列系统: 异步任务处理
- 多租户支持: 支持多系统集成

### 前端特殊功能
- 多 UI 框架共存: Element Plus、Ant Design Vue、Arco Design
- 富文本编辑器: Wangeditor + Markdown 支持
- 图表系统: ECharts 集成
- 权限管理: 基于角色的访问控制 (RBAC)
- 多种布局: 支持垂直、水平、综合等多种布局模式

### 代码规范
- 后端: 使用 PHP CS Fixer 进行代码格式化
- 前端: 使用 ESLint + Prettier 进行代码检查和格式化
- 两端都配置了 PHPStan/TypeScript 进行静态类型检查

### 测试
- 后端: PHPUnit 单元测试，测试文件位于 `test/Cases/`
- 前端: Jest 单元测试，测试文件位于 `tests/` 目录

### 热重载
- 后端: 使用 `php watch` 或 `php bin/hyperf.php server:watch` 实现热重载
- 前端: Vue CLI 内置热重载支持

## 常见问题

### 后端问题
1. 注解问题: 使用 `@Inject` 而不是 `@Inject()`
2. 数据库连接: 确保正确配置多数据库连接池
3. 队列任务: 检查 Redis 连接和队列消费者进程

### 前端问题
1. Element Plus 版本: 使用 2.5.0，避免 2.3.0 版本的 Cascader 问题
2. 路由缓存: 最大缓存 20 个路由页面
3. 构建优化: 生产环境自动开启 Gzip 压缩和代码分割

### 开发建议
1. 后端开发时优先查看 `app/Constants/` 目录了解业务常量
2. 前端开发时参考 `src/components/` 中的业务组件
3. API 开发时遵循 RESTful 规范
4. 数据库操作优先使用 Hyperf 的模型缓存
5. 前端状态管理使用 Pinia 而非 Vuex