#!/bin/bash

# Git本周提交汇报生成器
# 作者：qinsx
# 功能：生成本周（周一到周日）多项目Git提交汇报，输出Markdown格式

# =================================================================
# 项目配置区域 - 可以添加多个项目
# 格式：PROJECT_NAMES[索引]="项目名称" PROJECT_PATHS[索引]="项目路径"
# =================================================================

# 项目名称数组
PROJECT_NAMES[0]="数字天启前端"
PROJECT_NAMES[1]="数字天启后端"
PROJECT_NAMES[2]="其他项目示例"

# 项目路径数组（对应上面的项目名称）
PROJECT_PATHS[0]="/home/<USER>/Project/tchipbi/tchip_bi_frontend"
PROJECT_PATHS[1]="/home/<USER>/Project/tchipbi/tchip_bi_backend"
PROJECT_PATHS[2]="/path/to/other/project"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 计算本周时间范围（周一到周日）
calculate_this_week() {
    # 获取当前日期
    local current_date=$(date +%Y-%m-%d)
    local current_day_of_week=$(date +%u)  # 1-7，1为周一，7为周日
    
    # 计算本周一的日期
    local days_to_monday=$((current_day_of_week - 1))
    WEEK_START=$(date -d "$current_date -${days_to_monday} days" +%Y-%m-%d)
    
    # 计算本周日的日期
    local days_to_sunday=$((7 - current_day_of_week))
    WEEK_END=$(date -d "$current_date +${days_to_sunday} days" +%Y-%m-%d)
    
    echo -e "${GREEN}本周时间范围: $WEEK_START 到 $WEEK_END${NC}"
}

# 检查是否在Git仓库中
check_git_repo() {
    local repo_path="$1"
    if [ ! -d "$repo_path" ]; then
        echo -e "${RED}错误: 路径不存在: $repo_path${NC}"
        return 1
    fi
    
    cd "$repo_path"
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        echo -e "${RED}错误: $repo_path 不是Git仓库！${NC}"
        return 1
    fi
    return 0
}

# 打印分割线
print_separator() {
    echo -e "${CYAN}================================================================${NC}"
}

# 打印标题
print_title() {
    local title="$1"
    echo -e "${YELLOW}📊 $title${NC}"
    echo -e "${CYAN}────────────────────────────────────────────────────────────────${NC}"
}

# 获取用户输入
get_user_input() {
    echo -e "${BLUE}请输入要统计的用户名（留空统计当前用户）:${NC}"
    read -p "> " author_name
    
    if [ -z "$author_name" ]; then
        # 尝试从任意一个有效的项目路径获取用户名
        for i in "${!PROJECT_PATHS[@]}"; do
            if [ -d "${PROJECT_PATHS[$i]}" ]; then
                cd "${PROJECT_PATHS[$i]}"
                if git rev-parse --git-dir > /dev/null 2>&1; then
                    author_name=$(git config user.name)
                    break
                fi
            fi
        done
        
        if [ -z "$author_name" ]; then
            author_name="unknown"
        fi
        echo -e "${GREEN}使用Git用户: $author_name${NC}"
    fi
}

# 获取项目本周提交记录（搜索所有分支）
get_project_commits() {
    local project_name="$1"
    local project_path="$2"

    if ! check_git_repo "$project_path" 2>/dev/null; then
        echo "路径错误"
        return 1
    fi

    cd "$project_path"

    # 搜索所有分支中的提交（本周时间范围内）
    local commits=$(git log --all --author="$author_name" \
        --since="$WEEK_START 00:00:00" --until="$WEEK_END 23:59:59" \
        --no-merges --oneline --pretty=format:"%h %s" 2>/dev/null)

    if [ -z "$commits" ]; then
        echo "无提交记录"
        return 0
    fi

    echo "$commits"
}

# 生成Markdown格式的汇报
generate_markdown_report() {
    local report_file="weekly-report-$(date +%Y%m%d).md"
    local current_week=$(date -d "$WEEK_START" +%Y年第%V周)
    
    {
        echo "# 本周工作汇报 ($current_week)"
        echo ""
        echo "**汇报时间**: $(date '+%Y-%m-%d %H:%M:%S')"
        echo "**汇报人**: $author_name"
        echo "**汇报周期**: $WEEK_START 至 $WEEK_END"
        echo ""
        
        # 遍历所有项目
        for i in "${!PROJECT_NAMES[@]}"; do
            local project_name="${PROJECT_NAMES[$i]}"
            local project_path="${PROJECT_PATHS[$i]}"
            
            echo "## $project_name"
            echo ""
            
            # 获取该项目的提交记录
            local commits=$(get_project_commits "$project_name" "$project_path")

            if [ "$commits" = "无提交记录" ]; then
                echo "本周无提交记录"
                echo ""
            elif [ "$commits" = "路径错误" ]; then
                echo "错误: 路径不存在或不是Git仓库"
                echo ""
            else
                # 计算提交数量
                local commit_count=$(echo "$commits" | wc -l)
                echo "**本周提交数量**: $commit_count"
                echo ""
                echo "**提交记录**:"
                echo ""
                # 处理提交记录，格式化为Markdown列表
                echo "$commits" | while IFS= read -r commit; do
                    if [ ! -z "$commit" ]; then
                        # 提取提交信息（去掉hash）
                        local commit_msg=$(echo "$commit" | cut -d' ' -f2-)
                        echo "- $commit_msg"
                    fi
                done
                echo ""
            fi
        done
        
        echo "---"
        echo "*报告由git-contribution-report.sh自动生成*"
        
    } > "$report_file"
    
    echo -e "${GREEN}Markdown汇报已生成: $report_file${NC}"
    echo "$report_file"
}

# 显示项目汇总信息
show_project_summary() {
    echo -e "${CYAN}================================================================${NC}"
    echo -e "${PURPLE}📊 本周提交汇总${NC}"
    echo -e "${CYAN}================================================================${NC}"
    echo ""
    
    local total_commits=0
    
    # 遍历所有项目
    for i in "${!PROJECT_NAMES[@]}"; do
        local project_name="${PROJECT_NAMES[$i]}"
        local project_path="${PROJECT_PATHS[$i]}"
        
        echo -e "${YELLOW}$project_name:${NC}"
        
        # 获取该项目的提交记录
        local commits=$(get_project_commits "$project_name" "$project_path")

        if [ "$commits" = "无提交记录" ]; then
            echo -e "  ${RED}本周无提交记录${NC}"
        elif [ "$commits" = "路径错误" ]; then
            echo -e "  ${RED}错误: 路径不存在或不是Git仓库${NC}"
        else
            local commit_count=$(echo "$commits" | wc -l)
            total_commits=$((total_commits + commit_count))
            echo -e "  ${GREEN}本周提交数量: $commit_count${NC}"
            echo -e "  ${BLUE}最新提交: $(echo "$commits" | head -1 | cut -c10-)${NC}"
        fi
        echo ""
    done
    
    echo -e "${GREEN}本周总提交数量: $total_commits${NC}"
    echo ""
}

# 主函数
main() {
    clear
    echo -e "${CYAN}================================================================${NC}"
    echo -e "${PURPLE}🚀 Git本周提交汇报生成器${NC}"
    echo -e "${PURPLE}作者: qinsx${NC}"
    echo -e "${CYAN}================================================================${NC}"
    echo ""
    
    # 计算本周时间范围
    calculate_this_week
    echo ""
    
    # 获取用户输入
    get_user_input
    echo ""
    
    # 显示项目汇总信息
    show_project_summary
    
    # 询问是否生成Markdown汇报
    echo -e "${BLUE}是否生成Markdown格式的本周汇报？(y/n):${NC}"
    read -p "> " generate_md
    if [[ $generate_md =~ ^[Yy]$ ]]; then
        generate_markdown_report
        echo ""
    fi
    
    echo -e "${GREEN}感谢使用Git本周汇报工具！${NC}"
}

# 脚本入口
main "$@" 