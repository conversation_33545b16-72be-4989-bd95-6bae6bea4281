# 参数传递风格规则

## 概述
本文档定义了项目中Controller与Service层之间参数传递的标准规范，确保代码的一致性和可维护性。

## 核心原则

### 1. Controller层参数获取
Controller层统一使用 `BaseController::getParams()` 方法获取请求参数：

```php
list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
```

### 2. Service层方法签名
Service层方法应该使用独立的参数而非数组，每个参数都有明确的类型和默认值：

```php
public function getList(
    array $filter = [],
    array $op = [],
    string $sort = 'id',
    string $order = 'DESC',
    int $limit = 10,
    string $search = '',
    array $searchFields = []
): array
```

## 参数说明

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `$filter` | array | 过滤条件数组，键为字段名，值为过滤值 | `['status' => 1, 'type' => 'product']` |
| `$op` | array | 操作符数组，定义每个过滤字段的比较操作 | `['status' => '=', 'name' => 'LIKE']` |
| `$sort` | string | 排序字段 | `'created_at'` |
| `$order` | string | 排序方向，只能是 'ASC' 或 'DESC' | `'DESC'` |
| `$limit` | int | 每页记录数 | `20` |
| `$search` | string/array | 搜索关键词 | `'keyword'` 或 `['keyword1', 'keyword2']` |
| `$searchFields` | array | 指定搜索的字段列表 | `['name', 'description']` |

## 支持的操作符

| 操作符 | 说明 | 示例 |
|--------|------|------|
| `=` | 等于 | `['status' => 1]` with `['status' => '=']` |
| `!=` | 不等于 | `['status' => 1]` with `['status' => '!=']` |
| `>` | 大于 | `['price' => 100]` with `['price' => '>']` |
| `>=` | 大于等于 | `['price' => 100]` with `['price' => '>=']` |
| `<` | 小于 | `['price' => 100]` with `['price' => '<']` |
| `<=` | 小于等于 | `['price' => 100]` with `['price' => '<=']` |
| `LIKE` | 模糊匹配 | `['name' => 'test']` with `['name' => 'LIKE']` |
| `NOT LIKE` | 不匹配 | `['name' => 'test']` with `['name' => 'NOT LIKE']` |
| `IN` | 包含在列表中 | `['status' => '1,2,3']` with `['status' => 'IN']` |
| `NOT IN` | 不包含在列表中 | `['status' => '1,2,3']` with `['status' => 'NOT IN']` |
| `BETWEEN` | 范围内 | `['price' => [100, 200]]` with `['price' => 'BETWEEN']` |
| `IS NULL` | 为空 | `['deleted_at' => '']` with `['deleted_at' => 'IS NULL']` |
| `IS NOT NULL` | 不为空 | `['deleted_at' => '']` with `['deleted_at' => 'IS NOT NULL']` |

## 实现示例

### Controller层实现

```php
class TestFileController extends BaseController
{
    /**
     * 获取树形目录
     */
    public function getTree()
    {
        // 获取标准参数
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        
        // 获取特定参数
        $node = $this->request->input('node', '');
        $depth = (int) $this->request->input('depth', 1);
        $page = (int) $this->request->input('page', 1);
        
        try {
            // 调用Service方法，传递独立参数
            $result = $this->fileService->getTreeData(
                $node,
                $depth,
                $filter,
                $op,
                $sort,
                $order,
                $limit,
                $page
            );
            return $this->response->success($result);
        } catch (\Exception $e) {
            return $this->response->error(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }
}
```

### Service层实现

```php
class FileManagerService
{
    /**
     * 获取树形数据
     */
    public function getTreeData(
        string $node = '',
        int $depth = 1,
        array $filter = [],
        array $op = [],
        string $sort = '',
        string $order = 'ASC',
        int $limit = 50,
        int $page = 1
    ): array {
        // 构建查询
        $query = $this->buildQuery($filter, $op);
        
        // 应用排序
        $sortField = !empty($sort) ? $sort : 'id';
        $query->orderBy($sortField, strtoupper($order));
        
        // 应用分页
        $total = $query->count();
        $list = $query->forPage($page, $limit)->get();
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $limit,
            'total_page' => ceil($total / $limit)
        ];
    }
    
    /**
     * 构建查询条件
     */
    protected function buildQuery(array $filter, array $op)
    {
        $query = DB::table('table_name');
        
        foreach ($filter as $field => $value) {
            $operator = $op[$field] ?? '=';
            $this->applyFilter($query, $field, $value, $operator);
        }
        
        return $query;
    }
}
```

## BaseController::getParams() 方法

```php
protected function getParams(): array
{
    $filter        = $this->request->input('filter');
    $op            = $this->request->input('op');
    $sort          = $this->request->input('sort', 'id');
    $order         = $this->request->input('order', 'DESC');
    $limit         = $this->request->input('limit', $this->request->input('pageSize', 10));
    $search        = $this->request->input('search');
    $search_fields = $this->request->input('search_fields');
    
    // 处理JSON格式的参数
    if (!is_array($filter) && !is_array($op)) {
        $filter = json_decode($filter, true);
        $op = json_decode($op, true);
    }
    
    return [
        (array)$filter,
        (array)$op,
        $sort,
        $order,
        $limit,
        (array)$search,
        (array)$search_fields
    ];
}
```

## 前端调用示例

```javascript
// Vue/JavaScript
const params = {
    filter: {
        product: 'AIO-3576',
        status: 1
    },
    op: {
        product: 'LIKE',
        status: '='
    },
    sort: 'created_at',
    order: 'DESC',
    limit: 20,
    page: 1,
    search: 'keyword'
}

// 发送请求
const { data } = await api.getTreeData(params)
```

## 优势

1. **类型安全**：每个参数都有明确的类型声明
2. **默认值支持**：所有参数都有合理的默认值
3. **可读性高**：方法签名清晰表达了所需参数
4. **易于测试**：独立的参数更容易进行单元测试
5. **统一规范**：整个项目使用一致的参数传递风格
6. **向后兼容**：通过默认值支持，新增参数不会破坏现有调用

## 注意事项

1. Service层方法参数顺序建议：
   - 必需的业务参数在前
   - 查询相关参数（filter, op）在中间
   - 分页排序参数在后
   
2. 参数默认值设置原则：
   - 排序默认使用主键或创建时间
   - 排序方向默认DESC（新数据优先）
   - 分页大小根据业务场景设置合理默认值

3. 错误处理：
   - Controller层负责捕获异常并返回统一格式的错误响应
   - Service层可以抛出业务异常，由Controller层处理

## 迁移指南

对于现有代码的迁移：

1. 将Controller继承从 `AbstractController` 改为 `BaseController`
2. 使用 `getParams()` 方法获取参数
3. 修改Service方法签名，将数组参数拆分为独立参数
4. 更新方法调用，传递独立参数而非数组
5. 测试确保功能正常
