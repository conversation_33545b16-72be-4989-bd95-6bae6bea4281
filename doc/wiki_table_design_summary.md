# Wiki模块数据库表设计汇总

## 概述
本文档汇总了TchipBI系统中Wiki模块相关的所有数据库表设计，包括核心业务表和Redmine导入相关表。

## 表结构分类

### 1. 核心业务表（TchipBi模块）

#### 1.1 空间管理
**wiki_spaces** - Wiki空间表
- 主键：`space_id` (int)
- 字段：
  - `space_name` (string) - 空间名称
  - `description` (string) - 空间描述
  - `theme_color` (string) - 主题颜色
  - `is_public` (int) - 是否公开
  - `doc_count` (int) - 文档数量
  - `created_by` (int) - 创建者ID
  - `created_at`, `updated_at`, `deleted_at` - 时间戳
- 关联：一对多文档，一对多成员，一对一创建者

**wiki_space_members** - Wiki空间成员表
- 主键：`member_id` (int)
- 字段：
  - `space_id` (int) - 所属空间ID
  - `associated_field` (string) - 关联字段
  - `user_id` (int) - 用户ID
  - `group_id` (int) - 用户组ID
  - `department_id` (int) - 部门ID
  - `system_role_id` (int) - 系统角色ID
  - `role` (string) - 角色
  - `permission_level` (int) - 权限级别
  - `created_at`, `updated_at`, `deleted_at` - 时间戳
- 关联：多对一空间，多对一用户，多对一用户组，多对一部门，多对一系统角色

#### 1.2 目录管理
**wiki_catalogs** - Wiki目录表
- 主键：`catalog_id` (int)
- 字段：
  - `space_id` (int) - 所属空间ID
  - `pid` (int) - 父级目录ID
  - `path` (string) - 目录路径
  - `name` (string) - 目录名称
  - `description` (string) - 目录描述
  - `is_public` (int) - 是否公开
  - `sort_order` (int) - 排序
  - `created_by` (int) - 创建者ID
  - `created_at`, `updated_at`, `deleted_at` - 时间戳
- 关联：多对一创建者，一对多文档

**wiki_catalog_members** - Wiki目录成员表
- 主键：`member_id` (int)
- 字段：
  - `associated_field` (string) - 关联字段
  - `catalog_id` (int) - 目录ID
  - `user_id` (int) - 用户ID
  - `group_id` (int) - 用户组ID
  - `department_id` (int) - 部门ID
  - `system_role_id` (int) - 系统角色ID
  - `role` (string) - 角色
  - `permission_level` (int) - 权限级别
  - `created_at`, `updated_at`, `deleted_at` - 时间戳
- 关联：多对一用户，多对一用户组

#### 1.3 文档管理
**wiki_documents** - Wiki文档表
- 主键：`doc_id` (int)
- 字段：
  - `space_id` (int) - 所属空间ID
  - `parent_id` (int) - 父文档ID
  - `catalog_id` (int) - 所属目录ID
  - `is_public` (int) - 是否公开
  - `path` (string) - 文档路径
  - `title` (string) - 文档标题
  - `content_html` (string) - HTML内容
  - `content_markdown` (string) - Markdown内容
  - `current_editor_type` (int) - 当前编辑器类型
  - `wiki_from_type` (int) - Wiki来源类型
  - `sort_order` (int) - 排序
  - `is_pinned` (int) - 是否置顶
  - `pinned_at` (datetime) - 置顶时间
  - `pinned_by` (int) - 置顶人
  - `pinned_reason` (string) - 置顶原因
  - `created_by` (int) - 创建者ID
  - `updated_by` (int) - 更新者ID
  - `content_updated_by` (int) - 内容更新者ID
  - `content_updated_at` (datetime) - 内容更新时间
  - `view_count` (int) - 查看次数
  - `like_count` (int) - 点赞次数
  - `created_at`, `updated_at`, `deleted_at` - 时间戳
- 关联：多对一空间，多对一目录，自关联父子文档，一对多版本，多对多标签，一对多统计，一对多评论，一对多点赞，一对多状态

**wiki_document_versions** - Wiki文档版本表
- 主键：`version_id` (int)
- 字段：
  - `doc_id` (int) - 文档ID
  - `version_number` (int) - 版本号
  - `content_html` (string) - HTML内容
  - `content_markdown` (string) - Markdown内容
  - `editor_type` (int) - 编辑器类型
  - `created_by` (int) - 创建者ID
  - `change_log` (string) - 变更日志
  - `created_at`, `updated_at`, `deleted_at` - 时间戳
- 关联：多对一文档，多对一创建者

#### 1.4 文档状态管理
**wiki_document_status** - Wiki文档状态表
- 主键：`id` (int)
- 字段：
  - `doc_id` (int) - 文档ID
  - `status_type` (string) - 状态类型
  - `status_value` (int) - 状态值（0=待审核，1=通过，2=拒绝）
  - `created_by` (int) - 操作人ID
  - `added_points` (int) - 是否已添加积分
  - `reason` (string) - 操作原因
  - `created_at` - 创建时间
- 关联：多对一文档，多对一状态类型，多对一操作人

**wiki_document_status_types** - Wiki文档状态类型表
- 主键：`status_type` (string)
- 字段：
  - `description` (string) - 业务含义描述
  - `is_active` (int) - 是否启用
  - `created_at`, `updated_at` - 时间戳
- 常量：
  - `premium` - 精华认证
  - `training` - 培训认证
  - `audit` - 审核状态

#### 1.5 文档关联
**wiki_document_tags** - Wiki文档标签关联表
- 主键：`id` (int)
- 字段：
  - `doc_id` (int) - 文档ID
  - `tag_id` (int) - 标签ID
  - `created_at`, `updated_at`, `deleted_at` - 时间戳
- 关联：多对一文档，多对一标签

**wiki_document_statistics** - Wiki文档统计表
- 主键：`stat_id` (int)
- 字段：
  - `doc_id` (int) - 文档ID
  - `date` (string) - 统计日期
  - `view_count` (int) - 查看次数
  - `like_count` (int) - 点赞次数
  - `comment_count` (int) - 评论次数
  - `created_at`, `updated_at` - 时间戳
- 关联：多对一文档

#### 1.6 互动功能
**wiki_comments** - Wiki评论表
- 主键：`comment_id` (int)
- 字段：
  - `doc_id` (int) - 文档ID
  - `user_id` (int) - 用户ID
  - `parent_id` (int) - 父评论ID
  - `content` (string) - 评论内容
  - `is_deleted` (int) - 是否删除
  - `created_at`, `updated_at`, `deleted_at` - 时间戳
- 关联：多对一文档，多对一用户，自关联父子评论，一对多回复

**wiki_replies** - Wiki回复表
- 主键：`reply_id` (int)
- 字段：
  - `comment_id` (int) - 评论ID
  - `user_id` (int) - 用户ID
  - `parent_id` (int) - 父回复ID
  - `content` (string) - 回复内容
  - `is_deleted` (int) - 是否删除
  - `created_at`, `updated_at`, `deleted_at` - 时间戳
- 关联：多对一评论，多对一用户，自关联父子回复

**wiki_likes** - Wiki点赞表
- 主键：`like_id` (int)
- 字段：
  - `doc_id` (int) - 文档ID
  - `user_id` (int) - 用户ID
  - `created_at`, `updated_at`, `deleted_at` - 时间戳
- 关联：多对一文档，多对一用户

#### 1.7 用户组管理
**wiki_user_groups** - Wiki用户组表
- 主键：`group_id` (int)
- 字段：
  - `group_name` (string) - 组名
  - `description` (string) - 组描述
  - `created_by` (int) - 创建者ID
  - `created_at`, `updated_at`, `deleted_at` - 时间戳
- 关联：一对多用户映射，多对一创建者

**wiki_user_group_mappings** - Wiki用户组映射表
- 主键：`mapping_id` (int)
- 字段：
  - `user_id` (int) - 用户ID
  - `group_id` (int) - 用户组ID
  - `role` (string) - 角色
  - `created_at`, `updated_at`, `deleted_at` - 时间戳
- 关联：多对一用户，多对一用户组

### 2. Redmine导入相关表

#### 2.1 Redmine Wiki核心表
**wikis** - Redmine Wiki表
- 主键：`id` (int)
- 字段：
  - `project_id` (int) - 项目ID
  - `start_page` (string) - 起始页面
  - `status` (int) - 状态
- 关联：一对多Wiki页面，一对多Wiki重定向

**wiki_pages** - Redmine Wiki页面表
- 主键：`id` (int)
- 字段：
  - `wiki_id` (int) - Wiki ID（即项目ID）
  - `title` (string) - 页面标题
  - `created_on` (datetime) - 创建时间
  - `protected` (int) - 是否保护
  - `parent_id` (int) - 父页面ID
  - `sort_order` (int) - 排序
- 关联：一对一Wiki内容，一对多附件，一对多内容版本

#### 2.2 Redmine Wiki内容表
**wiki_contents** - Redmine Wiki内容表
- 主键：`id` (int)
- 字段：
  - `page_id` (int) - 页面ID
  - `author_id` (int) - 作者ID
  - `text` (string) - 文本内容
  - `comments` (string) - 评论
  - `updated_on` (datetime) - 更新时间
  - `version` (int) - 版本号
  - `usage_type` (int) - 使用类型
  - `text_html` (string) - HTML文本
- 关联：一对多内容版本，多对一作者，多对一页面

**wiki_content_versions** - Redmine Wiki内容版本表
- 主键：`id` (int)
- 字段：
  - `wiki_content_id` (int) - Wiki内容ID
  - `page_id` (int) - 页面ID
  - `author_id` (int) - 作者ID
  - `data` (string) - 数据
  - `compression` (string) - 压缩方式
  - `comments` (string) - 评论
  - `updated_on` (datetime) - 更新时间
  - `version` (int) - 版本号
- 关联：多对一Wiki页面，多对一用户

#### 2.3 Redmine Wiki重定向表
**wiki_redirects** - Redmine Wiki重定向表
- 主键：`id` (int)
- 字段：
  - `wiki_id` (int) - Wiki ID
  - `title` (string) - 重定向标题
  - `redirects_to` (string) - 重定向目标
  - `created_on` (datetime) - 创建时间
  - `redirects_to_wiki_id` (int) - 重定向目标Wiki ID

## 表关系图谱

### 核心关系链
1. **空间层级**：`wiki_spaces` → `wiki_space_members` (权限管理)
2. **目录层级**：`wiki_catalogs` → `wiki_catalog_members` (权限管理)
3. **文档层级**：`wiki_documents` → `wiki_document_versions` (版本控制)
4. **互动层级**：`wiki_documents` → `wiki_comments` → `wiki_replies` (评论回复)
5. **统计层级**：`wiki_documents` → `wiki_document_statistics` (数据统计)
6. **状态管理**：`wiki_documents` → `wiki_document_status` → `wiki_document_status_types`
7. **用户组管理**：`wiki_user_groups` → `wiki_user_group_mappings` (用户关联)

### Redmine导入关系链
1. **项目Wiki**：`wikis` → `wiki_pages` → `wiki_contents` → `wiki_content_versions`
2. **重定向**：`wikis` → `wiki_redirects`

## 权限设计模式

### 多层权限控制
1. **空间级权限**：通过`wiki_space_members`控制空间访问权限
2. **目录级权限**：通过`wiki_catalog_members`控制目录访问权限
3. **用户组权限**：通过`wiki_user_groups`和`wiki_user_group_mappings`管理用户组

### 权限字段标准化
- `permission_level` (int) - 权限级别数值
- `role` (string) - 角色名称
- `associated_field` (string) - 关联字段标识

## 软删除设计
所有核心业务表均使用Hyperf的SoftDeletes特性，通过`deleted_at`字段实现软删除，确保数据安全性。

## 状态管理设计
采用类型-值分离的状态管理模式：
- `wiki_document_status_types`：定义状态类型（精华、培训、审核）
- `wiki_document_status`：记录具体状态值和操作历史

## 版本控制设计
- **文档版本**：`wiki_document_versions`记录文档内容变更历史
- **Redmine版本**：`wiki_content_versions`兼容Redmine的版本控制机制

## 统计分析设计
- **实时统计**：文档表中直接维护`view_count`、`like_count`等计数器
- **历史统计**：`wiki_document_statistics`按日期维护历史统计数据

## 特殊设计说明

### 1. 时间处理
- Redmine相关表：时间字段有8小时偏移处理，与原Redmine数据库保持一致
- TchipBi表：标准时间戳处理，格式化显示时去除秒部分

### 2. 编辑器兼容
- 支持多种编辑器类型：HTML编辑器、Markdown编辑器
- 同时存储HTML和Markdown格式内容

### 3. 置顶机制
- 支持文档置顶功能
- 记录置顶时间、置顶人、置顶原因
- 支持置顶历史追踪（`ever_pinned`、`ever_pinned_at`等字段）

---

**文档生成时间**：2025-09-03  
**文档版本**：v1.0  
**涉及表数量**：16个表（TchipBi: 11个，Redmine: 5个）
