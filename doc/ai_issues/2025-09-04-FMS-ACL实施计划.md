### [计划] FMS ACL + 条件控制实施（2025-09-04）

作者：qinsx

---

## 背景与目标

基于 ACL（主体/规则/条件）的文件管理系统后端能力建设，覆盖目录、文件、标签、分享、回收站、审计日志等核心能力，满足按用户/部门/角色与标签/时间/IP 条件进行细粒度控制。

## 主要交付

- 迁移：10 张表（directories/files/subjects/acl_rules/acl_conditions/tags/file_tags/recycle_bin/operation_logs/file_shares）
- 模型/服务/控制器骨架与接口清单（遵循 Controller-Only 规范与 buildparams 统一查询规范）
- 权限判定策略：allow/deny + priority，条件命中后聚合位掩码权限

## 风险与对策（摘）

- 路由冲突（shadowed by）：变量路由增加正则范围；尽量避免与静态路径同级冲突
- 权限计算性能：加索引、短期缓存（用户-目标）
- 外链安全：口令哈希、访问计数与过期校验

## 下一步

1) 完成迁移与模型
2) 打通目录/文件/标签基础接口
3) 集成 ACL 判定引擎与接口
4) 打通分享、回收站、日志

— 记录人：qinsx


