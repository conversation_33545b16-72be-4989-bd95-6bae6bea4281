### FMS 前端组件开发计划（基于后端骨架）

作者：qinsx

---

## 1. 项目概述

围绕 FMS（文件管理系统）的目录、文件、标签、ACL 权限、分享、回收站、审计日志等能力，构建一套可扩展、可维护的前端组件体系。前端与后端接口严格对齐，落实以下目标：
- 统一的组件规范（Props/Events/方法/样式/BEM 命名）
- 统一的权限校验与可视化控制（页面/组件/按钮级）
- 统一的查询/筛选交互（列表上方使用 VabQueryForm 布局）
- 统一的消息提示、确认与对话框规范（$baseMessage、$baseConfirm、firefly-dialog）
- 外链分享的公开访问流程与安全校验

## 2. 技术栈说明

- Vue 3 + Composition API，统一使用 `<script setup>`
- 语言：JavaScript（开发效率优先），同时提供 TypeScript 类型定义（接口与实体类型）
- UI：Element Plus（与项目既有生态保持一致）
- 状态：Pinia
- 请求：Axios（统一拦截、错误处理、数据格式化）
- 样式：SCSS + BEM 命名规范
- 组件规范与项目约束：
  - 所有 ElMessage 使用 `const $baseMessage = inject('$baseMessage')`，示例：`$baseMessage('删除成功', 'success', 'vab-hey-message-success')`
  - 所有确认框使用 `const $baseConfirm = inject('$baseConfirm')`，回调形如 `$baseConfirm('确认删除?', onCancel, onConfirm)`
  - 弃用 `setTimeout`，统一使用 `await new Promise((resolve) => setTimeout(resolve, ms))`
  - 统一以 `firefly-dialog` 代替 `el-dialog`
  - 表格查询项需紧贴表格上方，使用 `<VabQueryForm>` 结构化布局
  - SFC 样式类名需与文件名相关联，防止构建后样式串扰（示例：`fms-file-list__toolbar` 来源文件 `FileList.vue`）

## 3. 目录结构规划（前端模块）

以下结构建立于 `src/views/fms/` 下：

```text
src/views/fms/
├── index.vue                           # FMS 主入口页面
├── components/                         # 子组件目录
│   ├── directory/                      # 目录管理组件
│   │   ├── DirectoryTree.vue          # 目录树组件
│   │   ├── DirectoryList.vue          # 目录列表组件
│   │   ├── CreateDirectoryDialog.vue  # 创建目录对话框（firefly-dialog）
│   │   ├── RenameDirectoryDialog.vue  # 重命名目录对话框（firefly-dialog）
│   │   └── MoveDirectoryDialog.vue    # 移动目录对话框（firefly-dialog）
│   ├── file/                          # 文件管理组件
│   │   ├── FileList.vue               # 文件列表组件
│   │   ├── FileUpload.vue             # 文件上传组件
│   │   ├── FilePreview.vue            # 文件预览组件
│   │   ├── FileVersions.vue           # 文件版本管理组件
│   │   └── FileMetaEditor.vue         # 文件元数据编辑器
│   ├── tag/                           # 标签管理组件
│   │   ├── TagManager.vue             # 标签管理器
│   │   ├── TagSelector.vue            # 标签选择器
│   │   └── CreateTagDialog.vue        # 创建标签对话框（firefly-dialog）
│   ├── acl/                           # 权限控制组件
│   │   ├── PermissionManager.vue      # 权限管理器
│   │   ├── AclRuleEditor.vue          # ACL 规则编辑器
│   │   └── SubjectSelector.vue        # 主体选择器
│   ├── share/                         # 分享管理组件
│   │   ├── ShareManager.vue           # 分享管理器
│   │   ├── CreateShareDialog.vue      # 创建分享对话框（firefly-dialog）
│   │   └── ShareLinkViewer.vue        # 分享链接查看器
│   ├── recycle/                       # 回收站组件
│   │   ├── RecycleBin.vue             # 回收站主组件
│   │   └── RestoreConfirmDialog.vue   # 恢复确认（firefly-dialog + $baseConfirm）
│   ├── log/                           # 审计日志组件
│   │   ├── OperationLogViewer.vue     # 操作日志查看器
│   │   └── LogFilter.vue              # 日志筛选器（含 VabQueryForm）
│   └── common/                        # 通用组件
│       ├── Breadcrumb.vue             # 面包屑导航
│       ├── SearchBar.vue              # 搜索栏
│       ├── FilterPanel.vue            # 筛选面板（封装 VabQueryForm 左右布局）
│       └── Pagination.vue             # 分页组件
├── stores/                            # Pinia
│   ├── fmsStore.js                    # 主状态（目录/文件/当前上下文）
│   ├── directoryStore.js              # 目录状态
│   ├── fileStore.js                   # 文件状态
│   └── permissionStore.js             # 权限状态（位掩码）
└── types/                             # TypeScript 类型定义
    ├── directory.ts                   # 目录类型
    ├── file.ts                        # 文件类型
    ├── permission.ts                  # 权限类型与位枚举
    └── common.ts                      # 通用类型
```

同时规划以下跨模块目录（非 views）：
- `src/api/fms/`：接口模块（见第 5 节）
- `src/utils/permission.ts`：按钮/组件权限指令(目前已存在，根据需要新添方法内容)
- `src/router/guards/permission.ts`：路由守卫
- `src/utils/bmsPermission.ts`：权限校验组合式函数

## 4. 组件设计规范

- 命名与样式
  - 组件名以业务语义命名；样式使用 SCSS + BEM：`<file-name>__block--modifier`
  - 样式类名必须与当前文件名相关联，如：`file-list__toolbar`、`acl-rule-editor__form`
- Props
  - 清晰、原子化，必要处提供校验与默认值；传递只读数据，内部通过 emits 通知变更
- Events
  - 使用 `update:*`/`change`/`submit`/`success` 等语义化事件；统一在文档表中声明
- 方法
  - 异步统一 `try/catch` + `$baseMessage`；确认类行为统一 `$baseConfirm`
  - 延时逻辑统一 `await new Promise((r)=>setTimeout(r, ms))`
- 表格查询
  - 与表格紧贴，封装在 `FilterPanel.vue` 与 `VabQueryForm` 左右面板中
- 对话框
  - 统一采用 `firefly-dialog`，通过 `v-model=` 控制显示
- 可用性
  - 空态、加载态、错误态具备反馈；关键操作记录日志并二次确认

## 5. API 接口对接计划（src/api/fms）

- 目录：`directories.ts`
  - list、tree、create、rename、move、updateVisibility、destroy、restore
- 文件：`files.ts`
  - list、upload、download、update、destroy、restore、versions、revertVersion、updateVisibility
- 标签：`tags.ts`
  - list、create、update、delete、assign、unassign、listByFile
- ACL：`acl.ts`
  - listRules、createRule、deleteRule、addCondition、removeCondition、check、compute
- 分享：`shares.ts`
  - create、revoke、showByToken、validate、downloadByToken
- 回收站：`recycle.ts`
  - listDeleted、restore、purge
- 日志：`logs.ts`
  - list、show

实现要点：
- Axios 封装：统一 baseURL、超时、鉴权头；响应拦截统一处理错误与数据 unwrap
- 错误处理：统一 `$baseMessage(errMsg, 'error', 'vab-hey-message-error')`
- 类型：在 `src/views/fms/types` 定义 DTO 与响应类型；API 方法返回类型统一
- 参数：路径/参数必须与后端文档完全匹配（含正则约束段），例如：`/api/fms/files/{id:[0-9]+}`

## 6. 权限集成方案

- 权限位（与后端对齐，可扩展）：`1=view, 2=upload, 4=delete, 8=share`
- 状态：`permissionStore` 维护当前用户针对目标（目录/文件）的有效权限位
- 组合函数：`usePermission()` 提供 `can(bit, target)`、`hasAny(bits[])`
- 指令：`v-permission="{ bit: 2, target: current }"` 控制按钮显隐/禁用
- 路由守卫：基于路由 `meta.requiredPermission` 校验；外链公开路由绕过鉴权但执行过期/口令校验
- 组件级控制：在入口 `index.vue` 按模块粒度控制是否渲染/可交互
- 按钮级控制：上传/删除/分享按钮基于位判断；无权限给出灰态与提示

## 7. 开发时间线（示意）

- 第 1 周：项目骨架、API 封装、权限基础设施（store/guard/directive/composable）
- 第 2 周：目录模块（树/列表/增删改移动/可见性/回收站对接）
- 第 3 周：文件模块（列表/上传/预览/版本/元数据/回收站）
- 第 4 周：标签模块（管理/选择/绑定检索），ACL 模块（主体/规则/条件）
- 第 5 周：分享模块（创建/校验/公开下载），日志模块（查询/筛选）
- 第 6 周：联调、性能与可用性优化、无障碍与边界用例、验收

---

## 8. 组件清单（逐组件详细表）

说明：以下按模块分类，每个组件提供“基本信息 / Props / Events / 依赖 API / UI 组件库依赖汇总”。

### root（入口）

#### index.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| index.vue | FMS 主入口与模块布局集成 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| 无 | - | - | - |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| 无 | - | - |

- 依赖的 API 接口：多模块汇总视图内按需触发

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| Element Plus | el-container, el-header, el-main, el-aside | 页面布局框架 |
| 自定义 | Breadcrumb, FilterPanel | 导航与查询区容器 |

---

### common 模块

#### Breadcrumb.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| Breadcrumb.vue | 面包屑导航 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| items | Array<{ label: string; to?: string }>| [] | 面包屑项 |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| change | (item) | 选中面包屑项变更 |

- 依赖的 API 接口：无

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| Element Plus | el-breadcrumb, el-breadcrumb-item | 面包屑展示 |

#### SearchBar.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| SearchBar.vue | 搜索输入与提交 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| modelValue | string | '' | 输入值 |
| placeholder | string | '请输入关键字' | 占位提示 |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| update:modelValue | (value: string) | 双向绑定 |
| search | (value: string) | 触发搜索 |

- 依赖的 API 接口：无

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| Element Plus | el-input, el-button | 搜索输入与触发 |

#### FilterPanel.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| FilterPanel.vue | 查询面板（VabQueryForm 包装） |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| modelValue | [string, any] | {} | 表单数据 |
| fields | [any] | [] | 字段配置 |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| submit | (payload) | 提交查询 |
| reset | () | 重置 |

- 依赖的 API 接口：无

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| 自定义 | VabQueryForm, vab-query-form-left-panel, vab-query-form-right-panel | 标准化查询区布局 |
| Element Plus | el-form, el-form-item, el-input, el-select, el-date-picker | 常见筛选字段 |

#### Pagination.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| Pagination.vue | 分页器（左对齐） |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| total | number | 0 | 总数 |
| page | number | 1 | 当前页 |
| pageSize | number | 10 | 每页大小 |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| change | ({ page, pageSize }) | 变更分页 |

- 依赖的 API 接口：无

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| Element Plus | el-pagination | 翻页控件 |

---

### directory 模块

#### DirectoryTree.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| DirectoryTree.vue | 展示与选择目录树 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| selectedId | number \| null | null | 当前选中目录 ID |
| rootId | number \| null | null | 根目录 ID（可选） |
| height | string | '100%' | 视图高度 |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| select | (node) | 选中目录节点 |

- 依赖的 API 接口：directories.tree

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| antd-vue | a-tree | 树结构与滚动(参考/home/<USER>/Project/tchipbi/tchip_bi_frontend/src/views/oa/Wiki/components/WikiContent.vue中的92行a-tree以及相关样式) |

#### DirectoryList.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| DirectoryList.vue | 指定父目录下的子目录列表（含查询） |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| parentId | number \| null | null | 父目录 ID |
| filters | [string, any] | {} | 查询条件 |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| refresh | () | 刷新列表 |
| rename | (row) | 触发重命名动作 |
| move | (row) | 触发移动动作 |

- 依赖的 API 接口：directories.list

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| 自定义 | FilterPanel, Pagination | 查询区与分页 |
| Element Plus | el-table, el-table-column, el-tag, el-button, el-dropdown | 列表展示与操作 |

#### CreateDirectoryDialog.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| CreateDirectoryDialog.vue | 创建目录对话框 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| parentId | number \| null | null | 父目录 ID |
| visible | boolean | false | 显隐控制（firefly-dialog） |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| success | (dir) | 创建成功回调 |
| update:visible | (v: boolean) | 显隐同步 |

- 依赖的 API 接口：directories.create

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| 自定义 | firefly-dialog | 标准化对话框 |
| Element Plus | el-form, el-form-item, el-input, el-button | 表单输入 |

#### RenameDirectoryDialog.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| RenameDirectoryDialog.vue | 重命名目录 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| id | number | - | 目录 ID |
| name | string | '' | 原名称 |
| visible | boolean | false | 显隐控制 |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| success | () | 重命名完成 |
| update:visible | (v: boolean) | 显隐同步 |

- 依赖的 API 接口：directories.rename

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| 自定义 | firefly-dialog | 对话框容器 |
| Element Plus | el-form, el-input, el-button | 表单输入 |

#### MoveDirectoryDialog.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| MoveDirectoryDialog.vue | 移动目录到目标父级 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| id | number | - | 源目录 ID |
| targetParentId | number \| null | null | 目标父目录 |
| visible | boolean | false | 显隐控制 |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| success | () | 移动完成 |
| update:visible | (v: boolean) | 显隐同步 |

- 依赖的 API 接口：directories.move

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| 自定义 | firefly-dialog | 对话框容器 |
| Element Plus & antd-vue | a-tree, el-button | 目录选择与提交 |

---

### file 模块

#### FileList.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| FileList.vue | 文件列表（含查询与标签筛选） |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| directoryId | number | - | 所属目录 |
| filters | [string, any] | {} | 查询条件 |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| refresh | () | 刷新列表 |
| preview | (row) | 预览文件 |
| delete | (row) | 删除文件 |

- 依赖的 API 接口：files.list, tags.listByFile

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| 自定义 | FilterPanel, Pagination | 查询与分页 |
| Element Plus | el-table, el-table-column, el-tag, el-avatar, el-button, el-dropdown | 列表展示与操作 |

#### FileUpload.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| FileUpload.vue | 文件上传到指定目录 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| directoryId | number | - | 目标目录 |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| success | (file) | 上传成功 |

- 依赖的 API 接口：files.upload

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| Element Plus | el-upload, el-button, el-progress | 上传与进度 |

#### FilePreview.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| FilePreview.vue | 预览或下载文件 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| fileId | number | - | 文件 ID |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| close | () | 关闭预览 |

- 依赖的 API 接口：files.download

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| Element Plus | el-image, el-dialog（或 firefly-dialog 外层） | 基础预览容器 |
| 第三方 | pdf 预览/文本高亮组件（可选） | 特定格式预览 |

#### FileVersions.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| FileVersions.vue | 文件版本列表与回滚 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| fileId | number | - | 文件 ID |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| revert | (version) | 回滚版本 |

- 依赖的 API 接口：files.versions, files.revertVersion

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| Element Plus | el-table, el-tag, el-button | 版本列表与操作 |

#### FileMetaEditor.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| FileMetaEditor.vue | 编辑文件元数据 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| fileId | number | - | 文件 ID |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| success | () | 更新成功 |

- 依赖的 API 接口：files.update

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| Element Plus | el-form, el-input, el-select, el-button | 元数据表单 |

---

### tag 模块

#### TagManager.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| TagManager.vue | 标签管理（增删改查） |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| 无 | - | - | 独立页面组件 |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| success | () | 变更后通知刷新 |

- 依赖的 API 接口：tags.list, tags.create, tags.update, tags.delete

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| Element Plus | el-table, el-form, el-input, el-button, el-tag | 标签管理 |

#### TagSelector.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| TagSelector.vue | 标签选择（支持多选） |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| modelValue | number[] \| number \| null | null | 已选标签 |
| multiple | boolean | false | 是否多选 |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| update:modelValue | (value) | 双向绑定 |

- 依赖的 API 接口：tags.list

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| Element Plus | el-select, el-option, el-tag | 选择与显示 |

#### CreateTagDialog.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| CreateTagDialog.vue | 创建新标签 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| visible | boolean | false | 显隐控制 |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| success | (tag) | 创建成功 |
| update:visible | (v: boolean) | 显隐同步 |

- 依赖的 API 接口：tags.create

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| 自定义 | firefly-dialog | 对话框容器 |
| Element Plus | el-form, el-input, el-button | 输入表单 |

---

### acl 模块

#### PermissionManager.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| PermissionManager.vue | 针对目标资源的权限规则配置与查看 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| targetType | 'directory' \| 'file' | 'file' | 规则目标类型 |
| targetId | number | - | 目标 ID |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| success | () | 更新权限成功 |

- 依赖的 API 接口：acl.listRules, acl.createRule, acl.deleteRule

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| Element Plus | el-table, el-form, el-select, el-input, el-button, el-tag | 规则配置与展示 |

#### AclRuleEditor.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| AclRuleEditor.vue | 编辑单条 ACL 规则的条件 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| rule | [string, any] | {} | 规则对象 |
| visible | boolean | false | 显隐控制 |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| success | () | 条件编辑完成 |
| update:visible | (v: boolean) | 显隐同步 |

- 依赖的 API 接口：acl.addCondition, acl.removeCondition

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| 自定义 | firefly-dialog | 条件编辑弹窗 |
| Element Plus | el-form, el-input, el-select, el-date-picker | 条件参数输入 |

#### SubjectSelector.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| SubjectSelector.vue | 选择权限主体（用户/部门/角色） |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| modelValue | any[] \| any | null | 选中主体 |
| types | string[] | ['user','dept','role'] | 可选主体类型 |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| update:modelValue | (value) | 双向绑定 |

- 依赖的 API 接口：acl.getSubjects（或项目既有用户/部门/角色服务）

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| Element Plus | el-select, el-option, el-tree-select（如有） | 主体选择 |

---

### share 模块

#### ShareManager.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| ShareManager.vue | 文件分享创建与管理 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| fileId | number | - | 文件 ID |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| success | () | 分享变更完成 |

- 依赖的 API 接口：shares.create, shares.revoke

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| Element Plus | el-form, el-input, el-switch, el-date-picker, el-button | 分享参数配置 |

#### CreateShareDialog.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| CreateShareDialog.vue | 新建外链分享 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| fileId | number | - | 文件 ID |
| visible | boolean | false | 显隐控制 |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| success | (share) | 创建完成 |
| update:visible | (v: boolean) | 显隐同步 |

- 依赖的 API 接口：shares.create

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| 自定义 | firefly-dialog | 对话框容器 |
| Element Plus | el-form, el-input, el-switch, el-date-picker, el-button | 输入表单 |

#### ShareLinkViewer.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| ShareLinkViewer.vue | 展示分享链接与口令 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| token | string | '' | 分享 Token |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| copy | () | 复制链接/口令 |

- 依赖的 API 接口：shares.showByToken

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| Element Plus | el-input, el-button, el-alert | 显示与复制 |

---

### recycle 模块

#### RecycleBin.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| RecycleBin.vue | 查看并操作回收站 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| filters | [string, any] | {} | 查询条件 |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| restore | (row) | 恢复目标 |
| purge | (row) | 彻底删除 |

- 依赖的 API 接口：recycle.listDeleted, recycle.restore, recycle.purge

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| 自定义 | FilterPanel, Pagination | 查询与分页 |
| Element Plus | el-table, el-table-column, el-tag, el-button | 列表与操作 |

#### RestoreConfirmDialog.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| RestoreConfirmDialog.vue | 恢复二次确认 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| targetType | 'directory' \| 'file' | 'file' | 目标类型 |
| targetId | number | - | 目标 ID |
| visible | boolean | false | 显隐控制 |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| success | () | 确认恢复完成 |
| update:visible | (v: boolean) | 显隐同步 |

- 依赖的 API 接口：recycle.restore

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| 自定义 | firefly-dialog, $baseConfirm | 确认弹窗与回调 |
| Element Plus | el-result, el-button | 结果展示 |

---

### log 模块

#### OperationLogViewer.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| OperationLogViewer.vue | 操作日志查询与查看 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| filters | [string, any] | {} | 查询条件 |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| search | (payload) | 条件查询 |

- 依赖的 API 接口：logs.list

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| 自定义 | FilterPanel, Pagination | 查询与分页 |
| Element Plus | el-table, el-table-column, el-tag, el-button, el-tooltip | 日志展示与交互 |

#### LogFilter.vue

- 基本信息

| 名称 | 功能描述 |
|:-|:-|
| LogFilter.vue | 日志筛选条件面板 |

- Props 定义

| 参数名 | 类型 | 默认值 | 描述 |
|:-|:-|:-|:-|
| modelValue | [string, any] | {} | 条件对象 |

- Events 定义

| 事件名 | 参数 | 描述 |
|:-|:-|:-|
| submit | (payload) | 提交查询 |
| reset | () | 重置 |

- 依赖的 API 接口：无

- UI 组件库依赖汇总

| 类别 | 组件 | 使用场景 |
|:-|:-|:-|
| 自定义 | VabQueryForm | 查询布局 |
| Element Plus | el-form, el-form-item, el-input, el-select, el-date-picker | 常用筛选输入 |
---
### 权限与交互约束补充

- 所有消息提示统一 `$baseMessage`：`const $baseMessage = inject('$baseMessage')`
- 所有确认统一 `$baseConfirm`：`const $baseConfirm = inject('$baseConfirm')`
- 对话框统一 `firefly-dialog`，通过 `v-model` 控制显隐
- 表格查询区紧贴表格上方；分页组件在底部左对齐（flex-start）
- 权限控制通过 `permissionStore + bmsPermission + v-permission` 组合实现

---

© 2025 tchipbi FMS 前端组件开发计划 | 作者：qinsx


