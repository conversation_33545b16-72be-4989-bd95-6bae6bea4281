### FMS（文件管理系统）ACL + 条件控制 实施计划

作者：qinsx

---

## 1. 目标与范围

- 构建基于“主体（用户/部门/角色）+ 规则 + 条件（标签/时间/IP）”的 ACL 权限模型，覆盖目录与文件级别的查看、上传、删除、分享等细粒度权限。
- 支持层级目录、文件版本、标签分类、软删除回收站、操作日志审计、外链分享。
- 后端采用 Hyperf（PHP 7.4）实现，遵守本项目 Controller/Service 分层规范与查询构建规范（BusinessService + buildparams）。

## 2. 整体里程碑

1) 基础表与模型（目录/文件/标签/回收站/日志/外链）
2) ACL 主体、规则、条件与判定引擎
3) 业务能力（目录管理、文件管理、标签绑定、回收站、审计日志）
4) 外链分享（令牌、口令、过期、访问统计）
5) 性能与安全（索引、缓存、条件评估优化、审计与风控）

## 3. 架构概述

- 存储层：MySQL（迁移管理），文件存储使用 Flysystem 适配器（由应用层封装使用）。
- 领域层：
  - 目录模块（Directory）：层级结构、排序、可见性、软删/恢复
  - 文件模块（File）：上传/下载、版本、标签、排序、软删/恢复
  - 标签模块（Tag）：标签 CRUD、文件-标签绑定
  - ACL 模块（Acl）：主体/规则/条件、权限计算与冲突优先级
  - 分享模块（Share）：外链 Token/密码/过期、访问计数
  - 回收站模块（Recycle）：统一软删与恢复
  - 审计模块（OperationLog）：所有关键操作记录
- 接口层：遵循 Controller 只做入参校验、调用 Service、返回标准响应。
- 基础设施：Carbon::now()、路由变量加正则约束、分页入参统一从 BaseController::getParams() 获取。

```mermaid
graph TD
  subgraph A[接口层 Controller]
    A1[DirectoryController]
    A2[FileController]
    A3[TagController]
    A4[AclController]
    A5[ShareController]
    A6[RecycleController]
    A7[OperationLogController]
  end
  subgraph B[领域服务 Service]
    B1[DirectoryService]
    B2[FileService]
    B3[TagService]
    B4[AclService]
    B5[ShareService]
    B6[RecycleService]
    B7[OperationLogService]
  end
  subgraph C[模型/数据 Model]
    C1[fms_directories]
    C2[fms_files]
    C3[fms_tags]
    C4[fms_file_tags]
    C5[fms_subjects]
    C6[fms_acl_rules]
    C7[fms_acl_conditions]
    C8[fms_recycle_bin]
    C9[fms_operation_logs]
    C10[fms_file_shares]
  end
  A1-->B1-->C1
  A2-->B2-->C2
  A3-->B3-->C3 & C4
  A4-->B4-->C5 & C6 & C7
  A5-->B5-->C10
  A6-->B6-->C8
  A7-->B7-->C9
  B2-.标签条件/检索.->B3
  B2-.权限判定.->B4
  B1-.权限判定.->B4
  B5-.权限判定.->B4
```

## 4. 数据库与索引

- 全量采用迁移文件管理，命名：时间戳_create_xxx_table.php
- 关键索引建议：
  - fms_directories(parent_id)、(path)、(owner_type, owner_id)、(is_deleted)
  - fms_files(directory_id)、(name, directory_id)、(is_deleted)、(visibility)
  - fms_acl_rules(target_type, target_id, priority)、(subject_id)
  - fms_acl_conditions(rule_id, condition_type)
  - fms_file_tags(file_id, tag_id) 唯一键
  - fms_file_shares(share_token) 唯一键、(expire_at)
  - 所有 created_at/updated_at 常用时间查询字段建议加索引（视业务）

## 5. 权限判定策略（Allow/Deny + Priority）

- 效果集：allow/deny，优先级数值越大优先级越高。
- 计算顺序：
  1) 收集主体集合（用户本身 + 所在部门 + 角色），基于 `fms_subjects`
  2) 拉取目标（目录/文件）相关规则，按 `priority` 降序
  3) 逐条评估条件（标签/时间/IP），未命中条件的规则跳过
  4) 聚合 `permission_set`（位掩码），遇到 deny 则对对应位进行覆盖拒绝
  5) 若无任何 allow，默认为无权限

## 6. 开发规范与约束

- PHP 7.4 兼容：不用属性语法，采用注解（如 @Listener），类型提示保持严格。
- Hyperf 路由：`/{id}` 等变量需加正则或避免与静态路径冲突，防止 shadowed by。
- 时间：统一使用 `Carbon::now()`。
- 控制器：
  - 仅做入参校验（BaseController::getParams()）、调用服务、返回响应
  - 列表类接口分页/排序遵循 buildparams 规范（Service 继承 BusinessService）
- Service：
  - 方法签名使用独立参数，类型明确并给默认值
  - 统一 `buildparams($filter, $op, $sort, $order, $limit, $query)` 构建查询

## 7. 流程概要

- 目录：创建/重命名/移动/删除(软)/恢复/树查询/排序
- 文件：上传/下载/更新元数据/版本管理/标签绑定/删除(软)/恢复
- 标签：CRUD/绑定解绑/按标签检索
- ACL：主体-规则-条件 CRUD、权限计算与检查
- 分享：创建/撤销/访问校验/下载/访问统计
- 回收站：列表/恢复/清理
- 日志：全链路操作记录

## 8. 非功能性需求

- 安全：
  - 外链访问次数与过期控制、口令哈希存储
  - ACL 条件评估防止旁路（下载/外链统一走鉴权）
- 性能：
  - 常用维度加索引
  - ACL 计算结果可做短期缓存（用户-目标-权限位）
- 可观测性：
  - 关键业务操作落 `fms_operation_logs`

## 9. 验收标准（摘）

- 全部表结构与索引按迁移创建成功
- 目录/文件/标签/ACL/分享/回收站/日志 API 全量打通
- 权限判定在常见组合（个人、部门、角色 + 条件）下正确
- 外链口令与过期控制生效，访问计数准确

---

© 2025 tchipbi FMS 规划 | 作者：qinsx


