### FMS 后端开发计划骨架（Hyperf + PHP 7.4）

作者：qinsx

---

## 1. 模块划分与依赖

- 目录模块 Directory ⇄ ACL、日志
- 文件模块 File ⇄ 目录、标签、ACL、分享、日志
- 标签模块 Tag ⇄ 文件
- ACL 模块 Acl ⇄ 目录/文件/主体/条件
- 分享模块 Share ⇄ 文件、ACL
- 回收站模块 Recycle ⇄ 目录/文件
- 审计模块 OperationLog ⇄ 所有业务

```mermaid
graph LR
  Directory-->Acl
  File-->Directory
  File-->Tag
  File-->Acl
  File-->Share
  Share-->Acl
  Recycle-->Directory
  Recycle-->File
  OperationLog-->Directory
  OperationLog-->File
  OperationLog-->Acl
  OperationLog-->Share
```

## 2. 目录结构建议（节选）

- app/Model/TchipBiFms/*
- app/Core/Service/TchipBiFms/*
- app/Controller/TchipBiFms/*
- migrations/*

## 3. 数据库迁移文件（建议命名）

| 表 | 迁移文件名 |
|:-|:-|
| fms_directories | 20250904090001_create_fms_directories_table.php |
| fms_files | 20250904090002_create_fms_files_table.php |
| fms_subjects | 20250904090003_create_fms_subjects_table.php |
| fms_acl_rules | 20250904090004_create_fms_acl_rules_table.php |
| fms_acl_conditions | 20250904090005_create_fms_acl_conditions_table.php |
| fms_tags | 20250904090006_create_fms_tags_table.php |
| fms_file_tags | 20250904090007_create_fms_file_tags_table.php |
| fms_recycle_bin | 20250904090008_create_fms_recycle_bin_table.php |
| fms_operation_logs | 20250904090009_create_fms_operation_logs_table.php |
| fms_file_shares | 20250904090010_create_fms_file_shares_table.php |

## 4. 模型文件（Model）

| 模型 | 路径 |
|:-|:-|
| FmsDirectory | app/Model/TchipBiFms/FmsDirectory.php |
| FmsFile | app/Model/TchipBiFms/FmsFile.php |
| FmsSubject | app/Model/TchipBiFms/FmsSubject.php |
| FmsAclRule | app/Model/TchipBiFms/FmsAclRule.php |
| FmsAclCondition | app/Model/TchipBiFms/FmsAclCondition.php |
| FmsTag | app/Model/TchipBiFms/FmsTag.php |
| FmsFileTag | app/Model/TchipBiFms/FmsFileTag.php |
| FmsRecycleBin | app/Model/TchipBiFms/FmsRecycleBin.php |
| FmsOperationLog | app/Model/TchipBiFms/FmsOperationLog.php |
| FmsFileShare | app/Model/TchipBiFms/FmsFileShare.php |





## 5. 服务层文件（Service）与方法

### DirectoryService

* 路径：`app/Core/Service/TchipBiFms/DirectoryService.php`

| 方法                  | 签名示意                                                                                                                                                                         |
| :------------------ | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| createDirectory     | `createDirectory(int $parentId=null, int $ownerId, string $ownerType, string $name, string $visibility='public'): array`                                                     |
| renameDirectory     | `renameDirectory(int $id, string $name): bool`                                                                                                                               |
| moveDirectory       | `moveDirectory(int $id, int $newParentId): bool`                                                                                                                             |
| updateVisibility    | `updateVisibility(int $id, string $visibility): bool`                                                                                                                        |
| listChildren        | `listChildren(int $parentId=null, array $filter=[], array $op=[], string $sort='id', string $order='DESC', int $limit=10, string $search='', array $searchFields=[]): array` |
| getTree             | `getTree(): array`                                                                                                                                                           |
| softDeleteDirectory | `softDeleteDirectory(int $id, int $deletedBy): bool`                                                                                                                         |
| restoreDirectory    | `restoreDirectory(int $id, int $restoredBy): bool`                                                                                                                           |
| updateSortOrder     | `updateSortOrder(array $idToOrder): bool`                                                                                                                                    |

---

### FileService

* 路径：`app/Core/Service/TchipBiFms/FileService.php`

| 方法               | 签名示意                                                                                                                                                                    |
| :--------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| uploadFile       | `uploadFile(int $directoryId, string $name, string $path, int $size, string $mimeType, int $createdBy, string $visibility='public'): array`                             |
| downloadFile     | `downloadFile(int $id): array`                                                                                                                                          |
| updateFileMeta   | `updateFileMeta(int $id, array $changes): bool`                                                                                                                         |
| listFiles        | `listFiles(int $directoryId, array $filter=[], array $op=[], string $sort='id', string $order='DESC', int $limit=10, string $search='', array $searchFields=[]): array` |
| softDeleteFile   | `softDeleteFile(int $id, int $deletedBy): bool`                                                                                                                         |
| restoreFile      | `restoreFile(int $id, int $restoredBy): bool`                                                                                                                           |
| listVersions     | `listVersions(int $id): array`                                                                                                                                          |
| revertVersion    | `revertVersion(int $id, int $version, int $userId): bool`                                                                                                               |
| updateVisibility | `updateVisibility(int $id, string $visibility): bool`                                                                                                                   |

---

### TagService

* 路径：`app/Core/Service/TchipBiFms/TagService.php`

| 方法                | 签名示意                                                                                                                                                 |
| :---------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------- |
| createTag         | `createTag(string $name, int $userId): array`                                                                                                        |
| updateTag         | `updateTag(int $id, string $name, int $userId): bool`                                                                                                |
| deleteTag         | `deleteTag(int $id, int $userId): bool`                                                                                                              |
| listTags          | `listTags(array $filter=[], array $op=[], string $sort='id', string $order='DESC', int $limit=10, string $search='', array $searchFields=[]): array` |
| assignTagsToFile  | `assignTagsToFile(int $fileId, array $tagIds, int $userId): bool`                                                                                    |
| removeTagFromFile | `removeTagFromFile(int $fileId, int $tagId, int $userId): bool`                                                                                      |
| listTagsByFile    | `listTagsByFile(int $fileId): array`                                                                                                                 |

---

### AclService

* 路径：`app/Core/Service/TchipBiFms/AclService.php`

| 方法                          | 签名示意                                                                                                                        |
| :-------------------------- | :-------------------------------------------------------------------------------------------------------------------------- |
| listRules                   | `listRules(string $targetType, int $targetId): array`                                                                       |
| createRule                  | `createRule(string $targetType, int $targetId, int $subjectId, int $permissionSet, string $effect, int $priority=0): array` |
| deleteRule                  | `deleteRule(int $ruleId): bool`                                                                                             |
| addCondition                | `addCondition(int $ruleId, string $conditionType, string $conditionValue): array`                                           |
| removeCondition             | `removeCondition(int $conditionId): bool`                                                                                   |
| checkPermission             | `checkPermission(int $userId, string $targetType, int $targetId, int $permissionBit): bool`                                 |
| computeEffectivePermissions | `computeEffectivePermissions(int $userId, string $targetType, int $targetId): int`                                          |
| getSubjectsForUser          | `getSubjectsForUser(int $userId): array`                                                                                    |
| evaluateConditions          | `evaluateConditions(array $conditions, int $fileId=null): bool`                                                             |

---

### ShareService

* 路径：`app/Core/Service/TchipBiFms/ShareService.php`

| 方法               | 签名示意                                                                                                                   |
| :--------------- | :--------------------------------------------------------------------------------------------------------------------- |
| createShare      | `createShare(int $fileId, int $createdBy, bool $isPublic=true, ?string $password=null, ?string $expireAt=null): array` |
| revokeShare      | `revokeShare(int $shareId, int $userId): bool`                                                                         |
| getShareByToken  | `getShareByToken(string $token): array`                                                                                |
| validatePassword | `validatePassword(string $token, ?string $password): bool`                                                             |
| recordVisit      | `recordVisit(int $shareId): void`                                                                                      |

---

### RecycleService

* 路径：`app/Core/Service/TchipBiFms/RecycleService.php`

| 方法          | 签名示意                                                                                                                                                            |
| :---------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| listDeleted | `listDeleted(array $filter=[], array $op=[], string $sort='deleted_at', string $order='DESC', int $limit=10, string $search='', array $searchFields=[]): array` |
| restore     | `restore(string $targetType, int $targetId, int $userId): bool`                                                                                                 |
| purge       | `purge(string $targetType, int $targetId, int $userId): bool`                                                                                                   |

---

### OperationLogService

* 路径：`app/Core/Service/TchipBiFms/OperationLogService.php`

| 方法       | 签名示意                                                                                                                                                         |
| :------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------- |
| log      | `log(int $userId, string $action, string $targetType, int $targetId, array $detail=[]): void`                                                                |
| listLogs | `listLogs(array $filter=[], array $op=[], string $sort='created_at', string $order='DESC', int $limit=10, string $search='', array $searchFields=[]): array` |
| getLog   | `getLog(int $id): array`                                                                                                                                     |

---
说明：Service 层统一继承 BusinessService，在列表方法中使用 `$this->buildparams($filter, $op, $sort, $order, $limit, $query)`

---


## 6. 控制器文件（Controller）与接口

### DirectoryController
- 路径：`app/Controller/TchipBiFms/DirectoryController.php`

| 方法 | HTTP | Path |
|:-|:-|:-|
| index | GET | /api/fms/directories |
| tree | GET | /api/fms/directories/tree |
| store | POST | /api/fms/directories |
| rename | PUT | /api/fms/directories/{id:[0-9]+}/rename |
| move | PUT | /api/fms/directories/{id:[0-9]+}/move |
| updateVisibility | PUT | /api/fms/directories/{id:[0-9]+}/visibility |
| destroy | DELETE | /api/fms/directories/{id:[0-9]+} |
| restore | POST | /api/fms/directories/{id:[0-9]+}/restore |

### FileController
- 路径：`app/Controller/TchipBiFms/FileController.php`

| 方法 | HTTP | Path |
|:-|:-|:-|
| index | GET | /api/fms/files?directory_id=... |
| upload | POST | /api/fms/files |
| download | GET | /api/fms/files/{id:[0-9]+}/download |
| update | PUT | /api/fms/files/{id:[0-9]+} |
| destroy | DELETE | /api/fms/files/{id:[0-9]+} |
| restore | POST | /api/fms/files/{id:[0-9]+}/restore |
| versions | GET | /api/fms/files/{id:[0-9]+}/versions |
| revertVersion | POST | /api/fms/files/{id:[0-9]+}/versions/{version:[0-9]+}/revert |
| updateVisibility | PUT | /api/fms/files/{id:[0-9]+}/visibility |

### TagController
- 路径：`app/Controller/TchipBiFms/TagController.php`

| 方法 | HTTP | Path |
|:-|:-|:-|
| index | GET | /api/fms/tags |
| store | POST | /api/fms/tags |
| update | PUT | /api/fms/tags/{id:[0-9]+} |
| destroy | DELETE | /api/fms/tags/{id:[0-9]+} |
| assign | POST | /api/fms/tags/assign |
| unassign | POST | /api/fms/tags/unassign |
| listByFile | GET | /api/fms/files/{id:[0-9]+}/tags |

### AclController
- 路径：`app/Controller/TchipBiFms/AclController.php`

| 方法 | HTTP | Path |
|:-|:-|:-|
| listRules | GET | /api/fms/acl/rules?target_type=&target_id= |
| createRule | POST | /api/fms/acl/rules |
| deleteRule | DELETE | /api/fms/acl/rules/{id:[0-9]+} |
| addCondition | POST | /api/fms/acl/rules/{id:[0-9]+}/conditions |
| removeCondition | DELETE | /api/fms/acl/conditions/{id:[0-9]+} |
| check | POST | /api/fms/acl/check |
| compute | POST | /api/fms/acl/compute |

### ShareController
- 路径：`app/Controller/TchipBiFms/ShareController.php`

| 方法 | HTTP | Path |
|:-|:-|:-|
| create | POST | /api/fms/shares |
| revoke | DELETE | /api/fms/shares/{id:[0-9]+} |
| showByToken | GET | /api/fms/shares/{token:[A-Za-z0-9_\-]{8,64}} |
| validate | POST | /api/public/shares/{token:[A-Za-z0-9_\-]{8,64}}/verify |
| downloadByToken | GET | /api/public/shares/{token:[A-Za-z0-9_\-]{8,64}}/download |

### RecycleController
- 路径：`app/Controller/TchipBiFms/RecycleController.php`

| 方法 | HTTP | Path |
|:-|:-|:-|
| index | GET | /api/fms/recycle-bin |
| restore | POST | /api/fms/recycle-bin/{id:[0-9]+}/restore |
| purge | DELETE | /api/fms/recycle-bin/{id:[0-9]+} |

### OperationLogController
- 路径：`app/Controller/TchipBiFms/OperationLogController.php`

| 方法 | HTTP | Path |
|:-|:-|:-|
| index | GET | /api/fms/logs |
| show | GET | /api/fms/logs/{id:[0-9]+} |

要求：Controller 仅负责参数获取与校验（使用 `BaseController::getParams()`）、调用 Service、返回统一结构。

## 7. API 约定与权限位

- 权限位（示例）：
  - 1=view, 2=upload, 4=delete, 8=share（可扩展）
- 头部/鉴权：所有需要鉴权的接口通过统一中间件校验用户身份与 ACL。
- 分页查询：参数统一（filter、op、sort、order、limit、search、searchFields），由 Service 的 `buildparams` 处理。

## 8. 开发注意事项

- PHP 7.4：严格类型与注解风格（如 @Listener）
- Carbon::now() 替代 now()
- 路由变量加正则范围，避免 shadowed by 冲突
- 软删通过 `fms_recycle_bin` 统一恢复/清理
- 操作日志：上传/下载/删除/恢复/创建目录/重命名等关键操作全部记录

---

## 附：表设计
表设计 **ACL + 条件控制** 
更灵活的 **主体+规则+条件** 模型：


----

#### 📁 `fms_directories`!!（目录表）


|字段名|类型|说明|
|:-:|:-:|:-:|
|id|BIGINT PK AUTO_INCREMENT|主键 ID|
|parent_id|BIGINT NULL|父目录 ID，NULL=根目录|
|owner_id|BIGINT|拥有者 ID（用户或部门）|
|owner_type|VARCHAR(50)|拥有者类型ENUM('user','dept')|
|name|VARCHAR(255)|目录名称|
|path|VARCHAR(500)|\/1/2/3\/|
|visibility|VARCHAR(50)|可见性范围ENUM('public','department','user','private') DEFAULT 'public'|
|sort_order|INT DEFAULT 0|目录排序（拖拽排序用）|
|is_deleted|TINYINT(1) DEFAULT 0|是否删除（回收站标记）|
|updated_by|BIGINT|最后修改者 ID|
|created_at|DATETIME|创建时间|
|updated_at|DATETIME|更新时间|


----

### 📑 `fms_files`（文件表）


|字段名|类型|说明|
|:-:|:-:|:-:|
|id|BIGINT PK AUTO_INCREMENT|主键 ID|
|directory_id|BIGINT|所属目录 ID|
|name|VARCHAR(255)|文件名|
|path|VARCHAR(500)|文件存储路径（Flysystem 适配器）|
|size|BIGINT|文件大小（字节）|
|mime_type|VARCHAR(100)|文件类型（MIME）|
|version|INT DEFAULT 1|版本号（覆盖上传时递增）|
|visibility|VARCHAR(50)|可见性范围ENUM('public','department','user','private') DEFAULT 'public'|
|sort_order|INT DEFAULT 0|文件排序（拖拽排序用）|
|is_deleted|TINYINT(1) DEFAULT 0|是否删除（回收站标记）|
|created_by|BIGINT|上传者 ID|
|updated_by|BIGINT|最后修改者 ID|
|created_at|DATETIME|创建时间|
|updated_at|DATETIME|更新时间|


----

### 👥 `fms_subjects`（权限主体表）


|字段名|类型|说明|
|:-:|:-:|:-:|
|id|BIGINT PK AUTO_INCREMENT|主键 ID|
|subject_type|VARCHAR(50)|主体类型ENUM('user','dept','role')|
|subject_id|BIGINT|对应用户/部门/角色 ID|


----

### 🔑 `fms_acl_rules`（访问控制规则表）


|字段名|类型|说明|
|:-:|:-:|:-:|
|id|BIGINT PK AUTO_INCREMENT|主键 ID|
|target_type|VARCHAR(50)|授权目标类型ENUM('directory','file')|
|target_id|BIGINT|目录或文件 ID|
|subject_id|BIGINT|引用 fms_subjects.id|
|permission_set|INT|位掩码权限集细粒度权限集合（"view""upload""delete""share"）|
|effect|ENUM('allow','deny')|允许/拒绝 |
|priority|INT DEFAULT 0|规则优先级（越大越优先），用于不同种类规则（个人偶or部门）冲突解决|
|created_at|DATETIME|创建时间|
|updated_at|DATETIME|更新时间|


----

### ⚙️ `fms_acl_conditions`（访问控制条件表）


|字段名|类型|说明|
|:-:|:-:|:-:|
|id|BIGINT PK AUTO_INCREMENT|主键 ID|
|rule_id|BIGINT|关联 fms_acl_rules.id|
|condition_type|VARCHAR(50)|条件类型('tag','time','ip')|
|condition_value|VARCHAR(255)|条件值（如标签=confidential，时间=2025-01-01~2025-12-31，IP=***********/24）|


----

### 🏷️ `fms_tags`（标签表）


|字段名|类型|说明|
|:-:|:-:|:-:|
|id|BIGINT PK AUTO_INCREMENT|主键 ID|
|name|VARCHAR(50)|标签名称|
|created_by|BIGINT|创建者 ID|
|updated_by|BIGINT|最后修改者 ID|
|created_at|DATETIME|创建时间|


----

### 🔗 `fms_file_tags`（文件-标签关系表）


|字段名|类型|说明|
|:-:|:-:|:-:|
|id|BIGINT PK AUTO_INCREMENT|主键 ID|
|file_id|BIGINT|文件 ID|
|tag_id|BIGINT|标签 ID|
|created_by|BIGINT|创建者 ID|
|updated_by|BIGINT|最后修改者 ID|
|created_at|DATETIME|创建时间|


----

### 🗑️ `fms_recycle_bin`（回收站表）


|字段名|类型|说明|
|:-:|:-:|:-:|
|id|BIGINT PK AUTO_INCREMENT|主键 ID|
|target_type|VARCHAR(50)|删除对象类型ENUM('directory','file')|
|target_id|BIGINT|对应目录/文件 ID|
|deleted_by|BIGINT|删除者 ID|
|deleted_at|DATETIME|删除时间|


----

### 📝 `fms_operation_logs`（操作日志表）


|字段名|类型|说明|
|:-:|:-:|:-:|
|id|BIGINT PK AUTO_INCREMENT|主键 ID|
|user_id|BIGINT|操作者 ID|
|action|VARCHAR(50)|操作类型('upload','download','delete','restore','create_dir','rename')|
|target_type| VARCHAR(50)|操作目标类型('directory','file')|
|target_id|BIGINT|操作对象 ID|
|detail|TEXT|操作详情（JSON，可选）|
|created_at|DATETIME|操作时间|


----

### 📤 `fms_file_shares`（外链分享表）


|字段名|类型|说明|
|:-:|:-:|:-:|
|id|BIGINT PK AUTO_INCREMENT|主键 ID|
|file_id|BIGINT|被分享的文件 ID|
|share_token|VARCHAR(64) UNIQUE|外链唯一标识（随机字符串/UUID）|
|created_by|BIGINT|分享发起人（用户 ID）|
|is_public|TINYINT(1) DEFAULT 1|是否公开（1=无需密码，0=需密码）|
|expire_at|DATETIME NULL|过期时间（NULL=永不过期）|
|password_hash| VARCHAR(255)| NULL COMMENT '外链访问密码（hash存储）';
|visits|INT DEFAULT 0|当前访问次数|
|created_at|DATETIME|创建时间|
|updated_at|DATETIME|更新时间|


----

### ✅ 关系概览：


- **fms_directories + fms_files** → 基础存储结构

- **fms_subjects + fms_acl_rules + fms_acl_conditions** → 高级 ACL 权限体系（可基于用户/部门/角色 + 条件控制）

- **fms_tags + fms_file_tags** → 文件属性扩展（支持 ACL 条件、检索、分类）

- **fms_recycle_bin** → 软删除兜底

- **fms_operation_logs** → 审计追踪

- **fms_file_shares** → 外链分享


----


© 2025 tchipbi FMS 后端骨架 | 作者：qinsx


