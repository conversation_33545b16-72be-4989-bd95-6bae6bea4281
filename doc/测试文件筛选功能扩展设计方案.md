# 测试文件筛选功能扩展设计方案（集成版）

## 一、背景分析

### 1.1 现状说明
当前系统存储了三种测试文件类型：
- **agingTest_result.txt**：老化测试结果文件
- **factoryTest_result.txt**：厂测结果文件  
- **其他测试文件**：测试日志等辅助文件

现有的 `bi_file_sync` 表仅记录了文件的基本信息，缺少对文件内容的结构化存储，导致无法实现精细化的筛选功能。

### 1.2 需求分析
需要对以下关键数据进行筛选：
- **老化测试数据**：运行时间、CPU温度、GPU温度、异常重启次数、老化测试次数等
- **厂测数据**：设备名、固件版本、内存容量、闪存容量、测试结果（成功/失败）、各测试项状态等

### 1.3 技术方案调整
- **利用现有流程**：在 `ReorganizeService::processFile` 方法中直接集成文件解析逻辑
- **同步处理**：文件复制到目标目录后立即解析，无需异步队列
- **扩展现有方法**：基于已有的 `parseAgingTestResult` 和 `parseFactoryTestResult` 方法进行扩展

## 二、数据库设计方案

### 2.1 关联表设计

#### 2.1.1 老化测试结果表 (bi_aging_test_result)
```sql
CREATE TABLE `bi_aging_test_result` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `file_sync_id` bigint unsigned NOT NULL COMMENT '关联文件表ID',
  `product` varchar(64) NOT NULL COMMENT '产品名称',
  `sn` varchar(64) NOT NULL COMMENT '序列号',
  `test_datetime` varchar(32) NOT NULL COMMENT '测试日期时间',
  
  -- 老化测试特有字段
  `soc_type` varchar(32) NULL COMMENT 'SOC型号',
  `cpu_cores` int NULL COMMENT 'CPU核心数',
  `cpu0_freq` int NULL COMMENT 'CPU0频率(MHz)',
  `cpu4_freq` int NULL COMMENT 'CPU4频率(MHz)',
  `cpu6_freq` int NULL COMMENT 'CPU6频率(MHz)',
  `gpu_freq` int NULL COMMENT 'GPU频率(MHz)',
  `npu_freq` int NULL COMMENT 'NPU频率(MHz)',
  `ddr_freq` int NULL COMMENT 'DDR频率(MHz)',
  `cpu_temp` decimal(5,2) NULL COMMENT 'CPU温度(°C)',
  `gpu_temp` decimal(5,2) NULL COMMENT 'GPU温度(°C)',
  `npu_load_core0` decimal(5,2) NULL COMMENT 'NPU Core0负载(%)',
  `npu_load_core1` decimal(5,2) NULL COMMENT 'NPU Core1负载(%)',
  `total_memory` int NULL COMMENT '总内存(MB)',
  `available_memory` int NULL COMMENT '可用内存(MB)',
  `runtime_hours` int NULL COMMENT '运行时长(小时)',
  `runtime_minutes` int NULL COMMENT '运行时长(分钟)',
  `runtime_seconds` int NULL COMMENT '运行时长(秒)',
  `runtime_total` varchar(20) NULL COMMENT '运行时间字符串',
  `abnormal_restart_count` int DEFAULT 0 COMMENT '异常重启次数',
  `aging_test_count` int DEFAULT 0 COMMENT '老化测试次数',
  
  `parsed_at` datetime NULL COMMENT '解析时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_file_sync_id` (`file_sync_id`),
  KEY `idx_product_sn` (`product`, `sn`),
  KEY `idx_test_datetime` (`test_datetime`),
  KEY `idx_runtime_hours` (`runtime_hours`),
  KEY `idx_cpu_temp` (`cpu_temp`),
  KEY `idx_abnormal_restart` (`abnormal_restart_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='老化测试结果表';
```

#### 2.1.2 厂测结果表 (bi_factory_test_result)
```sql
CREATE TABLE `bi_factory_test_result` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `file_sync_id` bigint unsigned NOT NULL COMMENT '关联文件表ID',
  `product` varchar(64) NOT NULL COMMENT '产品名称',
  `sn` varchar(64) NOT NULL COMMENT '序列号',
  `test_datetime` varchar(32) NOT NULL COMMENT '测试日期时间',
  
  -- 厂测特有字段
  `device_name` varchar(128) NULL COMMENT '设备名称',
  `cpu_id` varchar(64) NULL COMMENT 'CPU ID',
  `factory_version` varchar(64) NULL COMMENT '厂测版本',
  `firmware_version` varchar(255) NULL COMMENT '固件版本',
  `memory_capacity` varchar(32) NULL COMMENT '内存容量',
  `flash_capacity` varchar(32) NULL COMMENT '闪存容量',
  `test_result` varchar(20) NULL COMMENT '测试结果(成功/失败)',
  `success_count` int DEFAULT 0 COMMENT '成功项目数',
  `fail_count` int DEFAULT 0 COMMENT '失败项目数',
  `success_items` text NULL COMMENT '成功项目列表(JSON)',
  `fail_items` text NULL COMMENT '失败项目列表(JSON)',
  
  -- 各测试项状态（1=成功，0=失败，NULL=未测试）
  `test_memory` tinyint NULL COMMENT '内存测试',
  `test_time` tinyint NULL COMMENT '时间测试',
  `test_hdmi_edid` tinyint NULL COMMENT 'HDMI_EDID测试',
  `test_device_chk` tinyint NULL COMMENT '设备检查',
  `test_serial_port` tinyint NULL COMMENT '串口测试',
  `test_spi_flash` tinyint NULL COMMENT 'SPI闪存测试',
  `test_pcie` tinyint NULL COMMENT 'PCIE测试',
  `test_hw_version` tinyint NULL COMMENT '硬件版本测试',
  `test_usb` tinyint NULL COMMENT 'USB测试',
  `test_bluetooth` tinyint NULL COMMENT '蓝牙测试',
  `test_led` tinyint NULL COMMENT 'LED测试',
  `test_simcard` tinyint NULL COMMENT 'SIM卡测试',
  `test_sata` tinyint NULL COMMENT 'SATA测试',
  
  `parsed_at` datetime NULL COMMENT '解析时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_file_sync_id` (`file_sync_id`),
  KEY `idx_product_sn` (`product`, `sn`),
  KEY `idx_test_datetime` (`test_datetime`),
  KEY `idx_test_result` (`test_result`),
  KEY `idx_device_name` (`device_name`),
  KEY `idx_firmware_version` (`firmware_version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='厂测结果表';
```

#### 2.1.3 扩展 bi_file_sync 表
```sql
ALTER TABLE `bi_file_sync` 
ADD COLUMN `file_type` varchar(32) NULL COMMENT '文件类型(aging_test/factory_test/other)' AFTER `test_type`,
ADD COLUMN `is_parsed` tinyint DEFAULT 0 COMMENT '是否已解析(0-未解析,1-已解析,2-解析失败)' AFTER `sync_status`,
ADD COLUMN `parsed_at` datetime NULL COMMENT '解析时间' AFTER `is_parsed`,
ADD INDEX `idx_file_type` (`file_type`),
ADD INDEX `idx_is_parsed` (`is_parsed`);
```

#### 2.1.4 创建Model文件

// app/Model/TestFile/AgingTestResultModel.php

// app/Model/TestFile/FactoryTestResultModel.php


## 三、ReorganizeService 扩展方案

### 3.1 修改 processFile 方法
```php
protected function processFile(string $filePath, string $sourceBase, string $targetBase): bool
{
    // ... 现有代码 ...
    
    // 写入数据库
    $record = new FileSyncModel();
    // ... 设置字段 ...
    $record->save();
    
    // 解析测试文件内容（传递 fileSyncId）
    $this->parseTestFile($targetPath, $pathInfo, $record->id);
    
    // ... 其余代码 ...
}
```

### 3.2 修改 parseTestFile 方法
```php
protected function parseTestFile(string $filePath, array $pathInfo, int $fileSyncId = null): void
{
    // ... 现有代码 ...
    
    // 根据文件名解析具体内容
    switch ($filename) {
        case 'agingTest_result.txt':
            // 解析老化测试结果（传递 fileSyncId）
            $this->parseAgingTestResult($filePath, $testRecord->id, $fileSyncId);
            break;
            
        case 'factoryTest_result.txt':
            // 解析厂测结果（传递 fileSyncId）
            $this->parseFactoryTestResult($filePath, $testRecord->id, $fileSyncId);
            break;
            
        // ... 其他文件类型 ...
    }
}
```

### 3.3 扩展解析方法
```php
use App\Model\TestFile\AgingTestResultModel;
use App\Model\TestFile\FactoryTestResultModel;

/**
 * 解析老化测试结果（扩展版）
 */
protected function parseAgingTestResult(string $filePath, int $testRecordId, int $fileSyncId = null): void
{
    $content = file_get_contents($filePath);
    $data = [];
    
    // 解析SOC信息
    if (preg_match('/SOC:(\w+),核心数:(\d+)/', $content, $matches)) {
        $data['soc_type'] = $matches[1];
        $data['cpu_cores'] = (int)$matches[2];
    }
    
    // 解析CPU频率
    if (preg_match('/CPU0：(\d+)，CPU4：(\d+)，CPU6：(\d+)/', $content, $matches)) {
        $data['cpu0_freq'] = (int)$matches[1];
        $data['cpu4_freq'] = (int)$matches[2];
        $data['cpu6_freq'] = (int)$matches[3];
    }
    
    // 解析GPU/NPU/DDR频率
    if (preg_match('/GPU：(\d+)，NPU：(\d+)，DDR：(\d+)/', $content, $matches)) {
        $data['gpu_freq'] = (int)$matches[1];
        $data['npu_freq'] = (int)$matches[2];
        $data['ddr_freq'] = (int)$matches[3];
    }
    
    // 解析温度
    if (preg_match('/CPU 温度:\s*\*?(\d+\.?\d*)\s*°C\*?.*GPU 温度:\s*\*?(\d+\.?\d*)\s*°C\*?/', $content, $matches)) {
        $data['cpu_temp'] = (float)$matches[1];
        $data['gpu_temp'] = (float)$matches[2];
    }
    
    // 解析NPU负载
    if (preg_match('/NPU 负载：\s*Core0：\s*(\d+)%，\s*Core1：\s*(\d+)%/', $content, $matches)) {
        $data['npu_load_core0'] = (float)$matches[1];
        $data['npu_load_core1'] = (float)$matches[2];
    }
    
    // 解析内存信息
    if (preg_match('/运行内存:(\d+)\s*MB,可用内存：(\d+)\s*MB/', $content, $matches)) {
        $data['total_memory'] = (int)$matches[1];
        $data['available_memory'] = (int)$matches[2];
    }
    
    // 解析运行时间
    if (preg_match('/运行时间:(\d+):(\d+):(\d+)/', $content, $matches)) {
        $data['runtime_hours'] = (int)$matches[1];
        $data['runtime_minutes'] = (int)$matches[2];
        $data['runtime_seconds'] = (int)$matches[3];
        $data['runtime_total'] = $matches[0];
    }
    
    // 解析异常重启和老化测试次数
    if (preg_match('/异常重启次数:(\d+)/', $content, $matches)) {
        $data['abnormal_restart_count'] = (int)$matches[1];
    }
    if (preg_match('/老化测试次数:(\d+)/', $content, $matches)) {
        $data['aging_test_count'] = (int)$matches[1];
    }
    
    // 保存到关联表
    if ($fileSyncId && !empty($data)) {
        $fileSync = FileSyncModel::find($fileSyncId);
        if ($fileSync) {
            // 检查是否已存在解析记录
            $existing = AgingTestResultModel::where('file_sync_id', $fileSyncId)->first();
            if (!$existing) {
                $agingResult = new AgingTestResultModel();
                $agingResult->file_sync_id = $fileSyncId;
                $agingResult->product = $fileSync->product;
                $agingResult->sn = $fileSync->sn;
                $agingResult->test_datetime = $fileSync->test_datetime;
                $agingResult->fill($data);
                $agingResult->parsed_at = Carbon::now();
                $agingResult->save();
            }
            
            // 更新文件解析状态
            $fileSync->file_type = 'aging_test';
            $fileSync->is_parsed = 1;
            $fileSync->parsed_at = Carbon::now();
            $fileSync->save();
        }
    }
    
    // 更新测试记录
    TestRecordModel::where('id', $testRecordId)->update([
        'aging_test_result' => '完成'
    ]);
}

/**
 * 解析厂测结果（扩展版）
 */
protected function parseFactoryTestResult(string $filePath, int $testRecordId, int $fileSyncId = null): void
{
    $content = file_get_contents($filePath);
    $data = [];
    
    // 解析设备信息
    if (preg_match('/设备名:(.*?)\n/', $content, $matches)) {
        $data['device_name'] = trim($matches[1]);
    }
    
    if (preg_match('/CPU ID：(.*?)\n/', $content, $matches)) {
        $data['cpu_id'] = trim($matches[1]);
    }
    
    if (preg_match('/厂测版本:(.*?)\n/', $content, $matches)) {
        $data['factory_version'] = trim($matches[1]);
    }
    
    if (preg_match('/固件版本：(.*?)\n/', $content, $matches)) {
        $data['firmware_version'] = trim($matches[1]);
    }
    
    // 解析容量信息
    if (preg_match('/内存容量：(.*?)\n/', $content, $matches)) {
        $data['memory_capacity'] = trim($matches[1]);
    }
    
    if (preg_match('/闪存：(.*?)\n/', $content, $matches)) {
        $data['flash_capacity'] = trim($matches[1]);
    }
    
    // 解析测试结果
    if (preg_match('/厂测结果:(成功|失败)/', $content, $matches)) {
        $data['test_result'] = $matches[1];
    }
    
    // 解析成功和失败项目
    if (preg_match('/成功\s*项目:(\d+)\s*\[\s*([^\]]+)\s*\]/', $content, $matches)) {
        $data['success_count'] = (int)$matches[1];
        $successItems = preg_split('/\s+/', trim($matches[2]));
        $data['success_items'] = json_encode($successItems);
        
        // 解析各测试项状态
        foreach ($successItems as $item) {
            $columnName = $this->getTestColumnName($item);
            if ($columnName) {
                $data[$columnName] = 1;
            }
        }
    }
    
    if (preg_match('/失败项目：(\d+)\s*\[\s*([^\]]+)\s*\]/', $content, $matches)) {
        $data['fail_count'] = (int)$matches[1];
        $failItems = preg_split('/\s+/', trim($matches[2]));
        $data['fail_items'] = json_encode($failItems);
        
        // 解析各测试项状态
        foreach ($failItems as $item) {
            $columnName = $this->getTestColumnName($item);
            if ($columnName) {
                $data[$columnName] = 0;
            }
        }
    }
    
    // 保存到关联表
    if ($fileSyncId && !empty($data)) {
        $fileSync = FileSyncModel::find($fileSyncId);
        if ($fileSync) {
            // 检查是否已存在解析记录
            $existing = FactoryTestResultModel::where('file_sync_id', $fileSyncId)->first();
            if (!$existing) {
                $factoryResult = new FactoryTestResultModel();
                $factoryResult->file_sync_id = $fileSyncId;
                $factoryResult->product = $fileSync->product;
                $factoryResult->sn = $fileSync->sn;
                $factoryResult->test_datetime = $fileSync->test_datetime;
                $factoryResult->fill($data);
                $factoryResult->parsed_at = Carbon::now();
                $factoryResult->save();
            }
            
            // 更新文件解析状态
            $fileSync->file_type = 'factory_test';
            $fileSync->is_parsed = 1;
            $fileSync->parsed_at = Carbon::now();
            $fileSync->save();
        }
    }
    
    // 更新测试记录
    $result = $data['test_result'] ?? '未知';
    TestRecordModel::where('id', $testRecordId)->update([
        'factory_test_result' => $result
    ]);
}

/**
 * 获取测试项对应的数据库字段名
 */
protected function getTestColumnName(string $testItem): ?string
{
    $mapping = [
        'MEMORY' => 'test_memory',
        'TIME' => 'test_time',
        'HDMI_EDID' => 'test_hdmi_edid',
        'DEVICE_CHK' => 'test_device_chk',
        'SERIAL_PORT' => 'test_serial_port',
        'SPI_FLASH' => 'test_spi_flash',
        'PCIE' => 'test_pcie',
        'HW_VERSION' => 'test_hw_version',
        'USB' => 'test_usb',
        'BLUETOOTH' => 'test_bluetooth',
        'LED' => 'test_led',
        'SIMCARD' => 'test_simcard',
        'SATA' => 'test_sata'
    ];
    
    return $mapping[$testItem] ?? null;
}
```

## 四、实施计划

### 4.1 第一阶段：数据库扩展
1. 创建数据库迁移文件
2. 执行迁移，创建新表和字段
3. 创建对应的Model文件
4. 添加必要的索引

### 4.2 第二阶段：修改 ReorganizeService
1. 修改 `processFile` 方法，传递 fileSyncId
2. 修改 `parseTestFile` 方法，传递 fileSyncId
3. 扩展 `parseAgingTestResult` 方法
4. 扩展 `parseFactoryTestResult` 方法
5. 添加 `getTestColumnName` 辅助方法

### 4.3 第三阶段：前端筛选功能
1. 扩展筛选表单组件
2. 实现文件类型切换逻辑
3. 添加老化测试特有筛选条件
4. 添加厂测特有筛选条件
5. 优化查询结果展示

### 4.4 第四阶段：后端查询优化
1. 扩展 FileManagerService 的查询方法
2. 实现联合查询逻辑
3. 优化查询性能


## 五、性能优化

### 5.1 索引优化
- 所有筛选字段都已添加索引
- 使用复合索引优化联合查询

### 5.2 查询优化
- 使用分页限制单次查询数据量
- 实现懒加载机制

### 5.3 解析优化
- 在文件重排时同步解析，避免重复读取文件
- 使用事务确保数据一致性
- 添加解析失败重试机制

## 六、扩展性设计

### 6.1 支持新的测试类型
- 表结构预留扩展字段
- 解析方法采用策略模式
- 筛选条件可动态配置



## 总结

本方案通过在现有的 `ReorganizeService` 中集成文件解析功能，避免了异步队列的复杂性，实现了文件处理和内容解析的一体化。配合新建的关联表和扩展的查询功能，可以满足对测试文件内容的精细化筛选需求。

### 主要优势：
1. **实现简单**：基于现有代码扩展，改动最小
2. **实时解析**：文件处理时立即解析，数据及时性好
3. **维护方便**：所有逻辑集中在一个服务中，便于维护

### 注意事项：
1. 正则表达式需要充分测试，确保兼容各种格式
3. 解析失败时的异常处理要完善
