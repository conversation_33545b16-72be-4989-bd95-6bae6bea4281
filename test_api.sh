#!/bin/bash

# 测试后端 API 接口

echo "========================================="
echo "测试文件管理 API 接口"
echo "========================================="
echo ""

# API 基础 URL
BASE_URL="http://localhost:9501/api/testfile"

# 测试获取树形数据（根节点）
echo "1. 测试获取根节点数据（产品列表）"
echo "   请求: GET /tree?node=&page=1&limit=10&sort=product&order=DESC"
curl -s -X GET "${BASE_URL}/tree?node=&page=1&limit=10&sort=product&order=DESC" | python3 -m json.tool
echo ""
echo "-----------------------------------------"

# 测试带过滤条件的请求
echo "2. 测试带过滤条件的请求"
echo "   请求: GET /tree?node=&filter[product]=AIO&op[product]=LIKE"
curl -s -X GET "${BASE_URL}/tree?node=&filter[product]=AIO&op[product]=LIKE" | python3 -m json.tool
echo ""
echo "-----------------------------------------"

# 测试获取SN列表
echo "3. 测试获取SN列表（需要先有产品数据）"
echo "   请求: GET /tree?node=AIO-3576-JD4&page=1&limit=5"
curl -s -X GET "${BASE_URL}/tree?node=AIO-3576-JD4&page=1&limit=5" | python3 -m json.tool
echo ""
echo "-----------------------------------------"

# 测试日期范围过滤
echo "4. 测试日期范围过滤"
echo "   请求: GET /tree?node=AIO-3576-JD4/SN001&filter[start_date]=2024-01-01&filter[end_date]=2024-12-31"
curl -s -X GET "${BASE_URL}/tree?node=AIO-3576-JD4/SN001&filter[start_date]=2024-01-01&filter[end_date]=2024-12-31" | python3 -m json.tool
echo ""
echo "-----------------------------------------"

echo ""
echo "测试完成！"
